# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

AI投资洞察平台 - 基于 Nx Monorepo 的投资研究平台，包含 Next.js 前端和 FastAPI 后端。

## 常用开发命令

### 项目管理
```bash
# 在项目根目录安装所有依赖
pnpm install

# 查看所有项目
pnpm nx show projects

# 查看项目依赖图
pnpm nx graph
```

### 开发服务器
```bash
# 前端开发 (http://localhost:4200)
pnpm nx dev web-app

# 后端开发 (http://localhost:8000)
pnpm nx dev api-server

# 同时启动前后端
pnpm nx dev:all
```

### 构建与测试
```bash
# 构建所有项目
pnpm nx run-many -t build

# 构建共享库 (开发前必须执行)
pnpm nx run-many --target=build --projects=shared-types,api-client

# 运行测试
pnpm nx run-many -t test

# 代码检查
pnpm nx run-many -t lint

# 类型检查
pnpm nx run-many -t type-check

# 单个项目类型检查
pnpm nx type-check research-v2-fe
```

### 增量构建 (推荐)
```bash
# 只构建受影响的项目
pnpm nx affected -t build

# 只测试受影响的项目
pnpm nx affected -t test

# 基于特定分支的影响分析
pnpm nx affected -t build --base=main
```

### Python 后端专用命令

在 `apps/api-server` 目录下：

```bash
# 激活虚拟环境
source .venv/bin/activate

# 安装 Python 依赖
pip install -e .[dev]

# 运行后端服务器
python main.py

# Python 代码格式化和检查
ruff format .
ruff check .
mypy .

# 运行 Python 测试
pytest
```

## 项目架构

### Monorepo 结构
```
yai-investor-insight/
├── apps/
│   ├── web-app/           # Next.js 15 前端应用
│   └── api-server/        # FastAPI Python 后端
├── libs/
│   ├── shared-types/      # 共享 TypeScript 类型定义
│   └── api-client/        # API 客户端库
└── tools/                 # 构建工具和脚本
```

### 技术栈
- **前端**: Next.js 15 + TypeScript + Tailwind CSS
- **后端**: FastAPI + Python 3.11 + LangGraph
- **构建系统**: Nx Monorepo + pnpm
- **AI 技术栈**: LangChain + OpenAI/Anthropic
- **数据库**: PostgreSQL (通过 TortoiseORM)

### 代码架构模式

#### 后端架构 (`apps/api-server/src/`)
- **洋葱架构 (Onion Architecture)**:
  - `domain/`: 领域层 - 业务逻辑核心
  - `application/`: 应用层 - 用例和服务编排 
  - `infrastructure/`: 基础设施层 - 外部依赖
  - `api/`: 接口层 - REST API 控制器

#### 前端架构 (`apps/web-app/src/`)
- **特性驱动开发**:
  - `app/`: Next.js App Router 页面
  - `components/`: 可复用组件
  - `hooks/`: 自定义 React Hooks
  - `lib/`: 工具函数和配置
  - `store/`: Zustand 状态管理

## 开发规范

### 分支命名
- `feature/<description>`: 新功能
- `fix/<description>`: Bug 修复
- `chore/<description>`: 杂项任务

### 提交规范
采用 Conventional Commits：
- `feat(scope): description`
- `fix(scope): description` 
- `docs(scope): description`

### API 设计
- 端点使用 `kebab-case`
- JSON 字段使用 `snake_case`
- 示例: `GET /api/v1/market-data`

## 重要注意事项

1. **开发前必须构建共享库**: `pnpm nx run-many --target=build --projects=shared-types,api-client`

2. **Python 虚拟环境**: 后端使用 `.venv` 目录，需要先激活环境

3. **Monorepo 操作**: 始终注意当前工作目录，避免在错误位置执行命令

4. **依赖管理**: 前端依赖通过根目录 pnpm 管理，后端 Python 依赖独立管理

5. **代码架构原则**:
   - Python/TypeScript 文件不超过 200 行
   - 每层文件夹不超过 8 个文件
   - 优先编辑现有文件而非创建新文件
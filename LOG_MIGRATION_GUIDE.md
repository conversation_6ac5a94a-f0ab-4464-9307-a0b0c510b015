# 日志模块迁移指南

## 从 web-app 本地日志器迁移到 shared-fe-core 统一日志模块

### 1. 添加依赖

在 `apps/web-app/package.json` 中添加：

```json
{
  "dependencies": {
    "@yai-investor-insight/shared-fe-core": "workspace:*"
  }
}
```

### 2. 迁移现有日志调用

**之前的用法：**
```typescript
// apps/web-app/src/lib/logger.ts
import { logger, ContextLogger } from '../lib/logger';

await logger.info('用户登录', { userId: '123' });
```

**迁移后的用法：**
```typescript
// 从 shared-fe-core 导入
import { logger, ContextLogger } from '@yai-investor-insight/shared-fe-core';

await logger.info('用户登录', { userId: '123' });
```

### 3. 迁移服务端日志

**之前的用法：**
```typescript  
// apps/web-app/src/lib/server-logger.ts
import { serverLogger } from '../lib/server-logger';

await serverLogger.info('API 请求处理', { path: '/api/users' });
```

**迁移后的用法：**
```typescript
// 从 shared-fe-core 导入
import { serverLogger } from '@yai-investor-insight/shared-fe-core';

await serverLogger.info('API 请求处理', { path: '/api/users' });
```

### 4. 迁移工具函数

**之前的用法：**
```typescript
import { 
  generateTraceId, 
  logApiRequest, 
  logUserAction 
} from '../lib/logger';
```

**迁移后的用法：**
```typescript
import { 
  generateTraceId, 
  logApiRequest, 
  logUserAction 
} from '@yai-investor-insight/shared-fe-core';
```

### 5. 类型导入

**之前的用法：**
```typescript
import type { LogContext } from '../types/logging-config';
```

**迁移后的用法：**
```typescript
import type { LogContext } from '@yai-investor-insight/shared-fe-core';
```

### 6. 配置保持不变

环境变量和配置保持不变，统一日志模块会自动读取相同的环境变量：

- `NEXT_PUBLIC_SLS_ENDPOINT`
- `NEXT_PUBLIC_SLS_ACCESS_KEY_ID`
- `NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET`
- `NEXT_PUBLIC_SLS_PROJECT`
- `NEXT_PUBLIC_SLS_LOGSTORE`
- `NEXT_PUBLIC_SLS_ENABLED`

### 7. 删除旧文件

迁移完成后，可以删除以下文件：
- `apps/web-app/src/lib/logger.ts`
- `apps/web-app/src/lib/server-logger.ts`  
- `apps/web-app/src/types/logging-config.ts`

### 8. 构建共享库

在使用前需要构建共享库：

```bash
pnpm nx build shared-fe-core
```

### 示例：完整的迁移示例

**迁移前的文件 (`apps/web-app/src/hooks/useAuth.ts`)：**
```typescript
import { logger } from '../lib/logger';

export function useAuth() {
  const login = async (credentials: any) => {
    await logger.info('用户尝试登录', { email: credentials.email });
    // 登录逻辑...
  };
}
```

**迁移后的文件：**
```typescript
import { logger } from '@yai-investor-insight/shared-fe-core';

export function useAuth() {
  const login = async (credentials: any) => {
    await logger.info('用户尝试登录', { email: credentials.email });
    // 登录逻辑...
  };
}
```

### 优势

1. **统一管理**: 所有前端项目共享同一套日志实现
2. **版本一致**: 避免不同项目使用不同版本的日志库
3. **配置统一**: 统一的配置和环境变量处理
4. **维护简化**: 只需在一个地方更新日志逻辑
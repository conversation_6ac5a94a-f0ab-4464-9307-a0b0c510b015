{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "bundler", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@yai-investor-insight/demo-feature-fe": ["libs/demo-feature-fe/src/index.ts"], "@yai-investor-insight/research-v2-fe": ["libs/research-v2-fe/src/index.ts"], "@yai-investor-insight/research-v2b-fe": ["libs/research-v2b-fe/src/index.ts"], "@yai-investor-insight/research-v2h-fe": ["libs/research-v2h-fe/src/index.ts"], "@yai-investor-insight/shared-fe-core": ["libs/shared-fe-core/src/index.ts"], "@yai-investor-insight/shared-fe-core/server": ["libs/shared-fe-core/src/server.ts"], "@yai-investor-insight/shared-fe-kit": ["libs/shared-fe-kit/src/index.ts"], "@yai-investor-insight/shared-types": ["libs/shared-fe-types/src/index.ts"], "@yai-investor-insight/user-account-fe": ["libs/user-account-fe/src/index.ts"], "@yai-investor-insight/workbench-v1a-fe": ["libs/workbench-v1a-fe/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}
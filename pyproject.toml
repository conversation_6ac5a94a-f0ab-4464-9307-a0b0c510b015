# YAI Investor Insight - Python 工作区配置
# 此文件用于统一管理 Python 工作区的配置和开发工具

[tool.uv.workspace]
members = [
    "apps/api-server",
    "libs/shared-bs-core",
    "libs/shared-bs-llm",
    "libs/demo-feature-bs",
    "libs/research-v2-bs",
    "libs/research-v2b-bs",
    "libs/research-v2h-bs"
]

# Ruff - Python 代码格式化和 linting 工具
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "N",   # pep8-naming
    "B",   # flake8-bugbear
    "UP",  # pyupgrade
]
ignore = [
    "E501",  # line too long (handled by line-length)
    "B008",  # do not perform function calls in argument defaults
]
fixable = ["ALL"]

[tool.ruff.isort]
known-first-party = [
    "shared_bs_core",
    "shared_bs_llm",
    "demo_feature_bs",
    "research_v2_bs",
    "research_v2b_bs",
    "research_v2h_bs"
]
section-order = [
    "future",
    "standard-library", 
    "third-party",
    "first-party",
    "local-folder"
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

# MyPy - 静态类型检查
[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
ignore_missing_imports = true
show_error_codes = true
pretty = true

# 排除一些第三方库的类型检查
[[tool.mypy.overrides]]
module = [
    "uvicorn.*",
    "fastapi.*",
    "pydantic.*",
]
ignore_missing_imports = true

# Pytest - 测试配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "-q", 
    "--strict-markers",
    "--strict-config",
    "--disable-warnings"
]
testpaths = [
    "apps/api-server/tests",
    "libs/shared-bs-core/tests",
    "libs/demo-feature-bs/tests",
    "libs/shared-bs-llm/tests",
    "libs/research-v2-bs/tests",
    "libs/research-v2b-bs/tests",
    "libs/research-v2h-bs/tests"
]
asyncio_mode = "auto"
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

# Coverage - 代码覆盖率配置
[tool.coverage.run]
source = [
    "apps/api-server/src",
    "libs/shared-bs-core/src",
    "libs/demo-feature-bs/src",
    "libs/shared-bs-llm/src",
    "libs/research-v2-bs/src",
    "libs/research-v2b-bs/src",
    "libs/research-v2h-bs/src"
]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]
show_missing = true
precision = 2

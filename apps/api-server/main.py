"""主应用入口 - API 服务器壳工程"""
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
import os
import uuid
import time
from dotenv import load_dotenv

from src.core.settings import settings
from shared_bs_core.logger import configure_logging, get_logger

# 加载环境变量
load_dotenv()

# 配置日志
configure_logging(service_name="api-server")
logger = get_logger(__name__, service_name="api-server")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("API 服务器启动中...")

    logger.info("API 服务器启动完成")

    yield

    logger.info("API 服务器关闭完成")

# 创建 FastAPI 应用
app = FastAPI(
    title=settings.app_name,
    description="AI-powered investment research platform backend",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("ENVIRONMENT") == "development" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") == "development" else None
)

# 请求日志中间件 - 记录请求信息和性能指标
@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """请求日志中间件 - 记录请求信息和性能指标"""
    request_id = str(uuid.uuid4())
    start_time = time.time()

    # 记录请求开始
    logger.info("请求开始", extra={
        "request_id": request_id,
        "method": request.method,
        "url": str(request.url),
        "client_ip": request.client.host,
        "user_agent": request.headers.get("user-agent", "")
    })

    # 记录 DEBUG 级别的详细信息（会上报到 SLS）
    logger.debug("请求详细信息", extra={
        "request_id": request_id,
        "headers": dict(request.headers),
        "query_params": dict(request.query_params)
    })

    # 处理请求
    response = await call_next(request)

    # 计算处理时间
    process_time = time.time() - start_time

    # 记录请求结束
    logger.info("请求结束", extra={
        "request_id": request_id,
        "status_code": response.status_code,
        "process_time": f"{process_time:.3f}s"
    })

    # 记录性能指标（DEBUG 级别）
    logger.debug("请求性能指标", extra={
        "request_id": request_id,
        "process_time_ms": round(process_time * 1000, 2),
        "response_size": response.headers.get("content-length", "unknown")
    })

    response.headers["X-Request-ID"] = request_id
    response.headers["X-Process-Time"] = f"{process_time:.3f}"
    return response

# CORS 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:4200",  # 添加前端开发服务器端口
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",
        "http://127.0.0.1:4200"   # 添加前端开发服务器端口
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*", "X-Request-ID", "X-Trace-ID"],  # 添加 trace_id 相关头
)

# 路由配置
from src.api.v1 import v1_router

app.include_router(v1_router, prefix="/api")



# 插件路由注册
try:
    from demo_feature_bs import router as demo_feature_router
    app.include_router(demo_feature_router)
    logger.info("Demo feature plugin registered successfully")
except Exception as e:
    logger.warning(f"Demo feature plugin not found or failed to import: {e}")

try:
    from research_v2b_bs import router as research_v2b_router
    app.include_router(research_v2b_router)
    logger.info("Research V2B plugin registered successfully")
except Exception as e:
    logger.warning(f"Research V2B plugin not found or failed to import: {e}")

try:
    from research_v2h_bs import router as research_v2h_router
    app.include_router(research_v2h_router)
    logger.info("Research V2H plugin registered successfully")
except Exception as e:
    logger.warning(f"Research V2H plugin not found or failed to import: {e}")



@app.get("/", tags=["root"])
async def root():
    """根路径"""
    logger.info("处理根路径请求")
    logger.debug("根路径调试信息 - 这条日志会上报到 SLS")

    return {
        "message": "Welcome to the Investor Insight API",
        "version": "1.0.0",
        "docs": "/docs" if os.getenv("ENVIRONMENT") == "development" else "disabled",
        "health": "/api/v1/health",
        "logging_test": "/test-logging"
    }

@app.get("/test-logging", tags=["debug"])
async def test_logging():
    """测试各种日志级别"""
    logger.debug("这是 DEBUG 级别日志 - 会上报到 SLS")
    logger.info("这是 INFO 级别日志")
    logger.warning("这是 WARNING 级别日志")
    logger.error("这是 ERROR 级别日志")

    # 结构化日志测试
    logger.info("结构化日志测试", extra={
        "user_id": 12345,
        "action": "test_logging",
        "metadata": {"key1": "value1", "key2": "value2"},
        "timestamp": time.time()
    })

    # 业务流程日志（PackId 自动关联）
    order_id = f"TEST-{int(time.time())}"
    logger.info("开始处理测试订单", extra={"order_id": order_id})
    logger.debug("验证订单数据", extra={"order_id": order_id, "step": "validation"})
    logger.info("订单验证通过", extra={"order_id": order_id, "step": "validation"})
    logger.debug("处理支付", extra={"order_id": order_id, "step": "payment", "amount": 99.99})
    logger.info("支付处理完成", extra={"order_id": order_id, "step": "payment", "status": "success"})
    logger.info("订单处理完成", extra={"order_id": order_id, "final_status": "completed"})

    return {
        "message": "日志测试完成，请检查 SLS 控制台",
        "order_id": order_id,
        "sls_project": "yai-log-test",
        "sls_logstore": "app-log",
        "note": "相关日志已通过 PackId 自动关联"
    }


# 启动命令：uvicorn main:app --host 0.0.0.0 --port 8000 --reload
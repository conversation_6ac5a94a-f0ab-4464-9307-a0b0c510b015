"""健康检查API端点"""
from fastapi import APIRouter
from datetime import datetime, timezone
from ....core.settings import settings
from shared_bs_core.logger import get_logger

router = APIRouter(prefix="/health", tags=["health"])
logger = get_logger(__name__, service_name="api-server")


@router.get("/")
async def health_check():
    """基础健康检查"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "api-server"
    }


@router.get("/ready")
async def readiness_check():
    """就绪检查"""
    return {
        "ready": True,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }


@router.get("/live")
async def liveness_check():
    """存活检查"""
    return {
        "alive": True,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
"""API v1路由模块"""
from fastapi import APIRouter
from .endpoints.health import router as health_router

try:
    from research_v2b_bs import router as research_v2b_router
except ImportError:
    # 如果插件未安装，创建空路由
    research_v2b_router = APIRouter(prefix="/research-v2b", tags=["research-v2b"])
    
    @research_v2b_router.get("/health")
    async def research_v2b_placeholder():
        return {"status": "plugin_not_installed", "message": "Research V2B plugin not available"}

try:
    from research_v2h_bs import router as research_v2h_router
except ImportError:
    # 如果插件未安装，创建空路由
    research_v2h_router = APIRouter(prefix="/research-v2h", tags=["research-v2h"])
    
    @research_v2h_router.get("/health")
    async def research_v2h_placeholder():
        return {"status": "plugin_not_installed", "message": "Research V2H plugin not available"}

# 创建v1路由器，设置前缀
v1_router = APIRouter(prefix="/v1")

# 注册健康检查路由
v1_router.include_router(health_router)

# 注册插件路由
v1_router.include_router(research_v2b_router)
v1_router.include_router(research_v2h_router)

__all__ = ["v1_router"]
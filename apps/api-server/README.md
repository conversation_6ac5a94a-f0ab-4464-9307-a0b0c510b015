# API Server - 壳工程

## 概述

这是一个轻量级的 API 服务器壳工程，遵循核心设计原则：**专注于日志规范定义和依赖包加载**。

## 核心设计原则

1. **壳工程架构**：api-server 作为应用框架的壳，不包含具体业务逻辑
2. **插件化加载**：通过 libs 加载具体的业务功能模块
3. **统一日志规范**：提供标准化的日志配置和管理
4. **依赖管理**：管理核心依赖，业务依赖通过插件引入

## 目录结构

```
apps/api-server/
├── main.py                    # 主入口文件
├── pyproject.toml            # 项目配置和依赖管理
├── config/
│   └── README.md             # 配置说明
├── src/
│   ├── api/
│   │   └── v1/
│   │       ├── __init__.py   # API 路由注册
│   │       └── endpoints/
│   │           └── health.py # 健康检查端点
│   ├── core/
│   │   └── settings.py       # 应用配置
│   └── infrastructure/
│       └── services/         # 基础设施服务（向后兼容）
└── tests/                    # 测试目录
```

## 核心功能

### 1. 日志规范
- 使用 `shared-bs-core` 统一日志管理
- 基于 `yai-loguru-sinks` 的企业级日志组件
- 支持控制台、文件、SLS 多重输出
- 结构化日志和 PackId 自动关联
- 请求日志中间件，记录性能指标

### 2. 插件加载
- 自动发现和加载 libs 中的业务模块
- 支持插件路由注册
- 优雅的错误处理和降级

### 3. 基础设施
- FastAPI 框架
- CORS 中间件配置
- 健康检查端点
- 环境配置管理

## 插件注册

当前支持的插件：
- `demo-feature-bs`: 演示功能插件
- `research-v2b-bs`: 研究功能 V2B 插件
- `research-v2h-bs`: 研究功能 V2H 插件

插件会自动注册路由，如果插件不可用会优雅降级。

## 启动方式

```bash
# 开发环境
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 生产环境
uvicorn main:app --host 0.0.0.0 --port 8000
```

## API 端点

- `GET /`: 根路径，返回 API 信息
- `GET /api/v1/health`: 健康检查
- `GET /api/v1/health/ready`: 就绪检查
- `GET /api/v1/health/live`: 存活检查
- `GET /test-logging`: 日志测试端点
- `GET /docs`: API 文档（开发环境）

## 环境变量

主要环境变量：
- `ENVIRONMENT`: 运行环境 (development/production)
- `LOG_LEVEL`: 日志级别
- `SLS_*`: SLS 日志服务配置

## 依赖管理

核心依赖：
- `fastapi`: Web 框架
- `uvicorn`: ASGI 服务器
- `pydantic`: 数据验证
- `shared-bs-core`: 后端核心工具库（包含日志、数据库等）
- `python-dotenv`: 环境变量管理

业务依赖通过插件模块引入，保持壳工程的轻量化。

## 开发指南

1. **添加新插件**：在 `main.py` 中添加插件导入和注册逻辑
2. **修改日志配置**：编辑 `logging.yaml` 文件
3. **更新核心配置**：修改 `src/core/settings.py`
4. **添加中间件**：在 `main.py` 中注册新的中间件

## 注意事项

- 这是一个壳工程，不应包含具体业务逻辑
- 所有业务功能应通过插件模块实现
- 保持依赖的最小化，避免引入不必要的包
- 日志配置应统一管理，确保规范一致性

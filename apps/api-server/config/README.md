# 配置文件说明

## 目录结构

```
config/
├── logging.yaml          # 日志配置文件
└── README.md             # 本说明文件
```

## 日志配置 (logging.yaml)

### 配置说明

- **控制台输出**: 显示 INFO 及以上级别的日志，支持颜色输出
- **文件输出**: 记录所有 DEBUG 级别日志到 `../../logs/api-server.log`
- **SLS 输出**: 可选的阿里云 SLS 日志收集，需要配置环境变量

### 环境变量

日志配置支持以下环境变量：

- `LOG_LEVEL`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `ENVIRONMENT`: 运行环境 (development, production)
- `SLS_ENABLED`: 是否启用 SLS 输出 (true/false)
- `SLS_ENDPOINT`: SLS 服务端点
- `SLS_ACCESS_KEY_ID`: SLS 访问密钥 ID
- `SLS_ACCESS_KEY_SECRET`: SLS 访问密钥

### 自定义配置

如果需要自定义日志配置，可以：

1. 修改 `logging.yaml` 文件
2. 或者删除该文件，系统将使用默认配置

### 日志输出位置

- **控制台**: 直接输出到标准输出
- **文件**: `../../logs/api-server.log` (相对于项目根目录)
- **SLS**: 阿里云日志服务 (如果启用)

## 配置原则

作为壳工程，配置文件应该：

1. **简洁明了**: 只包含必要的配置项
2. **环境适配**: 支持开发和生产环境
3. **可扩展**: 便于插件模块扩展
4. **向后兼容**: 保持 API 稳定性

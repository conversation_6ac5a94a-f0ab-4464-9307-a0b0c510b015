[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "investor-insight"
version = "1.0.0"
description = "AI-powered investment research platform backend"
requires-python = ">=3.11"
dependencies = [
    # 核心 Web 框架
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.35.0",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.3.0",

    # 环境变量管理
    "python-dotenv>=1.0.0",

    # 核心依赖
    "shared-bs-core",

    # 插件依赖 - 通过 libs 加载
    "demo-feature-bs>=0.1.0",
    "research-v2b-bs",
    "research-v2h-bs",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
    "ruff>=0.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0"
]

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.hatch.metadata]
allow-direct-references = true

[tool.ruff]
line-length = 88
target-version = "py311"
select = ["E", "F", "UP"]
fixable = ["ALL"]

[tool.mypy]
python_version = "3.11"
strict = true
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
asyncio_mode = "auto"

[tool.uv.sources]
shared-bs-core = { workspace = true }
demo-feature-bs = { workspace = true }
research-v2b-bs = { workspace = true }
research-v2h-bs = { workspace = true }

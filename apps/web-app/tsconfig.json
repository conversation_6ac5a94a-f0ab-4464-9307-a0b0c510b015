{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@yai-investor-insight/api-client": ["../../libs/api-client/src"], "@yai-investor-insight/shared-types": ["../../libs/shared-fe-types/src"], "@yai-investor-insight/demo-feature-fe": ["../../libs/demo-feature-fe/src"], "@yai-investor-insight/user-account-fe": ["../../libs/user-account-fe/src"], "@yai-investor-insight/shared-fe-kit": ["../../libs/shared-fe-kit/src"], "@yai-investor-insight/shared-fe-core": ["../../libs/shared-fe-core/src"], "@yai-investor-insight/research-v2-fe": ["../../libs/research-v2-fe/src"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/types/**/*.d.ts"], "exclude": ["node_modules"]}
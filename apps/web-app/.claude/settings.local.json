{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(find:*)", "Bash(rm:*)", "Bash(pnpm install:*)", "Bash(pnpm nx run-many:*)", "Bash(pnpm nx build:*)", "Bash(pnpm nx dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(./scripts/build-all.sh:*)", "Bash(pnpm list:*)", "Bash(pnpm nx graph:*)", "Bash(pnpm nx show project:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(pnpm nx type-check:*)"], "deny": []}}
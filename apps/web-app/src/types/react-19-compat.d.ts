// React 19 类型兼容性声明文件
// 解决 React 19 中 ReactNode 和 forwardRef 的类型兼容性问题

declare module 'react' {
  // 修复 ReactPortal 类型问题
  interface ReactPortal {
    children?: ReactNode;
  }
  
  // 扩展 ReactElement 类型
  interface ReactElement {
    children?: ReactNode;
  }
}

// 全局类型扩展
declare global {
  namespace React {
    interface ReactPortal {
      children?: React.ReactNode;
    }
    
    interface ReactElement {
      children?: React.ReactNode;
    }
    
    // Fix for React 19 type compatibility
    type ReactNode = import('react').ReactNode;
  }
}

// Fix for Next.js Link component compatibility with React 19
declare module 'next/link' {
  import { ComponentType } from 'react';
  import { LinkProps } from 'next/link';
  
  const Link: ComponentType<LinkProps & { children?: React.ReactNode }>;
  export default Link;
}

export {};
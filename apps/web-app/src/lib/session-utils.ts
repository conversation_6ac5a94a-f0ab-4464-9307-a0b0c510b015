/**
 * webapp的session工具函数
 * 统一使用user-account-fe的Server Actions
 */

'use client';

import { useState, useEffect } from 'react';
import { getCurrentUser, logout as logoutAction } from '@yai-investor-insight/user-account-fe';
import { SessionResult } from '@yai-investor-insight/shared-fe-core';

export interface UseSessionResult {
  session: SessionResult | null;
  isLoading: boolean;
  isLoggedIn: boolean;
  refetch: () => Promise<void>;
  logout: () => Promise<void>;
}

/**
 * webapp统一的session hook
 * 使用user-account-fe的Server Actions
 */
export function useSession(): UseSessionResult {
  const [session, setSession] = useState<SessionResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchSession = async () => {
    try {
      setIsLoading(true);
      const sessionData = await getCurrentUser();
      setSession(sessionData);
    } catch (error) {
      console.error('Failed to fetch session:', error);
      setSession({ isLoggedIn: false });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await logoutAction();
      setSession({ isLoggedIn: false });
    } catch (error) {
      console.error('Failed to logout:', error);
      // 即使logout失败，也要清除客户端状态
      setSession({ isLoggedIn: false });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSession();
  }, []);

  return {
    session,
    isLoading,
    isLoggedIn: session?.isLoggedIn ?? false,
    refetch: fetchSession,
    logout: handleLogout,
  };
}

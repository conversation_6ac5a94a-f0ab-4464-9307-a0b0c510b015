import { createCoagentServer } from '@yai-investor-insight/shared-fe-core';
import axios from 'axios';

// 测试 API 客户端能否正常导入和使用
export function testApiClient() {
  const axiosInstance = axios.create({
    baseURL: 'http://localhost:8000',
    timeout: 5000,
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  });

  const client = createCoagentServer(axiosInstance);

  console.log('API Client created successfully:', client);
  return client;
}
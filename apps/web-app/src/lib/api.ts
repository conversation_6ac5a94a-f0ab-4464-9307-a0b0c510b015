import { createCoagentServer, setGlobalCoagentServer, useCoagentQuery, useCoagentMutation } from '@yai-investor-insight/shared-fe-core';
import axios from 'axios';

// API 配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const SSE_URL = `${API_BASE_URL}/api/stream`;

// 创建 axios 实例
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  },
});

// 创建 coagent 服务器
const coagentServer = createCoagentServer(axiosInstance);
setGlobalCoagentServer(coagentServer);

// API 客户端对象，提供与原api-client兼容的接口
export const apiClient = {
  async getTasks(params?: { status?: string; page?: number; page_size?: number }) {
    const queryParams = params ? new URLSearchParams(params as Record<string, string>).toString() : '';
    const url = queryParams ? `/api/tasks?${queryParams}` : '/api/tasks';
    
    const response = await coagentServer.dispatch({
      type: 'GET',
      payload: { url }
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  },

  async getTask(taskId: string) {
    const response = await coagentServer.dispatch({
      type: 'GET',
      payload: { url: `/api/tasks/${taskId}` }
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  },

  async createTask(task: Record<string, unknown>) {
    const response = await coagentServer.dispatch({
      type: 'POST',
      payload: { url: '/api/tasks', data: task }
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  },

  async updateTask(id: string, task: Record<string, unknown>) {
    const response = await coagentServer.dispatch({
      type: 'PUT',
      payload: { url: `/api/tasks/${id}`, data: task }
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  },

  async deleteTask(id: string) {
    const response = await coagentServer.dispatch({
      type: 'DELETE',
      payload: { url: `/api/tasks/${id}` }
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  },

  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await coagentServer.dispatch({
      type: 'POST',
      payload: { 
        url: '/api/upload', 
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  },

  async getTaskStats() {
    const response = await coagentServer.dispatch({
      type: 'GET',
      payload: { url: '/api/tasks/stats' }
    });
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  }
};

// SSE 客户端的简单实现
export const sseClient = {
  connect(onMessage: (data: unknown) => void, onError?: (error: Event) => void) {
    const eventSource = new EventSource(SSE_URL);
    
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('Failed to parse SSE message:', error);
      }
    };
    
    eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      if (onError) {
        onError(error);
      }
    };
    
    return eventSource;
  }
};

// API 端点常量
export const API_ENDPOINTS = {
  TASKS: '/api/tasks',
  UPLOAD: '/api/upload',
  STREAM: '/api/stream',
  HEALTH: '/api/health'
} as const;

// 导出 coagent hooks 以供组件使用
export { useCoagentQuery, useCoagentMutation };

import { useRef, useState, useEffect, useCallback } from 'react';
import { logger, generateTraceId } from '@yai-investor-insight/shared-fe-core';

export interface AGUIEvent {
  type: string;
  messageId?: string;
  threadId?: string;
  runId?: string;
  delta?: string;
  content?: string;
  data?: unknown;
  rawEvent?: unknown;
  custom?: unknown;
  state?: unknown;
  tool_call_id?: string;
  tool_call_name?: string;
  step_name?: string;
  error?: string;
  value?: unknown;
}

export interface UseAGUIOptions {
  url: string;
  onEvent?: (event: AGUIEvent) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}

// Mock HttpAgent class for now
class HttpAgent {
  private url: string;

  constructor(options: { url: string }) {
    this.url = options.url;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  run(_: unknown) {
    // Mock implementation - returns a mock observable
    return {
      subscribe: (observer: { next: (event: unknown) => void; complete: () => void; error?: (error: unknown) => void }) => {
        // Mock subscription
        setTimeout(() => {
          observer.next({ type: 'message', content: 'Mock response' });
          observer.complete();
        }, 1000);
        
        return {
          unsubscribe: () => {
            // Mock unsubscribe
          }
        };
      }
    };
  }
}

export function useAGUI(options: UseAGUIOptions) {
  const agentRef = useRef<HttpAgent | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error'>('connecting');
  const [isRunning, setIsRunning] = useState(false);



  const handleError = useCallback((error: Error) => {
    options.onError?.(error);
  }, [options]);

  useEffect(() => {
    try {
      agentRef.current = new HttpAgent({
        url: options.url
      });
      setConnectionStatus('connected');
      logger.info('AG-UI HttpAgent 初始化成功', { component: 'useAGUI', url: options.url });
    } catch (error) {
      logger.error('AG-UI 客户端初始化失败', { error, component: 'useAGUI', url: options.url });
      setConnectionStatus('error');
      handleError(error as Error);
    }
  }, [options.url, handleError]);

  const sendMessage = async (
    message: string,
    threadId?: string,
    runId?: string
  ) => {
    if (!agentRef.current || isRunning) {
      logger.warn('AG-UI 客户端未准备好或正在运行中', { component: 'useAGUI', url: options.url });
      return;
    }

    const traceId = generateTraceId();
    const finalThreadId = threadId || `thread_${Date.now()}`;
    const finalRunId = runId || `run_${Date.now()}`;

    setIsRunning(true);

    try {
      const request = {
        threadId: finalThreadId,
        runId: finalRunId,
        messages: [
          {
            id: `msg_${Date.now()}`,
            role: "user" as const,
            content: message
          }
        ],
        tools: [],
        context: [],
        state: {},
        forwardedProps: {}
      };

      logger.info('发送 AG-UI 消息', {
        message,
        request: JSON.stringify(request),
        component: 'useAGUI',
        url: options.url,
        traceId,
        threadId: finalThreadId,
        runId: finalRunId
      });

      const stream = agentRef.current.run(request);

      const subscription = stream.subscribe({
        next: (event: unknown) => {
          logger.debug('收到 AG-UI 事件', {
            eventType: (event as AGUIEvent).type,
            event: JSON.stringify(event),
            component: 'useAGUI',
            url: options.url,
            traceId,
            threadId: finalThreadId,
            runId: finalRunId
          });
          options.onEvent?.(event as AGUIEvent);
        },
        error: (error: unknown) => {
          logger.error('AG-UI 事件流错误', {
            error,
            component: 'useAGUI',
            url: options.url,
            traceId,
            threadId: finalThreadId,
            runId: finalRunId
          });
          setIsRunning(false);
          options.onError?.(error as Error);
        },
        complete: () => {
          logger.info('AG-UI 事件流完成', {
            component: 'useAGUI',
            url: options.url,
            traceId,
            threadId: finalThreadId,
            runId: finalRunId
          });
          setIsRunning(false);
          options.onComplete?.();
        }
      });

      return () => {
        subscription?.unsubscribe?.();
      };

    } catch (error) {
      logger.error('发送消息失败', {
        error,
        component: 'useAGUI',
        url: options.url,
        traceId,
        threadId: finalThreadId,
        runId: finalRunId
      });
      setIsRunning(false);
      options.onError?.(error as Error);
    }
  };

  return {
    connectionStatus,
    isRunning,
    sendMessage,
    agent: agentRef.current
  };
}
/**
 * 事实核查功能的事件处理器
 * 处理AGUI事件并转换为事实核查相关的回调
 */
import { useCallback } from 'react';
import { logger } from '@yai-investor-insight/shared-fe-core';
import { 
  FactCheckTask,
  Claim, 
  AgentStatus, 
  DebateRecord, 
  DebateMessage
} from '../types/factCheck';

interface FactCheckEvent {
  type: string;
  data?: unknown;
}

interface UseFactCheckEventHandlerOptions {
  onTaskStarted?: (task: FactCheckTask) => void;
  onClaimExtracted?: (claims: Claim[]) => void;
  onAgentProgress?: (agentStatus: AgentStatus) => void;
  onVerificationComplete?: (claim: Claim) => void;
  onDebateStarted?: (debate: DebateRecord) => void;
  onDebateMessage?: (claimId: string, message: DebateMessage) => void;
  onDebateConcluded?: (debate: DebateRecord) => void;
  onTaskComplete?: (task: FactCheckTask) => void;
  onCostUpdate?: (cost: number) => void;
  onError?: (error: string) => void;
}

export function useFactCheckEventHandler(options: UseFactCheckEventHandlerOptions) {
  return useCallback((event: FactCheckEvent) => {
    try {
      const { type, data } = event;
      
      logger.debug('处理事实核查事件', { type, data, component: 'useFactCheckEventHandler' });

      switch (type) {
        case 'TASK_STARTED':
          if (data && options.onTaskStarted) {
            options.onTaskStarted(data as FactCheckTask);
          }
          break;

        case 'CLAIM_EXTRACTED':
          if (data && options.onClaimExtracted) {
            options.onClaimExtracted(data as Claim[]);
          }
          break;

        case 'AGENT_PROGRESS':
          if (data && options.onAgentProgress) {
            options.onAgentProgress(data as AgentStatus);
          }
          break;

        case 'VERIFICATION_COMPLETE':
          if (data && options.onVerificationComplete) {
            options.onVerificationComplete(data as Claim);
          }
          break;

        case 'DEBATE_STARTED':
          if (data && options.onDebateStarted) {
            options.onDebateStarted(data as DebateRecord);
          }
          break;

        case 'DEBATE_MESSAGE':
          if (data && typeof data === 'object' && 'claimId' in data && 'message' in data && options.onDebateMessage) {
            const debateData = data as { claimId: string; message: DebateMessage };
            options.onDebateMessage(debateData.claimId, debateData.message);
          }
          break;

        case 'DEBATE_CONCLUDED':
          if (data && options.onDebateConcluded) {
            options.onDebateConcluded(data as DebateRecord);
          }
          break;

        case 'TASK_COMPLETE':
          if (data && options.onTaskComplete) {
            options.onTaskComplete(data as FactCheckTask);
          }
          break;

        case 'COST_UPDATE':
          if (data && typeof data === 'object' && 'cost' in data && typeof (data as { cost: unknown }).cost === 'number' && options.onCostUpdate) {
            const costData = data as { cost: number };
            options.onCostUpdate(costData.cost);
          }
          break;

        case 'ERROR':
          if (data && typeof data === 'object' && 'error' in data && options.onError) {
            const errorData = data as { error: string };
            options.onError(errorData.error);
          }
          break;

        default:
          logger.warn('未知的事实核查事件类型', { type, component: 'useFactCheckEventHandler' });
          break;
      }
    } catch (error) {
      logger.error('处理事实核查事件时发生错误', { error, event, component: 'useFactCheckEventHandler' });
      if (options.onError) {
        options.onError(`事件处理错误: ${error}`);
      }
    }
  }, [options]);
}
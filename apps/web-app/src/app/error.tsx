'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/Button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // 可以在这里记录错误到错误报告服务
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            出现了一些问题
          </h1>
          <p className="text-gray-600 mb-8">
            抱歉，应用程序遇到了错误。请稍后重试。
          </p>
          <div className="space-x-4">
            <Button
              onClick={reset}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              重试
            </Button>
            <Button
              onClick={() => window.location.href = '/'}
              variant="outline"
            >
              返回首页
            </Button>
          </div>
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-8 text-left">
              <summary className="cursor-pointer text-sm text-gray-500">
                错误详情 (仅开发环境显示)
              </summary>
              <pre className="mt-2 text-xs text-red-600 bg-red-50 p-4 rounded overflow-auto">
                {error.message}
                {error.stack && `\n\n${error.stack}`}
              </pre>
            </details>
          )}
        </div>
      </div>
    </div>
  );
}
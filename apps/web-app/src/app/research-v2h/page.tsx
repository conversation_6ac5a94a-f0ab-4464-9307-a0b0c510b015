/**
 * 调研演示 V2H 页面 (<PERSON>版本)
 * 全屏沉浸式布局，无 Header
 * 参考 demo-feature 模式，采用服务端组件架构，集成 Server Actions
 */
import { Metadata } from 'next';
import { ResearchV2Page } from '@yai-investor-insight/research-v2h-fe';

export const metadata: Metadata = {
  title: '调研演示 V2H - YAI投资洞察',
  description: '插件化的智能研究分析平台框架 (Harry版本)，展示模块化架构和组件复用能力',
};

export default function ResearchV2HPageRoute() {
  return (
    <div className="h-screen overflow-hidden">
      <ResearchV2Page />
    </div>
  );
}
'use client';

import { useState } from 'react';
import { sendOtpCode, loginWithMobile, getCurrentUser, logout } from '@yai-investor-insight/user-account-fe';
import { useSession } from '@/lib/session-utils';

interface TestResult {
  success: boolean;
  message?: string;
  data?: unknown;
  error?: string;
}

export default function TestSessionPage() {
  const { session, isLoading, isLoggedIn, refetch } = useSession();
  const [mobile, setMobile] = useState('');
  const [code, setCode] = useState('');
  const [testResults, setTestResults] = useState<Record<string, TestResult>>({});
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  const setLoading = (key: string, loading: boolean) => {
    setLoadingStates(prev => ({ ...prev, [key]: loading }));
  };

  const setResult = (key: string, result: TestResult) => {
    setTestResults(prev => ({ ...prev, [key]: result }));
  };

  // 测试发送验证码
  const testSendOtp = async () => {
    if (!mobile) {
      setResult('sendOtp', { success: false, error: '请输入手机号' });
      return;
    }

    setLoading('sendOtp', true);
    try {
      await sendOtpCode(mobile);
      setResult('sendOtp', { success: true, message: '验证码发送成功' });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '发送验证码失败';
      setResult('sendOtp', { success: false, error: errorMessage });
    } finally {
      setLoading('sendOtp', false);
    }
  };

  // 测试登录
  const testLogin = async () => {
    if (!mobile || !code) {
      setResult('login', { success: false, error: '请输入手机号和验证码' });
      return;
    }

    setLoading('login', true);
    try {
      const result = await loginWithMobile(mobile, code);
      setResult('login', { success: true, data: result });
      await refetch(); // 刷新session状态
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '登录失败';
      setResult('login', { success: false, error: errorMessage });
    } finally {
      setLoading('login', false);
    }
  };

  // 测试获取用户信息
  const testGetUser = async () => {
    setLoading('getUser', true);
    try {
      const result = await getCurrentUser();
      setResult('getUser', { success: true, data: result });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '获取用户信息失败';
      setResult('getUser', { success: false, error: errorMessage });
    } finally {
      setLoading('getUser', false);
    }
  };

  // 测试退出登录
  const testLogout = async () => {
    setLoading('logout', true);
    try {
      await logout();
      setResult('logout', { success: true, message: '退出登录成功' });
      await refetch(); // 刷新session状态
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '退出登录失败';
      setResult('logout', { success: false, error: errorMessage });
    } finally {
      setLoading('logout', false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Session 功能测试页面
          </h1>
          <p className="text-gray-600">
            测试基于iron-session的核心登录功能
          </p>
        </div>

        {/* 用户状态 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">当前用户状态</h2>
          {isLoading ? (
            <p className="text-gray-500">加载中...</p>
          ) : isLoggedIn ? (
            <div className="text-green-600">
              <p>✅ 已登录</p>
              <p>用户ID: {session?.userId}</p>
              <p>手机号: {session?.phone}</p>
              <p>用户名: {session?.userName || '未设置'}</p>
            </div>
          ) : (
            <p className="text-red-600">❌ 未登录</p>
          )}
        </div>

        {/* 测试区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 发送验证码测试 */}
          <TestCard
            title="发送验证码"
            onTest={testSendOtp}
            isLoading={loadingStates.sendOtp}
            result={testResults.sendOtp}
          >
            <input
              type="tel"
              placeholder="请输入手机号"
              value={mobile}
              onChange={(e) => setMobile(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </TestCard>

          {/* 登录测试 */}
          <TestCard
            title="手机号验证码登录"
            onTest={testLogin}
            isLoading={loadingStates.login}
            result={testResults.login}
          >
            <div className="space-y-3">
              <input
                type="tel"
                placeholder="请输入手机号"
                value={mobile}
                onChange={(e) => setMobile(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="text"
                placeholder="请输入验证码"
                value={code}
                onChange={(e) => setCode(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </TestCard>

          {/* 获取用户信息测试 */}
          <TestCard
            title="获取用户信息"
            onTest={testGetUser}
            isLoading={loadingStates.getUser}
            result={testResults.getUser}
          />

          {/* 退出登录测试 */}
          <TestCard
            title="退出登录"
            onTest={testLogout}
            isLoading={loadingStates.logout}
            result={testResults.logout}
          />
        </div>
      </div>
    </div>
  );
}

// 测试卡片组件
function TestCard({
  title,
  children,
  onTest,
  isLoading,
  result
}: {
  title: string;
  children?: React.ReactNode;
  onTest: () => void;
  isLoading?: boolean;
  result?: TestResult;
}) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>

      {children && <div className="mb-4">{children}</div>}

      <button
        onClick={onTest}
        disabled={isLoading}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? '测试中...' : '开始测试'}
      </button>

      {result && (
        <div className="mt-4 p-3 rounded-md text-sm">
          {result.success ? (
            <div className="bg-green-50 text-green-800 border border-green-200">
              <p className="font-medium">✅ 测试成功</p>
              {result.message && <p>{result.message}</p>}
              {(result.data && (
                <pre className="mt-2 text-xs overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              )) as React.ReactNode}
            </div>
          ) : (
            <div className="bg-red-50 text-red-800 border border-red-200">
              <p className="font-medium">❌ 测试失败</p>
              <p>{result.error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
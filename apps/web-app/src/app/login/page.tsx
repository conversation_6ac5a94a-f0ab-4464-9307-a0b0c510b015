'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { LoginPage } from '@yai-investor-insight/user-account-fe';

export default function LoginPageContainer() {
  const router = useRouter();

  const handleLoginSuccess = () => {
    // 登录成功，重定向到首页
    router.push('/');
    router.refresh(); // 刷新页面以更新认证状态
  };

  const homeLink = (
    <Link
      href="/"
      className="text-2xl font-bold text-gray-900 transition-opacity hover:opacity-80"
    >
      AI 投资洞察
    </Link>
  );

  const registerLink = (
    <Link
      href="/register"
      className="font-medium text-blue-600 hover:underline"
    >
      立即注册
    </Link>
  );

  return (
    <LoginPage
      onLoginSuccess={handleLoginSuccess}
      homeLink={homeLink}
      registerLink={registerLink}
      variant="light"
    />
  );
}
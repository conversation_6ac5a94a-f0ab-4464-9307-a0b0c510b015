import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./global.css";
import { QueryClientProvider } from "@/components/providers/QueryClientProvider";

// 强制动态渲染以避免静态生成时的 React Query 错误
export const dynamic = 'force-dynamic';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AI Investor Insight",
  description: "AI-powered investment research platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <QueryClientProvider>
          {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
          {children as any}
        </QueryClientProvider>
      </body>
    </html>
  );
}

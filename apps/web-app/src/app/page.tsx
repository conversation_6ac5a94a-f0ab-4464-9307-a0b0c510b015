'use client';

import { useEffect } from 'react';
import { Layout } from "@/components/layout/Layout";
import { HeroSearchSection } from "@/components/home/<USER>";
import { WorkspaceSection } from "@/components/workspace/WorkspaceSection";
import { CommunitySection } from "@/components/home/<USER>";
import { logger, logComponentMount } from '@yai-investor-insight/shared-fe-core';

export default function HomePage() {
  useEffect(() => {
    // 记录主页加载
    const logPageLoad = async () => {
      try {
        await logger.info('JS前端主页加载', {
          page: 'home',
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          referrer: document.referrer || 'direct'
        });

        await logComponentMount('HomePage');
      } catch (error) {
        console.error('日志记录失败:', error);
      }
    };

    logPageLoad();
  }, []);
  return (
    <Layout>
      <main className="flex flex-1 flex-col">
        {/* Hero Search Section - 顶部紫色搜索区域 */}
        <HeroSearchSection />
        
        {/* Workspace Section - 我的工作区 */}
        <div className="bg-white">
          <WorkspaceSection />
        </div>
        
        {/* Community Section - 来自社区 */}
        <CommunitySection />
        
        {/* 开发环境快速访问链接 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-gray-100 border-t p-4">
            <div className="max-w-7xl mx-auto">
              <h3 className="text-sm font-medium text-gray-600 mb-2">开发工具</h3>
              <div className="flex flex-wrap gap-2">
                <a href="/logging-demo" className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600">
                  日志演示
                </a>
                <a href="/test-coagent" className="text-xs bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600">
                  Session测试
                </a>
                <a href="/demo-feature" className="text-xs bg-purple-500 text-white px-2 py-1 rounded hover:bg-purple-600">
                  Demo功能
                </a>
              </div>
            </div>
          </div>
        )}
      </main>
    </Layout>
  );
}
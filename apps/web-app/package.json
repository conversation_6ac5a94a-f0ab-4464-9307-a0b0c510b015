{"name": "njs-investor-insight", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ag-ui/client": "^0.0.35", "@ag-ui/core": "^0.0.35", "@headlessui/react": "^2.0.0", "@heroicons/react": "^2.0.0", "@tanstack/react-query": "^5.83.0", "axios": "^1.11.0", "@yai-loglayer/browser": "0.7.9", "@yai-loglayer/server": "0.7.9", "@yai-loglayer/sls-transport": "0.7.9", "@yai-nexus/loglayer-support": "^0.5.2", "@yai-investor-insight/research-v2-fe": "workspace:*", "@yai-investor-insight/research-v2b-fe": "workspace:*", "@yai-investor-insight/research-v2h-fe": "workspace:*", "@yai-investor-insight/shared-fe-core": "workspace:*", "@yai-investor-insight/user-account-fe": "workspace:*", "clsx": "^2.0.0", "date-fns": "^3.0.0", "iron-session": "^8.0.4", "lightweight-charts": "^5.0.8", "lucide-react": "^0.525.0", "next": "^15.4.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^3.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@faker-js/faker": "^9.9.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "eslint": "^9.31.0", "eslint-config-next": "^15.4.0", "msw": "^2.10.4", "tailwindcss": "^4", "typescript": "^5.8.3"}}
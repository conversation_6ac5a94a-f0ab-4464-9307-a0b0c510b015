# Coagent 技术实现方案

## 1. 概述

`coagent` 是一个专为 YAI 投资研究平台设计的统一数据请求层，旨在解决 Next.js App Router 架构下客户端 (`use client`) 与服务端 (`use server`) 之间数据交互的复杂性，特别是涉及用户认证和后端服务调用的场景。

## 1.1 当前问题分析

### 问题描述
在实施 coagent 认证功能时遇到以下关键问题：

1. **模块依赖问题**: `axios` 模块无法在 `web-app` 中正确解析
   - 错误信息：`Module not found: Can't resolve 'axios'`
   - 影响：导致 `init-auth.ts` 无法正常工作，全局 CoagentServer 无法初始化

2. **Workspace 依赖管理问题**: 
   - `npm install` 在 `apps/web-app` 目录下失败
   - 错误：`Unsupported URL Type "workspace:": workspace:*`
   - 原因：monorepo 的 workspace 协议需要在根目录管理

3. **全局状态初始化问题**:
   - `Global CoagentServer not configured` 错误
   - 原因：认证初始化函数未能正确设置全局服务器实例

### 解决方案

#### 方案一：修复依赖管理（推荐）
1. **在根目录安装依赖**:
   ```bash
   cd /Users/<USER>/codespace/yai/mono/yai-investor-insight
   pnpm install
   ```

2. **确保 shared-fe-core 正确导出 axios**:
   - 在 `libs/shared-fe-core/src/index.ts` 中导出 axios
   - 或者创建专门的 axios 实例导出

3. **修改 init-auth.ts 使用内部 axios**:
   ```typescript
   // 从 shared-fe-core 导入 axios 实例
   import { createAxiosInstance } from '@yai-investor-insight/shared-fe-core';
   ```

#### 方案二：重构认证初始化
1. **移除对外部 axios 的直接依赖**
2. **使用 shared-fe-core 内置的请求能力**
3. **简化全局状态管理**

#### 方案三：延迟初始化
1. **在组件级别进行 coagent 初始化**
2. **避免在 layout 层面的全局初始化**
3. **使用 React Context 管理认证状态**

### 技术实施细节

#### 当前架构问题
1. **依赖循环**: `web-app` -> `shared-fe-core` -> `axios`，但 `web-app` 缺少 `axios` 依赖
2. **初始化时机**: 在 Server Component (`layout.tsx`) 中调用客户端初始化函数
3. **模块解析**: Next.js 无法正确解析 workspace 内的 axios 依赖

#### 推荐实施步骤
1. **第一阶段：修复依赖**
   - 在根目录执行 `pnpm install` 确保所有依赖正确安装
   - 验证 `shared-fe-core` 的 axios 依赖可被正确访问
   - 测试模块导入是否正常

2. **第二阶段：重构初始化**
   - 将 `init-auth.ts` 改为纯客户端模块
   - 在 `test-coagent` 页面中进行初始化测试
   - 确保全局 CoagentServer 正确设置

3. **第三阶段：集成验证**
   - 测试 `useCoagentQuery` 和 `useCoagentMutation` 功能
   - 验证 `lucas-uniq-userId` 头部注入
   - 确保认证流程完整性

### 风险评估

#### 高风险
- **依赖管理复杂性**: monorepo 环境下的模块解析可能持续出现问题
- **Next.js 兼容性**: App Router 的 SSR/CSR 边界可能影响初始化时机

#### 中风险
- **性能影响**: 全局状态管理可能影响应用启动性能
- **类型安全**: 跨库的类型定义可能不一致

#### 低风险
- **功能回退**: 可以回退到直接使用 axios 的方案
- **渐进升级**: 可以逐步迁移现有代码

### 验收标准
1. **功能完整性**:
   - [ ] `useCoagentQuery` 正常工作
   - [ ] `useCoagentMutation` 正常工作
   - [ ] 认证头部自动注入
   - [ ] 公共接口正确处理

2. **性能要求**:
   - [ ] 初始化时间 < 100ms
   - [ ] 请求响应时间无明显增加
   - [ ] 内存占用合理

3. **开发体验**:
   - [ ] TypeScript 类型提示完整
   - [ ] 错误信息清晰
   - [ ] 调试信息充分

### 具体实现方案

#### 方案A：修复当前架构（优先级：高）

**步骤1：依赖管理修复**
```bash
# 在项目根目录执行
cd /Users/<USER>/codespace/yai/mono/yai-investor-insight
pnpm install
```

**步骤2：修改 shared-fe-core 导出**
```typescript
// libs/shared-fe-core/src/index.ts
export { default as axios } from 'axios';
export * from './lib/coagent';
```

**步骤3：重构 init-auth.ts**
```typescript
// apps/web-app/src/lib/init-auth.ts
import { axios, setGlobalCoagentServer, createCoagentServer } from '@yai-investor-insight/shared-fe-core';
import { setAuthConfig } from '@yai-investor-insight/user-account-fe';
import { getCoagentAuthConfig } from './coagent-auth';

export async function initializeAuth() {
  try {
    const authConfig = await getCoagentAuthConfig();
    setAuthConfig(authConfig);
    
    const axiosInstance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
      timeout: 10000,
    });
    
    const globalServer = createCoagentServer(axiosInstance, {
      getUserId: authConfig.getUserId,
      createHeaders: authConfig.createHeaders,
    });
    
    setGlobalCoagentServer(globalServer);
    console.log('✅ Coagent authentication initialized');
  } catch (error) {
    console.error('❌ Failed to initialize coagent:', error);
  }
}
```

#### 方案B：组件级初始化（备选方案）

**创建认证上下文**
```typescript
// apps/web-app/src/components/providers/CoagentProvider.tsx
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { initializeAuth } from '../../lib/init-auth';

const CoagentContext = createContext<{ initialized: boolean }>({ initialized: false });

export function CoagentProvider({ children }: { children: React.ReactNode }) {
  const [initialized, setInitialized] = useState(false);
  
  useEffect(() => {
    initializeAuth().then(() => setInitialized(true));
  }, []);
  
  return (
    <CoagentContext.Provider value={{ initialized }}>
      {children}
    </CoagentContext.Provider>
  );
}

export const useCoagent = () => useContext(CoagentContext);
```

### 实施时间计划

#### 第一周：问题修复
- **Day 1-2**: 依赖管理修复，确保 axios 正确导入
- **Day 3-4**: 重构 init-auth.ts，测试基本功能
- **Day 5**: 集成测试，验证认证流程

#### 第二周：功能完善
- **Day 1-2**: 完善错误处理和日志记录
- **Day 3-4**: 性能优化和类型安全改进
- **Day 5**: 文档更新和代码审查

#### 第三周：测试验收
- **Day 1-3**: 全面功能测试
- **Day 4**: 性能测试和压力测试
- **Day 5**: 最终验收和部署准备

### 回滚计划

如果实施过程中遇到无法解决的问题，可以按以下顺序回滚：

1. **Level 1**: 回滚到直接使用 axios 的简单实现
2. **Level 2**: 移除 coagent 层，直接在组件中处理认证
3. **Level 3**: 使用传统的 API 路由方式处理后端调用

### 监控指标

- **错误率**: < 1%
- **初始化成功率**: > 99%
- **平均响应时间**: < 200ms
- **内存使用**: 增长 < 10MB

## 2. 核心目标

- **简化开发**: 提供统一的 API，屏蔽底层 `fetch`、`cookie` 解析和 `header` 设置的复杂性。
- **统一认证**: 自动化处理基于 `cookie` 的用户身份验证，无缝注入 `lucas-uniq-userId` 请求头。
- **安全可靠**: 在服务端 (`Server Actions`) 通过 Node.js `http` 模块直接调用后端服务，避免敏感信息暴露到客户端。
- **灵活配置**: 支持公共接口和私有接口的区分，满足不同场景的请求需求。

## 技术架构

`coagent` 提供基于 Action-Response 模式的核心实现：

**核心版本** (`@yai-investor-insight/shared-fe-core`) - 基于 Action-Response 模式的核心实现

### 3.1. 核心版本：基于 Action-Response 模式的实现 (`@yai-investor-insight/shared-fe-core`)

这是一个轻量级的 `coagent` 实现，采用 Action-Response 模式，专注于提供统一的数据请求接口。

#### a. 服务端核心 (`service.ts`)

定义了 `CoagentAction`、`CoagentResponse` 和 `CoagentServer` 接口，提供了基于 Axios 的服务端请求处理能力。

```typescript:libs/shared-fe-core/src/lib/coagent/service.ts
import { AxiosInstance } from 'axios';

export interface CoagentAction {
  type: string;
  payload?: any;
}

export interface CoagentResponse<T = any> {
  data: T;
  error?: string;
}

export interface CoagentServer {
  dispatch: (action: CoagentAction) => Promise<CoagentResponse>;
}

export function createCoagentServer(axiosInstance: AxiosInstance): CoagentServer {
  return {
    dispatch: async (action: CoagentAction) => {
      try {
        const response = await axiosInstance.post('/api/coagent', action);
        return response.data;
      } catch (error: any) {
        return {
          data: null,
          error: error.message || 'Unknown error occurred'
        };
      }
    }
  };
}
```

#### b. 客户端 Hooks (`client.ts`)

提供了基于 `@tanstack/react-query` 的 React Hooks，支持查询和变更操作。

```typescript:libs/shared-fe-core/src/lib/coagent/client.ts
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { CoagentAction, CoagentResponse, CoagentServer } from './server';

export interface CoagentClient {
  useCoagentQuery: <T>(
    queryKey: unknown[],
    action: CoagentAction,
    options?: any
  ) => any;
  useCoagentMutation: <T, TVariables = void>(
    mutationKey: unknown[],
    action: CoagentAction,
    options?: any
  ) => any;
}

export function createCoagentClient(server: CoagentServer): CoagentClient {
  return {
    useCoagentQuery: <T>(queryKey: unknown[], action: CoagentAction, options?: any) => {
      return useQuery<CoagentResponse<T>, Error>({
        queryKey: queryKey,
        queryFn: () => server.dispatch(action),
        ...options,
      });
    },
    useCoagentMutation: <T, TVariables = void>(
      mutationKey: unknown[],
      action: CoagentAction,
      options?: any
    ) => {
      const queryClient = useQueryClient();
      return useMutation<CoagentResponse<T>, Error, TVariables>({
        mutationKey: mutationKey,
        mutationFn: (variables: TVariables) => server.dispatch({ ...action, payload: variables }),
        ...options,
        onSuccess: (data: CoagentResponse<T>, variables: TVariables, context: unknown) => {
          if (options?.onSuccess) {
            options.onSuccess(data, variables, context);
          }
          queryClient.invalidateQueries({ queryKey: mutationKey });
        },
      });
    },
  };
}
```

#### c. Axios 适配器 (`adapter.ts`)

提供了将现有 Axios 请求转换为 CoagentAction 的适配器，便于集成现有的 API 客户端。

```typescript:libs/shared-fe-core/src/lib/coagent/adapter.ts
import { AxiosInstance, AxiosRequestConfig } from 'axios';
import { CoagentAction, CoagentServer } from './server';

export function createCoagentAdapter(server: CoagentServer) {
  return (config: AxiosRequestConfig) => {
    const action: CoagentAction = {
      type: 'axios',
      payload: {
        method: config.method,
        url: config.url,
        data: config.data,
        params: config.params,
        headers: config.headers
      }
    };

    return server.dispatch(action).then(response => {
      if (response.error) {
        return Promise.reject(new Error(response.error));
      }
      return response.data;
    });
  };
}
```

### 3.3. 统一请求头处理：更优的方案

在最初的设计中，我们考虑过在各个业务方的 `api-client`（例如 `user-account-fe` 的 `custom-instance.ts`）中通过拦截器注入 `userId`。然而，这种方法存在以下缺点：

- **重复工作**：每个需要认证的 `api-client` 都需要实现相似的头部注入逻辑。
- **耦合度高**：业务 `api-client` 与认证逻辑紧密耦合，增加了维护成本。
- **灵活性差**：如果认证方式变更（例如从 `userId` 变为 `token`），需要修改所有相关的 `api-client`。

**更优的方案是将请求头处理逻辑统一收敛到 `coagentServer` 中。**

`coagent` 作为统一的数据请求层，其服务端核心 `coagentServer` 是处理认证和请求头的理想位置。所有通过 `coagent` 发出的请求都会经过 `coagentServer`，我们可以在这里集中处理所有私有接口的认证逻辑。

**优势：**

1.  **单一职责**：`coagentServer` 专职负责服务端请求的构建和发送，包括认证头的处理。业务 `api-client` 只需关注业务接口的定义和调用。
2.  **完全解耦**：业务 `api-client`（如 `user-account-fe`）不再需要关心 `userId` 如何获取和注入，实现了与认证机制的完全分离。
3.  **易于维护**：当认证逻辑需要变更时，只需修改 `coagent/service.ts` 一个文件即可，所有调用方自动生效。
4.  **更高的安全性**：认证凭证（如 `session`）的处理被严格限制在服务端，不会意外泄露到客户端或业务逻辑代码中。

因此，我们将在 `coagent/service.ts` 的 `createHeaders` 函数中统一实现 `lucas-uniq-userId` 的注入逻辑，而 `custom-instance.ts` 则保持纯净，只负责基础的 `axios` 实例创建和 `baseUrl` 配置。

### 3.4. 客户端 Hooks (`client.ts`)

客户端通过自定义 Hooks 与 `coagent` 交互。这些 Hooks 基于 `TanStack React Query`，提供了强大的数据获取和状态管理功能。



## 5. 工作流程

### 场景1: 客户端组件 (`use client`) 获取私有数据

1.  **开发者调用**: 在 React 组件中调用 `useCoagent({ endpoint: '/api/data' })`。
2.  **客户端请求**: `useCoagent` 内部的 `useQuery` 向一个 Server Action (例如 `app/actions/coagent.ts`) 发起 `fetch` 请求。浏览器自动携带用户的 `cookie`。
3.  **服务端处理**: Server Action 被触发，执行 `coagent` 的核心逻辑。
4.  **认证与封装**: `coagent` 解析请求中的 `cookie`，获取 `userId`，并将其注入到 `lucas-uniq-userId` 请求头中。
5.  **后端调用**: `coagent` 使用 Node.js `http` 模块，带着新的请求头，向真正的后端数据服务 (如 `http://api.service:8080/data`) 发起请求。
6.  **数据返回**: 后端服务返回数据，`coagent` 将其传递回 Server Action，最终返回给客户端组件。

### 场景2: 调用公共接口 (如登录)

1.  **开发者调用**: `useCoagent({ endpoint: '/api/login', isPublic: true })`。
2.  **流程类似**: 流程与场景1类似，但在第4步，`coagent` 会识别到 `isPublic: true` 的标志。
3.  **跳过认证**: `coagent` 会跳过 `cookie` 解析和请求头注入的步骤，直接向后端公共接口发起请求。

## 6. 目录结构

`coagent` 相关代码分布在两个库中，以便在整个 Monorepo 中共享和复用。

### 6.1. 核心版本目录结构 (`shared-fe-core`)

```
libs/shared-fe-core/
└── src/
    ├── index.ts         # 库主入口
    └── lib/
        └── coagent/
            ├── index.ts     # coagent 模块入口
            ├── service.ts   # Action-Response 服务端实现
            ├── client.ts    # React Query Hooks
            └── adapter.ts   # Axios 适配器
```

### 6.2. 项目配置文件

```
# 根目录配置
tsconfig.base.json           # 路径别名配置
package.json                 # 工作区依赖管理
pnpm-workspace.yaml          # pnpm 工作区配置

# 库配置
libs/shared-fe-core/package.json     # 核心库依赖
```

## 7. 调用时序图

```mermaid
sequenceDiagram
    participant Browser as Next.js 浏览器端

    box "coagent 封装"
        participant Hook as useCoagent Hook
        participant Action as Server Action
        participant Coagent as coagentServer
        participant Client as Account API Client
    end

    participant Backend as 后端服务 (yai-account-service)

    Browser->>Hook: 1. 调用 useCoagent
    Hook->>Action: 2. 触发 Server Action
    Action->>Coagent: 3. 调用 coagentServer 方法
    Coagent->>Client: 4. 使用 API Client 发起请求
    Client->>Backend: 5. 请求内部服务
    Backend-->>Client: 6. 返回响应
    Client-->>Coagent: 7. 返回数据
    Coagent-->>Action: 8. 返回数据
    Action-->>Hook: 9. 返回数据
    Hook-->>Browser: 10. 更新状态

```

上图展示了一个完整的请求流程：

1. 客户端组件使用 `useCoagent` hooks 发起请求
2. hooks 触发 Server Action
3. Server Action 调用 `coagentServer` 的对应方法
4. `coagentServer` 从 cookie 中获取用户 ID
5. `coagentServer` 构造请求头（对私有接口注入 userId）
6. `coagentServer` 直接请求内部服务
7. 响应数据沿原路径返回到客户端组件

## 8. 实际实现状态

### 8.1. 当前项目结构

根据最新的代码检查，项目中已经实现了两个版本的 `coagent`：

#### 核心版本 (`@yai-investor-insight/shared-fe-core`)
- ✅ **已实现** - 基于 Action-Response 模式的轻量级实现
- ✅ **已配置** - 支持 Axios 适配器模式
- ✅ **已导出** - 提供统一的 coagent 接口

### 8.2. 工作区配置状态

#### 依赖管理
- ✅ **已解决** - 为 `shared-fe-core` 创建了 `package.json`
- ✅ **已安装** - `axios` 和 `@tanstack/react-query` 依赖已正确配置
- ✅ **已更新** - `tsconfig.base.json` 路径别名已配置为 `@yai-investor-insight/shared-fe-core`

#### 路径别名配置
```json
// tsconfig.base.json
{
  "paths": {
    "@yai-investor-insight/shared-fe-core": ["libs/shared-fe-core/src/index.ts"]
  }
}
```

### 8.3. 使用示例

#### 使用核心版本

```typescript
// 在 React 组件中使用
import { useCoagentQuery, useCoagentMutation } from '@yai-investor-insight/shared-fe-core';

function UserProfile() {
  // 查询用户信息
  const { data, isLoading, error } = useCoagentQuery({
    queryKey: ['user', 'profile'],
    endpoint: '/api/user/profile',
    // isPublic: false (默认，会自动注入 userId 请求头)
  });

  // 更新用户信息
  const updateUser = useCoagentMutation({
    endpoint: '/api/user/profile',
    method: 'PUT',
    onSuccess: () => {
      // 更新成功后的处理
    }
  });

  return (
    <div>
      {isLoading && <div>Loading...</div>}
      {error && <div>Error: {error.message}</div>}
      {data && <div>User: {data.name}</div>}
    </div>
  );
}
```

#### 使用核心版本 (适用于现有项目集成)

```typescript
// 创建 coagent 客户端
import { createCoagentServer, createCoagentClient, createCoagentAdapter } from '@yai-investor-insight/shared-fe-core';
import axios from 'axios';

const axiosInstance = axios.create({
  baseURL: process.env.API_BASE_URL
});

const server = createCoagentServer(axiosInstance);
const client = createCoagentClient(server);

// 在组件中使用
function MyComponent() {
  const query = client.useCoagentQuery(
    ['data'],
    { type: 'fetch', payload: { endpoint: '/api/data' } }
  );
  
  return <div>{query.data?.data}</div>;
}
```

## 9. Cookie 处理机制

`coagent` 的认证能力依赖于底层的 Cookie 处理机制。本项目使用 `iron-session` 库来实现安全的会话管理，通过加密的 Cookie 存储用户登录状态和关键信息。

### 9.1. 登录与会话创建

当用户成功登录后（例如通过 `loginWithCode` 函数），系统会执行以下操作：

1.  **验证网关响应**：确认后端认证服务返回成功。
2.  **创建会话**：使用 `getIronSession` 创建一个安全的会-话实例。
3.  **存储会话数据**：将用户的核心信息存入会话对象中。主要包括：
    -   `isLoggedIn`: `true`
    -   `userId`: 用户唯一标识
    -   `phone`: 用户手机号
    -   `userName`: 用户名
    -   `token`: 后端服务下发的认证令牌```
4.  **加密并保存 Cookie**：调用 `session.save()`，`iron-session` 会将上述数据加密，并将其作为 `HttpOnly`、`Secure` 的 Cookie（名为 `yai-session`）下发到浏览器。这个 Cookie 使用 `SECRET_COOKIE_PASSWORD` 环境变量作为密钥进行加密，确保即使 Cookie 被截获，其内容也无法被解读。
```
```typescript:apps/web-app/src/app/actions/sessionActions.ts
// 简化版登录逻辑
export async function loginWithCode(phone: string, code: string): Promise<LoginResult> {
  const response = await authWithMobile({ mobile: phone, code });

  if (response.data.code === '0' && response.data.data) {
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);
    const authData = response.data.data;

    session.isLoggedIn = true;
    session.userId = authData.user?.id?.toString();
    session.phone = phone;
    session.userName = authData.user?.name;
    session.token = authData.token;

    await session.save(); // 加密并写入 Cookie

    return { success: true };
  }
  // ...
}
```

### 9.2. 请求中的 Cookie 解析

`coagentServer` 的核心优势在于其能够在服务端自动、安全地解析这些加密的 Cookie。

-   **`getUserIdFromCookie` 函数**: 在 `coagent/service.ts` 中，此函数是连接 `coagent` 与 `iron-session` 的桥梁。
-   **工作流程**:
    1.  当客户端通过 `coagent` 发起请求时，浏览器会自动携带 `yai-session` 这个 Cookie。
    2.  请求到达 Server Action 后，`coagentServer` 内部的 `getUserIdFromCookie` 函数被调用。
    3.  该函数使用 `getIronSession` 和预定义的密钥 (`SECRET_COOKIE_PASSWORD`) 来解密 Cookie。
    4.  解密成功后，从中提取出 `userId`，并用于后续的 `lucas-uniq-userId` 请求头注入。

这个过程完全在服务端进行，确保了密钥和会话内容的安全，客户端代码无需也无法直接访问这些敏感信息。

### 9.3. 安全配置

Cookie 的安全性由 `iron-session` 的配置保证，主要包括：

-   **`password`**: 一个至少32位的强密钥，用于加密和解密会话数据。
-   **`cookieName`**: 自定义的 Cookie 名称，例如 `yai-session`。
-   **`cookieOptions`**:
    -   `secure: true` (生产环境): 强制 Cookie 只能通过 HTTPS 传输。
    -   `httpOnly: true`: 防止客户端 JavaScript 通过 `document.cookie` 访问 Cookie，有效抵御 XSS 攻击。
    -   `maxAge`: Cookie 的有效期，例如7天。
    -   `sameSite: 'lax'`: 提供对 CSRF 攻击的防护。

## 10. 架构演进与版本对比

### 10.1. 两个版本的设计理念

#### 核心版本 (`shared-fe-core`) - 通用性优先
- **设计理念**: 提供通用的 Action-Response 抽象层
- **适用场景**: 现有项目集成，需要灵活的适配能力
- **优势**:
  - 框架无关的设计
  - 易于集成现有 Axios 代码
  - 轻量级实现
  - 支持多种后端架构

### 10.2. 未来发展方向

1. **统一接口**: 考虑在两个版本之间提供统一的接口层
2. **插件化**: 支持更多的认证方式和中间件
3. **性能优化**: 实现请求缓存和批处理机制
4. **监控集成**: 添加请求追踪和性能监控
5. **文档完善**: 提供更多的使用示例和最佳实践

## 11. 总结

`coagent` 通过在服务端统一处理认证和请求逻辑，极大地简化了 Next.js 应用的开发。它有效地将客户端的便利性与服务端的安全性结合起来，为构建复杂的、需要身份验证的 Web 应用提供了一个强大而优雅的解决方案。
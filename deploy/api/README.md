# API Server 部署文档

本文档介绍如何构建和部署 YAI Investor Insight API Server。

## 构建方式

### 1. 本地开发环境构建

使用统一的构建脚本来设置开发环境：

```bash
# 在项目根目录执行
./scripts/build-backend.sh
```

这个脚本会：
- 检查Python和uv环境
- 创建虚拟环境（如果不存在）
- 安装所有依赖
- 构建Python包
- 验证构建结果

构建完成后，可以使用以下命令启动开发服务器：

```bash
cd apps/api-server
source .venv/bin/activate
python main.py
```

### 2. Docker镜像构建

Docker镜像构建复用相同的构建脚本，通过环境变量区分运行环境：

```bash
# 在项目根目录执行
docker build -f deploy/api/Dockerfile -t yai-investor-insight-api:latest .
```

## Docker镜像特性

### 构建过程

Docker镜像使用相同的 `scripts/build-backend.sh` 构建脚本，通过 `DOCKER_BUILD=1` 环境变量自动适配Docker环境：

**本地环境模式**（`DOCKER_BUILD` 未设置）：
1. 创建并使用Python虚拟环境
2. 安装开发依赖（包含dev组）
3. 使用workspace依赖

**Docker环境模式**（`DOCKER_BUILD=1`）：
1. 跳过虚拟环境创建，直接使用系统Python
2. 按依赖顺序安装本地Python库
3. 使用本地路径依赖（在Dockerfile中预处理pyproject.toml）
4. 只安装生产依赖

### 镜像结构

```
/app/
├── libs/                    # 本地依赖库
│   ├── shared-bs-core/
│   ├── shared-bs-llm/
│   ├── demo-feature-bs/
│   ├── research-v2-bs/
│   ├── research-v2b-bs/
│   └── research-v2h-bs/
├── apps/api-server/         # API服务器应用
│   ├── src/
│   ├── dist/               # 构建产物
│   ├── main.py
│   └── pyproject.toml
└── scripts/                # 构建脚本
    └── build-backend.sh    # 统一构建脚本
```

### 运行容器

```bash
# 基本运行
docker run -p 8000:8000 yai-investor-insight-api:latest

# 后台运行
docker run -d -p 8000:8000 --name yai-api yai-investor-insight-api:latest

# 挂载环境变量文件
docker run -p 8000:8000 --env-file .env yai-investor-insight-api:latest

# 查看日志
docker logs yai-api
```

## 环境变量

API服务器支持以下环境变量：

```bash
# API配置
API_HOST=0.0.0.0
API_PORT=8000

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost/db

# 外部服务配置
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# 日志配置
LOG_LEVEL=INFO
```

## 构建脚本特性

### 环境自适应

`scripts/build-backend.sh` 脚本通过 `DOCKER_BUILD` 环境变量自动适配不同环境：

- **本地环境**：创建虚拟环境，安装开发依赖
- **Docker环境**：使用系统Python，按顺序安装本地库，只安装生产依赖

### 依赖安装顺序

Docker环境中按以下顺序安装本地Python库：
1. `shared-bs-core`
2. `shared-bs-llm`
3. `demo-feature-bs`
4. `research-v2-bs`
5. `research-v2b-bs`
6. `research-v2h-bs`

## 故障排除

### 常见问题

1. **构建失败 - 依赖冲突**
   ```bash
   # 清理本地环境重新构建
   rm -rf apps/api-server/.venv
   ./scripts/build-backend.sh
   ```

2. **Docker构建失败 - 网络问题**
   ```bash
   # 构建时已使用阿里云镜像源，如仍有问题可检查网络连接
   docker build --no-cache -f deploy/api/Dockerfile -t yai-investor-insight-api:latest .
   ```

3. **容器启动失败 - 端口占用**
   ```bash
   # 使用不同端口
   docker run -p 8001:8000 yai-investor-insight-api:latest
   ```

### 调试模式

```bash
# 进入容器调试
docker run -it --entrypoint /bin/bash yai-investor-insight-api:latest

# 查看构建日志
docker build --no-cache -f deploy/api/Dockerfile -t yai-investor-insight-api:latest .
```

## 开发工作流

### 本地开发

1. 使用统一构建脚本设置环境：`./scripts/build-backend.sh`
2. 在虚拟环境中开发和测试
3. 提交代码前运行测试

### 部署流程

1. 本地测试通过后构建Docker镜像
2. 阿里云云效自动执行Dockerfile构建
3. 在目标环境部署容器

```bash
# 本地开发和测试
./scripts/build-backend.sh

# Docker镜像构建（通常由CI/CD自动执行）
docker build -f deploy/api/Dockerfile -t yai-investor-insight-api:latest .
```

## 优势

1. **统一构建逻辑**：本地和Docker使用相同的构建脚本
2. **环境自适应**：脚本自动检测运行环境并调整行为
3. **依赖管理优化**：正确处理workspace依赖和安装顺序
4. **简化维护**：只需维护一个构建脚本
5. **CI/CD友好**：适合阿里云云效等自动化构建平台

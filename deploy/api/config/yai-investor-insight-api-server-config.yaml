apiVersion: v1
kind: ConfigMap
metadata:
  name: yai-investor-insight-api-server-config
data:
  HOST: "0.0.0.0"
  PORT: "8000"
  DEBUG: "true"
  LLM__OPENAI_API_KEY: "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"
  OPENROUTER_API_KEY: "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"
  OPENROUTER_BASE_URL: "https://openrouter.ai/api/v1"
  CLAUDE_API_KEY: "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"
  CLAUDE_BASE_URL: "https://openrouter.ai/api/v1"
  PERPLEXITY_API_KEY: "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"
  PERPLEXITY_BASE_URL: "https://openrouter.ai/api/v1"
  APP_NAME: "investor-insight"
  LOG_APP_NAME: "investor-insight"
  LOG_CONSOLE_ENABLED: "true"
  LOG_FILE_ENABLED: "true"
  LOG_FILE_PATH: "logs/investor-insight.log"
  LOG_UVICORN_INTEGRATION_ENABLED: "true"
  LOG_LEVEL: "DEBUG"
  ENVIRONMENT: "development"
  SLS_ENABLED: "true"
  SLS_ENDPOINT: "cn-beijing.log.aliyuncs.com"
  SLS_ACCESS_KEY_ID: "LTAI5tPrkKMrPLW1XjbBxwhm"
  SLS_ACCESS_KEY_SECRET: "******************************"
  SLS_PROJECT: "yai-log-test"
  SLS_LOGSTORE: "app-log"
  SLS_REGION: "cn-beijing"
  SLS_TOPIC: "default"
  LANGFUSE_SECRET_KEY: "******************************************"
  LANGFUSE_PUBLIC_KEY: "pk-lf-6ff5f2d4-caf1-497e-a4d0-909a7a956b05"
  LANGFUSE_HOST: "http://**************:3000"

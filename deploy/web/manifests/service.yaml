apiVersion: apps/v1
kind: Deployment
metadata:
  name: yai-investor-insight-web-app
  namespace: default
  labels:
    app: yai-investor-insight-web-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: yai-investor-insight-web-app
  template:
    metadata:
      labels:
        app: yai-investor-insight-web-app
    spec:
      containers:
      - name: yai-investor-insight-web-app
        image: ${SERVICE_IMAGE}
        ports:
        - containerPort: 3000
        envFrom:
          # 批量导入应用配置
          - configMapRef:
              name: yai-investor-insight-web-app-config
          # 保留原有的 SLS 配置
          - configMapRef:
              name: sls-config
          - secretRef:
              name: sls-secret
        resources:
          requests:
            cpu: "50m"
            memory: "100Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
---
apiVersion: v1
kind: Service
metadata:
  name: yai-investor-insight-web-app-service
  namespace: default
  labels:
    app: yai-investor-insight-web-app
spec:
  selector:
    app: yai-investor-insight-web-app
  ports:
    - protocol: TCP
      port: 3000
      targetPort: 3000
  type: LoadBalancer
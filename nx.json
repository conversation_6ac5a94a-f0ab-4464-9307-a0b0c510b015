{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "sharedGlobals": []}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "outputs": ["{projectRoot}/dist", "{projectRoot}/.next"], "cache": true}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true}, "dev": {"dependsOn": ["build:libs"], "cache": false}, "start": {"cache": false}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.js"], "cache": true}, "type-check": {"inputs": ["default"], "cache": true}, "docker:build": {"dependsOn": ["build"], "cache": true}, "docker:up": {"cache": false}, "docker:down": {"cache": false}}, "plugins": [{"plugin": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "devTargetName": "dev", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/js", "options": {"targetName": "build"}}, {"plugin": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "watchDepsTargetName": "watch-deps", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}, {"plugin": "@nx/rollup/plugin", "options": {"buildTargetName": "build", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "jest:test"}}], "generators": {"@nx/next": {"application": {"style": "css", "linter": "eslint"}}, "@nx/react": {"library": {"style": "css", "linter": "none", "unitTestRunner": "none"}, "application": {"babel": true, "style": "css", "linter": "none", "bundler": "vite"}, "component": {"style": "css"}}}, "defaultBase": "master", "tags": [{"name": "scope:shared", "description": "共享代码库，可被所有模块依赖"}, {"name": "scope:user-auth", "description": "用户认证功能模块"}, {"name": "scope:market-data", "description": "市场行情功能模块"}, {"name": "scope:portfolio-management", "description": "投资组合管理功能模块"}, {"name": "scope:news-analysis", "description": "新闻分析功能模块"}, {"name": "scope:company-financials", "description": "公司财报功能模块"}, {"name": "scope:demo-feature", "description": "演示功能模块（试点）"}, {"name": "type:fe", "description": "前端代码库"}, {"name": "type:bs", "description": "后端服务代码库"}, {"name": "type:app", "description": "应用程序"}], "implicitDependencies": {}}
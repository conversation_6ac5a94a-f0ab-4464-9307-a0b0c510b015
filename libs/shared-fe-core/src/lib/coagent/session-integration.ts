/**
 * Coagent与Session的集成工具
 * 提供基于最新session实现的便捷创建函数
 */

import { AxiosInstance } from 'axios';
import { createCoagentServer, CoagentServer, GetUserIdFunction, CreateHeadersFunction } from './service';
import { getUserIdFromDefaultSession } from '../session/session-utils';

/**
 * 创建认证请求头
 * 根据用户ID添加lucas-uniq-userId头
 */
export async function createAuthHeaders(
  userId: string | null, 
  existingHeaders: Record<string, string> = {}
): Promise<Record<string, string>> {
  const headers: Record<string, string> = { ...existingHeaders };
  
  // 如果有用户ID，则注入lucas-uniq-userId请求头
  if (userId) {
    headers['lucas-uniq-userId'] = userId;
  }
  
  // 确保Content-Type存在
  if (!headers['Content-Type'] && !headers['content-type']) {
    headers['Content-Type'] = 'application/json';
  }
  
  return headers;
}

/**
 * 基于session创建Coagent服务器
 * 自动集成用户身份认证功能
 * 
 * @param axiosInstance - axios实例
 * @param cookiesGetter - 获取cookies实例的函数，通常是 () => cookies()
 * @returns CoagentServer实例
 */
export function createSessionBasedCoagentServer(
  axiosInstance: AxiosInstance,
  cookiesGetter: () => Promise<any>
): CoagentServer {
  const getUserId: GetUserIdFunction = async () => {
    try {
      const cookiesInstance = await cookiesGetter();
      return await getUserIdFromDefaultSession(cookiesInstance);
    } catch (error) {
      console.warn('Failed to get user ID from session:', error);
      return null;
    }
  };

  const createHeaders: CreateHeadersFunction = async (userId, existingHeaders) => {
    return createAuthHeaders(userId, existingHeaders);
  };

  return createCoagentServer(axiosInstance, {
    getUserId,
    createHeaders
  });
}

/**
 * 创建默认的请求头
 */
export function createDefaultHeaders(): Record<string, string> {
  return {
    'Content-Type': 'application/json',
  };
}

/**
 * 为现有的Coagent服务器添加自定义请求头
 */
export function addCustomHeaders(
  baseHeaders: Record<string, string>,
  customHeaders: Record<string, string>
): Record<string, string> {
  return {
    ...baseHeaders,
    ...customHeaders
  };
}

/**
 * 检查请求是否需要认证
 * 根据URL路径判断是否为公开接口
 */
export function isPublicEndpoint(url: string): boolean {
  const publicPaths = [
    '/health',
    '/ping',
    '/public',
    '/login',
    '/register',
    '/forgot-password'
  ];
  
  return publicPaths.some(path => url.includes(path));
}

/**
 * 创建带有自动认证检测的Coagent服务器
 * 自动判断接口是否需要认证
 */
export function createSmartCoagentServer(
  axiosInstance: AxiosInstance,
  cookiesGetter: () => Promise<any>
): CoagentServer {
  const baseServer = createSessionBasedCoagentServer(axiosInstance, cookiesGetter);
  
  return {
    dispatch: async (action) => {
      // 如果payload中没有明确指定isPublic，则自动判断
      if (action.payload && action.payload.isPublic === undefined) {
        const url = action.payload.url || '';
        action.payload.isPublic = isPublicEndpoint(url);
      }
      
      return baseServer.dispatch(action);
    }
  };
}

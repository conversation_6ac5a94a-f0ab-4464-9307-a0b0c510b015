# Coagent - HTTP请求代理

Coagent是一个HTTP请求代理系统，核心功能是通过cookies中解析出的userId，自动添加认证header头，代理请求到下游的HTTP服务。

## 核心特性

- 🔐 **自动认证**：从cookies中解析userId，自动添加`lucas-uniq-userId`请求头
- 🚀 **简化使用**：与最新的session模块深度集成
- 📝 **请求日志**：自动记录API请求和响应日志
- 🎯 **智能判断**：自动识别公开接口，无需手动指定
- ⚡ **React集成**：提供React Query集成的hooks

## 核心工作流程

```mermaid
graph TD
    A[HTTP请求] --> B{是否公开接口?}
    B -->|是| C[直接发送请求]
    B -->|否| D[从cookies获取userId]
    D --> E{userId存在?}
    E -->|是| F[添加lucas-uniq-userId头]
    E -->|否| G[记录警告日志]
    F --> H[发送认证请求]
    G --> I[发送无认证请求]
    C --> J[记录请求日志]
    H --> J
    I --> J
    J --> K[处理响应]
    K --> L{请求成功?}
    L -->|是| M[记录成功日志]
    L -->|否| N[记录错误日志]
    M --> O[返回响应数据]
    N --> P[返回错误信息]
```

### 工作流程说明

1. **接口类型判断**：首先判断请求的接口是否为公开接口
   - 自动识别：根据URL路径（如`/public`、`/login`等）
   - 手动指定：通过`isPublic`参数明确指定

2. **用户身份获取**：对于需要认证的接口
   - 从cookies中解析session数据
   - 提取userId用于身份标识

3. **请求头注入**：根据用户身份信息
   - 添加`lucas-uniq-userId`请求头
   - 保留原有的自定义请求头

4. **请求执行**：发送HTTP请求到下游服务
   - 使用配置的axios实例
   - 自动处理超时和重试

5. **日志记录**：完整的请求生命周期日志
   - 请求开始：记录方法、URL、traceId
   - 请求完成：记录状态码、响应时间
   - 请求失败：记录错误信息和堆栈

## 系统架构

```mermaid
graph TB
    subgraph "客户端应用"
        A[React组件] --> B[useCoagentQuery/Mutation]
        C[Server Actions] --> D[CoagentServer]
    end

    subgraph "Coagent代理层"
        B --> E[SmartCoagentServer]
        D --> E
        E --> F[SessionBasedCoagentServer]
        F --> G[CoagentServer Core]
    end

    subgraph "Session模块"
        G --> H[getUserIdFromDefaultSession]
        H --> I[iron-session]
        I --> J[Encrypted Cookies]
    end

    subgraph "HTTP层"
        G --> K[Axios Instance]
        K --> L[添加认证头]
        L --> M[lucas-uniq-userId: user123]
    end

    subgraph "下游服务"
        M --> N[API Gateway]
        N --> O[Business Services]
    end

    subgraph "日志系统"
        G --> P[Request Logger]
        P --> Q[Trace ID]
        P --> R[Performance Metrics]
    end
```

### 架构说明

- **客户端层**：React组件和Server Actions通过不同方式调用Coagent
- **代理层**：智能判断接口类型，自动处理认证逻辑
- **Session集成**：与iron-session深度集成，自动获取用户身份
- **HTTP处理**：通过axios发送请求，自动添加认证头
- **日志追踪**：完整的请求生命周期监控和性能指标

## 文件结构

```
coagent/
├── service.ts              # 核心服务逻辑和适配器功能
├── client.ts               # React Query集成hooks
├── session-integration.ts  # Session模块集成工具
└── index.ts               # 统一导出
```

### 文件职责

- **service.ts**：核心的CoagentServer实现，包含HTTP请求处理、认证、日志记录和Axios适配器功能
- **client.ts**：提供React Query集成的hooks，包括useCoagentQuery和useCoagentMutation
- **session-integration.ts**：与最新session模块的集成工具，提供便捷的创建函数
- **index.ts**：统一导出所有公共接口

## 快速开始

以下是一个完整的使用示例，展示如何在Next.js应用中集成Coagent：

```typescript
// 1. 创建axios实例和Coagent服务器 (lib/coagent.ts)
import axios from 'axios';
import { createSmartCoagentServer } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';

const axiosInstance = axios.create({
  baseURL: process.env.API_BASE_URL || 'https://api.example.com',
  timeout: 10000,
});

export const coagentServer = createSmartCoagentServer(
  axiosInstance,
  () => cookies()
);

// 2. 在Server Action中使用 (actions/user.ts)
'use server';

export async function getUserProfile() {
  const response = await coagentServer.dispatch({
    type: 'GET',
    payload: {
      url: '/api/user/profile'
      // 自动识别为需要认证的接口，会添加 lucas-uniq-userId 头
    }
  });

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

// 3. 在React组件中使用 (components/UserProfile.tsx)
'use client';

import { useQuery } from '@tanstack/react-query';
import { getUserProfile } from '../actions/user';

export function UserProfile() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['user', 'profile'],
    queryFn: getUserProfile
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>Welcome, {data?.name}</h1>
      <p>Email: {data?.email}</p>
    </div>
  );
}
```

## 基本使用

### 1. 创建基于Session的Coagent服务器

```typescript
import { createSessionBasedCoagentServer } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';
import axios from 'axios';

// 创建axios实例
const axiosInstance = axios.create({
  baseURL: 'https://api.example.com',
  timeout: 10000,
});

// 创建Coagent服务器
const coagentServer = createSessionBasedCoagentServer(
  axiosInstance,
  () => cookies() // 提供cookies获取函数
);
```

### 2. 使用智能Coagent服务器（推荐）

```typescript
import { createSmartCoagentServer } from '@yai-investor-insight/shared-fe-core';

// 创建智能Coagent服务器，自动判断接口是否需要认证
const smartServer = createSmartCoagentServer(
  axiosInstance,
  () => cookies()
);

// 发送请求
const response = await smartServer.dispatch({
  type: 'POST',
  payload: {
    url: '/api/user/profile', // 自动识别为需要认证的接口
    data: { name: 'John' }
  }
});
```

### 3. 在React组件中使用

```typescript
'use client';

import { useCoagentQuery, useCoagentMutation, setGlobalCoagentServer } from '@yai-investor-insight/shared-fe-core';
import { useEffect } from 'react';

// 在应用初始化时设置全局服务器
function App() {
  useEffect(() => {
    const server = createSmartCoagentServer(axiosInstance, () => cookies());
    setGlobalCoagentServer(server);
  }, []);

  return <UserProfile />;
}

// 在组件中使用
function UserProfile() {
  // 查询用户信息
  const { data, isLoading, error } = useCoagentQuery({
    queryKey: ['user', 'profile'],
    endpoint: '/api/user/profile',
    method: 'GET'
  });

  // 更新用户信息
  const updateProfile = useCoagentMutation({
    endpoint: '/api/user/profile',
    method: 'PUT',
    onSuccess: () => {
      console.log('Profile updated successfully');
    }
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>{data?.data?.name}</h1>
      <button onClick={() => updateProfile.mutate({ name: 'New Name' })}>
        Update Profile
      </button>
    </div>
  );
}
```

### 4. Server Actions中使用

```typescript
'use server';

import { createSessionBasedCoagentServer } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';

export async function getUserData() {
  const server = createSessionBasedCoagentServer(axiosInstance, () => cookies());
  
  const response = await server.dispatch({
    type: 'GET',
    payload: {
      url: '/api/user/data'
      // isPublic: false (默认值，会自动添加认证头)
    }
  });

  return response.data;
}

export async function getPublicData() {
  const server = createSessionBasedCoagentServer(axiosInstance, () => cookies());
  
  const response = await server.dispatch({
    type: 'GET',
    payload: {
      url: '/api/public/data',
      isPublic: true // 明确指定为公开接口
    }
  });

  return response.data;
}
```

## 认证机制

### 自动添加的请求头

当用户已登录时，Coagent会自动添加以下请求头：

```
lucas-uniq-userId: user123
Content-Type: application/json
```

### 公开接口判断

以下路径会被自动识别为公开接口（不添加认证头）：
- `/health`
- `/ping`
- `/public`
- `/login`
- `/register`
- `/forgot-password`

### 手动指定接口类型

```typescript
// 明确指定为需要认证的接口
await server.dispatch({
  type: 'POST',
  payload: {
    url: '/api/private/data',
    isPublic: false,
    data: { key: 'value' }
  }
});

// 明确指定为公开接口
await server.dispatch({
  type: 'GET',
  payload: {
    url: '/api/some/endpoint',
    isPublic: true
  }
});
```

## 错误处理

```typescript
const response = await server.dispatch({
  type: 'POST',
  payload: {
    url: '/api/user/update',
    data: userData
  }
});

if (response.error) {
  console.error('Request failed:', response.error);
} else {
  console.log('Success:', response.data);
}
```

## 自定义请求头

```typescript
import { addCustomHeaders } from '@yai-investor-insight/shared-fe-core';

const response = await server.dispatch({
  type: 'POST',
  payload: {
    url: '/api/upload',
    data: formData,
    headers: addCustomHeaders(
      { 'Content-Type': 'multipart/form-data' },
      { 'X-Custom-Header': 'custom-value' }
    )
  }
});
```

## 日志记录

Coagent会自动记录所有API请求和响应：
- 请求开始时记录请求信息
- 请求完成时记录响应状态和耗时
- 请求失败时记录错误信息

日志包含traceId，便于追踪和调试。

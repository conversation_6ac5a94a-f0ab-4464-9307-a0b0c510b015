import { CoagentAction, CoagentResponse, CoagentServer } from './service';
import { useMutation, useQuery, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';

// 简化的 hooks 接口，用于直接使用
export interface UseCoagentQueryOptions<T> extends Omit<UseQueryOptions<CoagentResponse<T>, Error>, 'queryKey' | 'queryFn'> {
  queryKey: unknown[];
  endpoint: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  headers?: Record<string, string>;
  isPublic?: boolean;
}

export interface UseCoagentMutationOptions<T, TVariables> extends Omit<UseMutationOptions<CoagentResponse<T>, Error, TVariables>, 'mutationFn'> {
  endpoint: string;
  method?: 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  isPublic?: boolean;
}

// 独立的 hooks 导出，用于直接使用
// 全局 coagent 服务器实例
let globalCoagentServer: CoagentServer | null = null;

/**
 * 设置全局 coagent 服务器实例
 */
export function setGlobalCoagentServer(server: CoagentServer) {
  globalCoagentServer = server;
}

/**
 * 获取全局 coagent 服务器实例
 */
function getGlobalCoagentServer(): CoagentServer {
  if (!globalCoagentServer) {
    throw new Error('Global CoagentServer not configured. Please call setGlobalCoagentServer first.');
  }
  return globalCoagentServer;
}

export function useCoagentQuery<T>(options: UseCoagentQueryOptions<T>) {
  const { queryKey, endpoint, method = 'GET', data, headers, isPublic = false, ...queryOptions } = options;

  const action: CoagentAction = {
    type: method,
    payload: {
      url: endpoint,
      data,
      headers,
      isPublic
    }
  };

  return useQuery<CoagentResponse<T>, Error>({
    queryKey: queryKey,
    queryFn: () => getGlobalCoagentServer().dispatch(action),
    ...queryOptions,
  });
}

export function useCoagentMutation<T, TVariables = void>(options: UseCoagentMutationOptions<T, TVariables>) {
  const { endpoint, method = 'POST', headers, isPublic = false, ...mutationOptions } = options;
  const queryClient = useQueryClient();

  return useMutation<CoagentResponse<T>, Error, TVariables>({
    mutationFn: (variables: TVariables) => {
      const action: CoagentAction = {
        type: method,
        payload: {
          url: endpoint,
          data: variables,
          headers,
          isPublic
        }
      };
      return getGlobalCoagentServer().dispatch(action);
    },
    ...mutationOptions,
    onSuccess: (data: CoagentResponse<T>, variables: TVariables, context: unknown) => {
      if (mutationOptions?.onSuccess) {
        mutationOptions.onSuccess(data, variables, context);
      }
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: [endpoint] });
    },
  });
}
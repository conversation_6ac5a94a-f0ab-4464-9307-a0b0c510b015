import { AxiosInstance, AxiosRequestConfig } from 'axios';
import { generateTraceId, logApiRequest, logApiResponse } from '../log/helpers';

export interface CoagentAction {
  type: string;
  payload?: any;
}

export interface CoagentResponse<T = any> {
  data: T;
  error?: string;
}

/**
 * 用户身份获取函数类型
 * 从cookies或其他存储中获取用户ID
 */
export type GetUserIdFunction = () => Promise<string | null>;

/**
 * 请求头创建函数类型
 * 根据用户ID和其他信息创建请求头
 */
export type CreateHeadersFunction = (userId: string | null, existingHeaders?: Record<string, string>) => Promise<Record<string, string>>;

export interface CoagentServerOptions {
  /** 获取用户ID的函数 */
  getUserId?: GetUserIdFunction;
  /** 创建请求头的函数 */
  createHeaders?: CreateHeadersFunction;
}

export interface CoagentServer {
  dispatch: (action: CoagentAction) => Promise<CoagentResponse>;
}

/**
 * 标准化URL字符串
 */
function normalizeUrl(url: any): string {
  if (typeof url === 'string') {
    return url;
  }
  if (url && typeof url === 'object') {
    return url.url || url.path || url.endpoint || JSON.stringify(url);
  }
  return String(url || '');
}

/**
 * 构建完整URL
 */
function buildFullUrl(baseURL: string | undefined, url: string): string {
  if (!baseURL) return url;
  const cleanBaseURL = baseURL.replace(/\/$/, '');
  const cleanUrl = url.startsWith('/') ? url : '/' + url;
  return `${cleanBaseURL}${cleanUrl}`;
}

/**
 * 创建请求头，如果不是公开接口则注入用户身份信息
 */
async function createRequestHeaders(
  headers: Record<string, string> = {},
  isPublic: boolean = false,
  options?: CoagentServerOptions
): Promise<Record<string, string>> {
  let finalHeaders = { ...headers };

  // 如果不是公开接口且提供了认证相关函数，则注入用户身份信息
  if (!isPublic && options?.getUserId && options?.createHeaders) {
    try {
      const userId = await options.getUserId();
      finalHeaders = await options.createHeaders(userId, finalHeaders);
    } catch (error) {
      console.warn('Failed to get user ID or create headers:', error);
      // 继续执行请求，但不注入用户身份信息
    }
  }

  return finalHeaders;
}

export function createCoagentServer(axiosInstance: AxiosInstance, options?: CoagentServerOptions): CoagentServer {
  return {
    dispatch: async (action: CoagentAction) => {
      const traceId = generateTraceId();
      const startTime = Date.now();

      try {
        const { type, payload } = action;
        const { url, data, headers, isPublic = false, ...config } = payload || {};
        const method = type.toUpperCase();

        // 标准化URL
        const urlString = normalizeUrl(url);
        const fullUrl = buildFullUrl(axiosInstance.defaults.baseURL, urlString);
        
        // 记录请求日志
        await logApiRequest(fullUrl, { method }, traceId);

        // 创建最终的请求头
        const finalHeaders = await createRequestHeaders(headers, isPublic, options);
        
        let response;
        switch (method) {
          case 'POST':
            response = await axiosInstance.post(urlString, data, { headers: finalHeaders, ...config });
            break;
          case 'PUT':
            response = await axiosInstance.put(urlString, data, { headers: finalHeaders, ...config });
            break;
          case 'DELETE':
            response = await axiosInstance.delete(urlString, { headers: finalHeaders, ...config });
            break;
          case 'PATCH':
            response = await axiosInstance.patch(urlString, data, { headers: finalHeaders, ...config });
            break;
          case 'GET':
          default:
            response = await axiosInstance.get(urlString, { 
              headers: finalHeaders, 
              params: payload?.params, 
              ...config 
            });
            break;
        }
        
        // 记录成功响应日志（使用完整URL）
        const duration = Date.now() - startTime;
        const mockResponse = { ok: true, status: response.status, statusText: 'OK' } as Response;
        await logApiResponse(fullUrl, mockResponse, traceId, duration);
        
        return {
          data: response.data,
          error: undefined
        };
      } catch (error: any) {
        // 记录错误响应日志
        const duration = Date.now() - startTime;
        const status = error.response?.status || 500;
        const { type, payload } = action;
        const errorUrlString = normalizeUrl(payload?.url);
        const errorFullUrl = buildFullUrl(axiosInstance.defaults.baseURL, errorUrlString) || 'unknown';

        const mockErrorResponse = { ok: false, status, statusText: 'Error' } as Response;
        await logApiResponse(errorFullUrl, mockErrorResponse, traceId, duration);

        return {
          data: null,
          error: error.message || 'Unknown error occurred'
        };
      }
    }
  };
}

/**
 * 创建Coagent适配器，将Axios配置转换为Coagent请求
 * 这是原adapter.ts的功能，现在合并到service.ts中
 */
export function createCoagentAdapter(server?: CoagentServer) {
  return (config: AxiosRequestConfig) => {
    if (!server) {
      throw new Error('CoagentServer is required for createCoagentAdapter');
    }

    const action: CoagentAction = {
      type: 'axios',
      payload: {
        method: config.method,
        url: config.url,
        data: config.data,
        params: config.params,
        headers: config.headers
      }
    };

    return server.dispatch(action).then(response => {
      if (response.error) {
        return Promise.reject(new Error(response.error));
      }
      return response.data;
    });
  };
}
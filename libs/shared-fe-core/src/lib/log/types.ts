/**
 * YAI LogLayer 配置类型定义
 */

export interface YAILogLayerConfig {
  sls: {
    endpoint: string;
    accessKeyId: string;
    accessKeySecret: string;
    project: string;
    logstore: string;
    region: string;
  };
  app: {
    name: string;
    environment: 'development' | 'staging' | 'production';
    version: string;
  };
  features: {
    enableBatch: boolean;
    batchSize: number;
    flushInterval: number;
    enableRetry: boolean;
    maxRetries: number;
    enableConsole: boolean; // 是否启用控制台输出
    enableSls: boolean; // 是否启用 SLS 输出
    enableFile?: boolean; // 是否启用文件输出（服务端和客户端）
  };
}

export interface LogContext {
  traceId?: string;
  requestId?: string;
  userId?: string;
  component?: string;
  action?: string;
  sessionId?: string;
  [key: string]: any;
}

export interface LoggerInterface {
  debug(message: string, data?: any): Promise<void>;
  info(message: string, data?: any): Promise<void>;
  warn(message: string, data?: any): Promise<void>;
  error(message: string, data?: any): Promise<void>;
}
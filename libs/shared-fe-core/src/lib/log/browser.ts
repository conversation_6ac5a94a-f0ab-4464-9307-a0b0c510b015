/**
 * 浏览器端日志系统 - 统一接口
 */
import { createBrowserLogger } from '@yai-loglayer/browser';
import type { YAILogLayerConfig, LoggerInterface } from './types';

// 简化的环境变量读取
const getEnv = (key: string, defaultValue = ''): string => {
  try {
    return process.env[key] || (typeof window !== 'undefined' && (window as any).__NEXT_DATA__?.env?.[key]) || defaultValue;
  } catch {
    return defaultValue;
  }
};

// 创建配置
const createConfig = (): YAILogLayerConfig => {
  const nodeEnv = getEnv('NODE_ENV', 'development');
  const slsEnabled = getEnv('NEXT_PUBLIC_SLS_ENABLED') === 'true';
  const receiverEnabled = getEnv('NEXT_PUBLIC_LOG_RECEIVER_ENABLED', 'true') === 'true';
  
  // 检查SLS配置是否完整
  const slsConfigComplete = [
    'NEXT_PUBLIC_SLS_ENDPOINT',
    'NEXT_PUBLIC_SLS_ACCESS_KEY_ID', 
    'NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET',
    'NEXT_PUBLIC_SLS_PROJECT',
    'NEXT_PUBLIC_SLS_LOGSTORE'
  ].every(key => getEnv(key));

  return {
    app: {
      name: getEnv('NEXT_PUBLIC_SERVICE_NAME', 'yai-investor-insight-webapp'),
      environment: nodeEnv as any,
      version: getEnv('NEXT_PUBLIC_APP_VERSION', '1.0.0')
    },
    sls: {
      endpoint: getEnv('NEXT_PUBLIC_SLS_ENDPOINT'),
      accessKeyId: getEnv('NEXT_PUBLIC_SLS_ACCESS_KEY_ID'),
      accessKeySecret: getEnv('NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET'),
      project: getEnv('NEXT_PUBLIC_SLS_PROJECT'),
      logstore: getEnv('NEXT_PUBLIC_SLS_LOGSTORE'),
      region: getEnv('NEXT_PUBLIC_SLS_REGION', 'cn-beijing')
    },
    features: {
      enableBatch: true,
      batchSize: 10,
      flushInterval: 5000,
      enableRetry: true,
      maxRetries: 2,
      enableConsole: nodeEnv === 'development' || !slsConfigComplete,
      enableSls: slsEnabled && slsConfigComplete,
      enableFile: receiverEnabled
    }
  };
};

const config = createConfig();

// 日志器实例
let loggerInstance: any = null;
let initPromise: Promise<any> | null = null;

const initLogger = async () => {
  if (initPromise) return initPromise;
  
  if (!loggerInstance) {
    initPromise = (async () => {
      try {
        const browserConfig: any = { level: 'debug', outputs: {} };
        
        // 控制台输出
        if (config.features.enableConsole) {
          browserConfig.outputs.console = { enabled: true, colorized: true };
        }
        
        // HTTP接收器（文件输出）
        if (config.features.enableFile) {
          browserConfig.outputs.http = {
            enabled: true,
            endpoint: '/api/logs/client',
            method: 'POST',
            batchSize: config.features.batchSize,
            retryAttempts: config.features.maxRetries,
            headers: { 'Content-Type': 'application/json' }
          };
        }
        
        // SLS输出
        if (config.features.enableSls && config.sls.endpoint) {
          browserConfig.outputs.sls = {
            enabled: true,
            endpoint: config.sls.endpoint,
            method: 'POST',
            batchSize: config.features.batchSize,
            retryAttempts: config.features.maxRetries,
            headers: {
              'Authorization': `${config.sls.accessKeyId}:${config.sls.accessKeySecret}`,
              'Content-Type': 'application/json'
            }
          };
        }
        
        // 确保至少有控制台输出
        if (Object.keys(browserConfig.outputs).length === 0) {
          browserConfig.outputs.console = { enabled: true, colorized: true };
        }
        
        loggerInstance = await createBrowserLogger(browserConfig);
        return loggerInstance;
      } catch (error) {
        console.error('Browser logger init failed:', error);
        // 创建fallback logger
        loggerInstance = {
          debug: (msg: string) => console.debug(msg),
          info: (msg: string) => console.info(msg),
          warn: (msg: string) => console.warn(msg),
          error: (msg: string) => console.error(msg)
        };
        return loggerInstance;
      }
    })();
  }
  
  return initPromise;
};

// 统一日志接口
export const logger: LoggerInterface = {
  debug: async (message: string, data?: any) => {
    const log = await initLogger();
    log.debug(message, { data, service: config.app.name, environment: 'client' });
  },
  
  info: async (message: string, data?: any) => {
    const log = await initLogger();
    log.info(message, { data, service: config.app.name, environment: 'client' });
  },
  
  warn: async (message: string, data?: any) => {
    const log = await initLogger();
    log.warn(message, { data, service: config.app.name, environment: 'client' });
  },
  
  error: async (message: string, data?: any) => {
    const log = await initLogger();
    log.error(message, { data, service: config.app.name, environment: 'client' });
  }
};

export default logger;
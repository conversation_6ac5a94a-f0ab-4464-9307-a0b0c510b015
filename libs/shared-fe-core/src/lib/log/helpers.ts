/**
 * 日志工具函数 - 核心功能
 */
import { logger } from './browser';

/**
 * 生成唯一的追踪ID
 */
export function generateTraceId(): string {
  return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * API请求日志
 */
export async function logApiRequest(url: string, options: RequestInit, traceId?: string): Promise<void> {
  await logger.info('API Request', {
    url,
    method: options.method || 'GET',
    traceId: traceId || generateTraceId(),
    type: 'api_request'
  });
}

/**
 * API响应日志
 */
export async function logApiResponse(url: string, response: Response, traceId: string, duration?: number): Promise<void> {
  const level = response.ok ? 'info' : 'error';
  await logger[level]('API Response', {
    url,
    status: response.status,
    statusText: response.statusText,
    duration: duration ? `${duration}ms` : undefined,
    traceId,
    type: 'api_response'
  });
}

/**
 * 用户行为日志
 */
export async function logUserAction(action: string, element?: string, data?: Record<string, unknown>): Promise<void> {
  await logger.info('User Action', {
    action,
    element,
    data,
    type: 'user_action'
  });
}

/**
 * 错误日志
 */
export async function logClientError(error: Error, context?: Record<string, unknown>): Promise<void> {
  await logger.error('Client Error', {
    error: error.message,
    stack: error.stack,
    context,
    type: 'client_error'
  });
}

/**
 * 组件挂载日志
 */
export async function logComponentMount(componentName: string, props?: Record<string, unknown>): Promise<void> {
  await logger.debug('Component Mount', {
    component: componentName,
    props,
    type: 'component_mount'
  });
}
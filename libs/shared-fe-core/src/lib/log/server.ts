/**
 * 服务端日志系统 - 统一文件
 */
import { createServerLogger } from '@yai-loglayer/server';
import type { YAILogLayerConfig, LoggerInterface } from './types';
import path from 'path';
import fs from 'fs';

// 创建配置
const createConfig = (): YAILogLayerConfig => {
  const nodeEnv = process.env['NODE_ENV'] || 'development';
  const slsEnabled = process.env['NEXT_PUBLIC_SLS_ENABLED'] === 'true';
  
  // 检查SLS配置是否完整
  const slsConfigComplete = [
    'NEXT_PUBLIC_SLS_ENDPOINT',
    'NEXT_PUBLIC_SLS_ACCESS_KEY_ID',
    'NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET', 
    'NEXT_PUBLIC_SLS_PROJECT',
    'NEXT_PUBLIC_SLS_LOGSTORE'
  ].every(key => process.env[key]);

  return {
    app: {
      name: process.env['NEXT_PUBLIC_SERVICE_NAME'] || 'yai-investor-insight-webapp',
      environment: nodeEnv as any,
      version: process.env['NEXT_PUBLIC_APP_VERSION'] || '1.0.0'
    },
    sls: {
      endpoint: process.env['NEXT_PUBLIC_SLS_ENDPOINT'] || '',
      accessKeyId: process.env['NEXT_PUBLIC_SLS_ACCESS_KEY_ID'] || '',
      accessKeySecret: process.env['NEXT_PUBLIC_SLS_ACCESS_KEY_SECRET'] || '',
      project: process.env['NEXT_PUBLIC_SLS_PROJECT'] || '',
      logstore: process.env['NEXT_PUBLIC_SLS_LOGSTORE'] || '',
      region: process.env['NEXT_PUBLIC_SLS_REGION'] || 'cn-beijing'
    },
    features: {
      enableBatch: true,
      batchSize: 10,
      flushInterval: 1000,
      enableRetry: true,
      maxRetries: 3,
      enableConsole: nodeEnv === 'development' || !slsConfigComplete,
      enableSls: slsEnabled && slsConfigComplete,
      enableFile: true
    }
  };
};

// 获取日志文件路径
const getLogFilePath = (): string => {
  // 从当前目录向上查找workspace根目录
  let currentDir = process.cwd();
  while (currentDir !== path.dirname(currentDir)) {
    const packageJsonPath = path.join(currentDir, 'package.json');
    const nxJsonPath = path.join(currentDir, 'nx.json');
    
    if (fs.existsSync(packageJsonPath) && fs.existsSync(nxJsonPath)) {
      const logsDir = path.join(currentDir, 'logs');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }
      return path.join(logsDir, 'webapp.server.log');
    }
    currentDir = path.dirname(currentDir);
  }
  
  // 回退方案
  const fallbackLogsDir = path.join(process.cwd(), 'logs');
  if (!fs.existsSync(fallbackLogsDir)) {
    fs.mkdirSync(fallbackLogsDir, { recursive: true });
  }
  return path.join(fallbackLogsDir, 'webapp.server.log');
};

const config = createConfig();

// 日志器实例
let loggerInstance: any = null;
let initPromise: Promise<any> | null = null;

const initLogger = async () => {
  if (initPromise) return initPromise;

  if (!loggerInstance) {
    initPromise = (async () => {
      try {
        const outputs: any[] = [];
        
        // 控制台输出
        if (config.features.enableConsole) {
          outputs.push({ type: 'stdout' });
        }
        
        // 文件输出
        if (config.features.enableFile) {
          const logFilePath = getLogFilePath();
          outputs.push({
            type: 'file',
            config: {
              dir: path.dirname(logFilePath),
              filename: path.basename(logFilePath),
              maxSize: '10MB',
              maxFiles: 5
            }
          });
        }

        // SLS输出
        if (config.features.enableSls && config.sls.endpoint) {
          outputs.push({
            type: 'sls',
            config: {
              endpoint: config.sls.endpoint,
              accessKeyId: config.sls.accessKeyId,
              accessKeySecret: config.sls.accessKeySecret,
              project: config.sls.project,
              logstore: config.sls.logstore,
              region: config.sls.region
            }
          });
        }

        // 确保至少有控制台输出
        if (outputs.length === 0) {
          outputs.push({ type: 'stdout' });
        }

        const loggerConfig = {
          level: { default: 'debug' as const },
          server: { outputs },
          client: { outputs: [] }
        };
        
        loggerInstance = await createServerLogger(config.app.name, loggerConfig);
        return loggerInstance;
      } catch (error) {
        console.error('Server logger init failed:', error);
        // 创建fallback logger
        loggerInstance = {
          debug: (msg: string) => console.debug(msg),
          info: (msg: string) => console.info(msg),
          warn: (msg: string) => console.warn(msg),
          error: (msg: string) => console.error(msg)
        };
        return loggerInstance;
      }
    })();
  }

  return initPromise;
};

// 统一日志接口
export const logger: LoggerInterface = {
  debug: async (message: string, data?: any) => {
    const log = await initLogger();
    log.debug(message, { data, service: config.app.name, environment: 'server' });
  },
  
  info: async (message: string, data?: any) => {
    const log = await initLogger();
    log.info(message, { data, service: config.app.name, environment: 'server' });
  },
  
  warn: async (message: string, data?: any) => {
    const log = await initLogger();
    log.warn(message, { data, service: config.app.name, environment: 'server' });
  },
  
  error: async (message: string, data?: any) => {
    const log = await initLogger();
    log.error(message, { data, service: config.app.name, environment: 'server' });
  }
};

// 向后兼容
export const serverLogger = logger;

// 工具函数
export function generateServerTraceId(): string {
  return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// 别名导出，保持与客户端一致的API
export const generateTraceId = generateServerTraceId;

// API路由日志
export async function logApiRouteRequest(method: string, path: string, traceId: string, data?: any) {
  await logger.info('API Route 请求', { method, path, traceId, data, type: 'api_route_request' });
}

export async function logApiRouteResponse(method: string, path: string, traceId: string, status: number, duration: number, error?: any) {
  const logData = { method, path, traceId, status, duration, type: 'api_route_response' };
  
  if (error) {
    await logger.error('API Route 失败', { ...logData, error: error.message, stack: error.stack });
  } else if (status >= 400) {
    await logger.warn('API Route 错误', logData);
  } else {
    await logger.info('API Route 成功', logData);
  }
}

export default logger;
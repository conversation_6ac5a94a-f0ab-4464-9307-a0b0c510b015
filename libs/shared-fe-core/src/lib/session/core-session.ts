/**
 * 核心Session功能
 * 专注于登录保存cookies和解析用户信息的核心能力
 */

import { getIronSession } from 'iron-session';
import { SessionData, SessionResult, SessionOptions, getDefaultSessionOptions } from './types';

/**
 * 保存登录用户信息到cookies
 * 这是登录成功后调用的核心函数
 * 
 * @param cookiesInstance - cookies实例 (通常是 await cookies())
 * @param userData - 用户登录信息
 * @param sessionOptions - 可选的session配置
 */
export async function saveLoginSession(
  cookiesInstance: any,
  userData: {
    userId: string;
    phone: string;
    userName?: string;
    token?: string;
  },
  sessionOptions?: SessionOptions
): Promise<void> {
  try {
    const options = sessionOptions || getDefaultSessionOptions();
    const session = await getIronSession<SessionData>(cookiesInstance, options);
    
    // 保存用户信息到session
    session.isLoggedIn = true;
    session.userId = userData.userId;
    session.phone = userData.phone;
    session.userName = userData.userName;
    session.token = userData.token;
    
    // 保存到cookies
    await session.save();
    
    console.log('Login session saved successfully for user:', userData.userId);
  } catch (error) {
    console.error('Failed to save login session:', error);
    throw new Error(`Failed to save login session: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * 从cookies获取当前登录用户信息
 * 这是获取用户信息的核心函数
 * 
 * @param cookiesInstance - cookies实例 (通常是 await cookies())
 * @param sessionOptions - 可选的session配置
 * @returns 用户信息或null
 */
export async function getCurrentUserFromCookies(
  cookiesInstance: any,
  sessionOptions?: SessionOptions
): Promise<SessionResult> {
  try {
    const options = sessionOptions || getDefaultSessionOptions();
    const session = await getIronSession<SessionData>(cookiesInstance, options);
    
    if (session.isLoggedIn && session.userId) {
      return {
        isLoggedIn: true,
        userId: session.userId,
        phone: session.phone,
        userName: session.userName,
        token: session.token
      };
    }
    
    return { isLoggedIn: false };
  } catch (error) {
    console.warn('Failed to get current user from cookies:', error);
    return { isLoggedIn: false };
  }
}

/**
 * 检查用户是否已登录
 * 
 * @param cookiesInstance - cookies实例
 * @param sessionOptions - 可选的session配置
 * @returns 是否已登录
 */
export async function isUserLoggedIn(
  cookiesInstance: any,
  sessionOptions?: SessionOptions
): Promise<boolean> {
  try {
    const userInfo = await getCurrentUserFromCookies(cookiesInstance, sessionOptions);
    return userInfo.isLoggedIn;
  } catch (error) {
    console.warn('Failed to check login status:', error);
    return false;
  }
}

/**
 * 清除登录session（退出登录）
 * 
 * @param cookiesInstance - cookies实例
 * @param sessionOptions - 可选的session配置
 */
export async function clearLoginSession(
  cookiesInstance: any,
  sessionOptions?: SessionOptions
): Promise<void> {
  try {
    const options = sessionOptions || getDefaultSessionOptions();
    const session = await getIronSession<SessionData>(cookiesInstance, options);
    
    // 清除所有session数据
    session.isLoggedIn = false;
    session.userId = '';
    session.phone = '';
    session.userName = undefined;
    session.token = undefined;
    
    // 保存清空的session
    await session.save();
    
    console.log('Login session cleared successfully');
  } catch (error) {
    console.error('Failed to clear login session:', error);
    throw new Error(`Failed to clear login session: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * 获取用户ID（用于Coagent等需要用户ID的场景）
 * 
 * @param cookiesInstance - cookies实例
 * @param sessionOptions - 可选的session配置
 * @returns 用户ID或null
 */
export async function getUserIdFromCookies(
  cookiesInstance: any,
  sessionOptions?: SessionOptions
): Promise<string | null> {
  try {
    const userInfo = await getCurrentUserFromCookies(cookiesInstance, sessionOptions);
    return userInfo.isLoggedIn ? userInfo.userId || null : null;
  } catch (error) {
    console.warn('Failed to get user ID from cookies:', error);
    return null;
  }
}

/**
 * 更新用户信息（部分更新）
 * 
 * @param cookiesInstance - cookies实例
 * @param updates - 要更新的用户信息
 * @param sessionOptions - 可选的session配置
 */
export async function updateUserSession(
  cookiesInstance: any,
  updates: {
    userName?: string;
    token?: string;
    phone?: string;
  },
  sessionOptions?: SessionOptions
): Promise<void> {
  try {
    const options = sessionOptions || getDefaultSessionOptions();
    const session = await getIronSession<SessionData>(cookiesInstance, options);
    
    if (!session.isLoggedIn) {
      throw new Error('No active session to update');
    }

    // 应用更新
    if (updates.userName !== undefined) session.userName = updates.userName;
    if (updates.token !== undefined) session.token = updates.token;
    if (updates.phone !== undefined) session.phone = updates.phone;
    
    // 保存更新
    await session.save();
    
    console.log('User session updated successfully for user:', session.userId);
  } catch (error) {
    console.error('Failed to update user session:', error);
    throw new Error(`Failed to update user session: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// 便捷的默认配置函数
export const coreSession = {
  /**
   * 使用默认配置保存登录session
   */
  saveLogin: (cookiesInstance: any, userData: Parameters<typeof saveLoginSession>[1]) => 
    saveLoginSession(cookiesInstance, userData),
  
  /**
   * 使用默认配置获取当前用户
   */
  getCurrentUser: (cookiesInstance: any) => 
    getCurrentUserFromCookies(cookiesInstance),
  
  /**
   * 使用默认配置检查登录状态
   */
  isLoggedIn: (cookiesInstance: any) => 
    isUserLoggedIn(cookiesInstance),
  
  /**
   * 使用默认配置清除登录session
   */
  clearLogin: (cookiesInstance: any) => 
    clearLoginSession(cookiesInstance),
  
  /**
   * 使用默认配置获取用户ID
   */
  getUserId: (cookiesInstance: any) => 
    getUserIdFromCookies(cookiesInstance),
  
  /**
   * 使用默认配置更新用户session
   */
  updateUser: (cookiesInstance: any, updates: Parameters<typeof updateUserSession>[1]) => 
    updateUserSession(cookiesInstance, updates)
};

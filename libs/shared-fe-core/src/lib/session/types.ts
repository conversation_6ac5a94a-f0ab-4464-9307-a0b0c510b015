/**
 * Session管理核心类型定义
 * 专注于登录保存cookies和解析用户信息
 */

export interface SessionData {
  userId: string;
  phone: string;
  isLoggedIn: boolean;
  userName?: string;
  token?: string;
}

export interface SessionOptions {
  password: string;
  cookieName: string;
  cookieOptions: {
    secure: boolean;
    httpOnly: boolean;
    maxAge: number;
    sameSite: 'lax' | 'strict' | 'none';
  };
}

/**
 * 获取默认会话配置
 * 统一管理session配置和密钥
 */
export function getDefaultSessionOptions(): SessionOptions {
  return {
    password: process.env['SECRET_COOKIE_PASSWORD'] || 'complex_password_at_least_32_characters_long_for_yai_investor_insight',
    cookieName: 'yai-session',
    cookieOptions: {
      secure: process.env['NODE_ENV'] === 'production',  // HTTPS only in production
      httpOnly: true,                                 // 防止 XSS 攻击
      maxAge: 1000 * 60 * 60 * 24 * 7,               // 7 天有效期
      sameSite: 'lax',                                // CSRF 保护
    },
  };
}

export interface SessionResult {
  isLoggedIn: boolean;
  userId?: string;
  phone?: string;
  userName?: string;
  token?: string;
}

export interface LoginResult {
  success: boolean;
  error?: string;
}


/**
 * Session 工具函数
 * 提供通用的 iron-session 操作功能，专注于核心的用户信息获取
 */

import { getIronSession } from 'iron-session';
import { SessionData, SessionOptions, getDefaultSessionOptions } from './types';

/**
 * 从 session 中获取用户 ID 的通用函数
 *
 * @param sessionOptions - session 配置选项
 * @param cookiesInstance - cookies 实例 (通常是 await cookies())
 * @returns 用户 ID 或 null
 */
export async function getUserIdFromSession(
  sessionOptions: SessionOptions,
  cookiesInstance: any
): Promise<string | null> {
  try {
    const session = await getIronSession<SessionData>(cookiesInstance, sessionOptions);

    if (session.isLoggedIn && session.userId) {
      return session.userId;
    }

    return null;
  } catch (error) {
    console.warn('Failed to get user ID from session:', error);
    return null;
  }
}

/**
 * 使用默认配置从 session 中获取用户 ID
 *
 * @param cookiesInstance - cookies 实例 (通常是 await cookies())
 * @returns 用户 ID 或 null
 */
export async function getUserIdFromDefaultSession(
  cookiesInstance: any
): Promise<string | null> {
  const sessionOptions = getDefaultSessionOptions();
  return getUserIdFromSession(sessionOptions, cookiesInstance);
}





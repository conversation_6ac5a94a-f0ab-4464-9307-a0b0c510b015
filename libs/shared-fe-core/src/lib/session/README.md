# Session 核心功能

专注于登录保存cookies和解析用户信息的核心能力。

## 核心功能

### 1. 保存登录信息到cookies

```typescript
import { saveLoginSession } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';

// 在Server Action中使用
export async function handleLogin(phone: string, code: string) {
  // 调用认证API
  const authResponse = await authWithMobile({ mobile: phone, code });
  
  if (authResponse.success) {
    const cookiesInstance = await cookies();
    
    // 保存用户信息到cookies
    await saveLoginSession(cookiesInstance, {
      userId: authResponse.data.user.id,
      phone: authResponse.data.user.phone,
      userName: authResponse.data.user.name,
      token: authResponse.data.token
    });
    
    return { success: true };
  }
  
  return { success: false, error: '登录失败' };
}
```

### 2. 从cookies获取用户信息

```typescript
import { getCurrentUserFromCookies } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';

// 在Server Action中使用
export async function getCurrentUser() {
  const cookiesInstance = await cookies();
  return await getCurrentUserFromCookies(cookiesInstance);
}

// 在页面组件中使用
export default async function ProfilePage() {
  const user = await getCurrentUser();
  
  if (!user.isLoggedIn) {
    redirect('/login');
  }
  
  return (
    <div>
      <h1>欢迎, {user.userName}</h1>
      <p>用户ID: {user.userId}</p>
      <p>手机号: {user.phone}</p>
    </div>
  );
}
```

### 3. 检查登录状态

```typescript
import { isUserLoggedIn } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';

// 在middleware中使用
export async function middleware(request: NextRequest) {
  const cookiesInstance = await cookies();
  const isLoggedIn = await isUserLoggedIn(cookiesInstance);
  
  if (!isLoggedIn && request.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
}
```

### 4. 退出登录

```typescript
import { clearLoginSession } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';

// 在Server Action中使用
export async function handleLogout() {
  const cookiesInstance = await cookies();
  await clearLoginSession(cookiesInstance);
  return { success: true };
}
```

### 5. 获取用户ID（用于API调用）

```typescript
import { getUserIdFromCookies } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';

// 在API路由中使用
export async function GET() {
  const cookiesInstance = await cookies();
  const userId = await getUserIdFromCookies(cookiesInstance);
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // 使用userId调用其他API
  const data = await fetchUserData(userId);
  return NextResponse.json(data);
}
```

### 6. 便捷的coreSession对象

```typescript
import { coreSession } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';

export async function someServerAction() {
  const cookiesInstance = await cookies();
  
  // 使用便捷方法
  const user = await coreSession.getCurrentUser(cookiesInstance);
  const isLoggedIn = await coreSession.isLoggedIn(cookiesInstance);
  const userId = await coreSession.getUserId(cookiesInstance);
  
  // 保存登录信息
  await coreSession.saveLogin(cookiesInstance, {
    userId: 'user123',
    phone: '13800138000',
    userName: '测试用户'
  });
  
  // 清除登录信息
  await coreSession.clearLogin(cookiesInstance);
}
```

## 类型定义

```typescript
interface SessionData {
  userId: string;
  phone: string;
  isLoggedIn: boolean;
  userName?: string;
  token?: string;
}

interface SessionResult {
  isLoggedIn: boolean;
  userId?: string;
  phone?: string;
  userName?: string;
  token?: string;
}

interface LoginResult {
  success: boolean;
  error?: string;
}
```

## 环境变量

需要设置以下环境变量：

```env
SECRET_COOKIE_PASSWORD=your-secret-key-at-least-32-characters-long
```

## 注意事项

1. 所有函数都需要在服务端环境中使用（Server Actions、API Routes、middleware等）
2. cookies实例通常通过 `await cookies()` 获取
3. 默认session有效期为7天
4. 生产环境下cookies会自动启用secure模式

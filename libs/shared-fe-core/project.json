{"name": "shared-fe-core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared-fe-core/src", "projectType": "library", "tags": ["scope:shared", "type:fe"], "// targets": "to see all targets run: nx show project shared-fe-core --web", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared-fe-core", "main": "libs/shared-fe-core/src/index.ts", "tsConfig": "libs/shared-fe-core/tsconfig.json", "assets": ["libs/shared-fe-core/*.md"]}}}}
# Session管理迁移指南

## 概述

本指南说明如何将webapp中的session管理迁移到统一的shared-fe-core库中。

## 迁移步骤

### 1. 更新webapp中的session使用

#### 替换现有的useSession Hook

**原来的代码 (apps/web-app/src/hooks/useSession.ts):**
```typescript
import { useSession } from '@/hooks/useSession';

function MyComponent() {
  const { session, isLoading, isLoggedIn, refetch, logout } = useSession();
  // ...
}
```

**新的代码:**
```typescript
import { useSession } from '@yai-investor-insight/shared-fe-core';
import { getCurrentUser, logout as logoutAction, loginWithCode, isAuthenticated } from '@/app/actions/sessionActions';

function MyComponent() {
  const { session, isLoading, isLoggedIn, refetch, logout } = useSession({
    authMethod: 'cookies',
    serverActions: {
      loginWithCode,
      getCurrentUser,
      logout: logoutAction,
      isAuthenticated
    }
  });
  // ...
}
```

#### 保持现有Server Actions（cookies模式）

**保持 apps/web-app/src/app/actions/sessionActions.ts 现有实现:**
```typescript
// 保持现有的sessionActions实现不变
// 只需要在React组件中使用新的useSession Hook
'use server';

import { authWithMobile, type AuthMobileArgVO } from '@yai-investor-insight/user-account-fe';

// 保持现有的server actions实现
export async function loginWithCode(phone: string, code: string) {
  // 现有的登录逻辑保持不变
  const payload: AuthMobileArgVO = { 
    mobile: phone, 
    code,
    productLine: 'investor_insight'
  };
  return await authWithMobile(payload);
}

export async function getCurrentUser() {
  // 现有的获取用户信息逻辑保持不变
  return await getUserProfile();
}

export async function logout() {
  // 现有的登出逻辑保持不变
  return await handleLogout();
}

export async function isAuthenticated() {
  // 现有的认证检查逻辑保持不变
  const user = await getCurrentUser();
  return user.isLoggedIn;
}
```

### 2. 更新user-account-fe模块

#### 使用统一的session管理器进行写入操作

**在user-account-fe中创建session管理器:**
```typescript
// libs/user-account-fe/src/lib/session/userAccountSession.ts
import { createSessionManager } from '@yai-investor-insight/shared-fe-core';
import { authWithMobile, getUserProfile } from '../api';

export const userAccountSessionManager = createSessionManager('localStorage', {
  loginWithCode: async (phone: string, code: string) => {
    try {
      const response = await authWithMobile({ mobile: phone, code, productLine: 'investor_insight' });
      return { success: response.status === 200 };
    } catch (error) {
      return { success: false, error: '登录失败' };
    }
  },
  getUserProfile: async () => {
    return await getUserProfile();
  }
});

// 导出给其他模块使用的接口
export async function handleUserLogin(phone: string, code: string) {
  return await userAccountSessionManager.login(phone, code);
}

export async function handleUserLogout() {
  return await userAccountSessionManager.logout();
}

export async function updateUserSession(updates: any) {
  return await userAccountSessionManager.updateSession(updates);
}
```

### 3. 更新其他模块（只读查询）

#### 在其他功能模块中使用session查询

```typescript
// 在任何需要读取session的组件中
import { useSessionQuery } from '@yai-investor-insight/shared-fe-core';

function SomeFeatureComponent() {
  const { session, isLoggedIn, isLoading } = useSessionQuery('localStorage');
  
  if (isLoading) return <div>Loading...</div>;
  
  if (!isLoggedIn) {
    return <div>请先登录</div>;
  }
  
  return (
    <div>
      <h1>功能页面</h1>
      <p>当前用户: {session?.userName}</p>
      <p>用户ID: {session?.userId}</p>
    </div>
  );
}
```

### 4. 清理旧代码

#### 删除原有的session管理文件

1. 删除 `apps/web-app/src/hooks/useSession.ts`
2. 删除 `libs/user-account-fe/src/lib/hooks/useSession.ts`
3. 更新所有导入路径

#### 更新依赖

确保在需要使用session管理的模块中添加对shared-fe-core的依赖：

```json
{
  "dependencies": {
    "@yai-investor-insight/shared-fe-core": "*"
  }
}
```

## 职责分工

### shared-fe-core
- 提供统一的session管理接口
- 支持多种存储方式（cookies、localStorage）
- 提供React Hooks和管理器类
- 处理session的读写、验证等核心逻辑

### user-account-fe
- 负责用户认证相关的业务逻辑
- 调用shared-fe-core的接口进行session写入
- 处理登录、注册、用户信息更新等操作

### 其他模块
- 只读取session信息
- 使用useSessionQuery或session管理器的查询方法
- 根据session状态调整UI和功能

## 注意事项

1. **存储方式选择**: 根据应用需求选择cookies或localStorage
2. **环境变量**: cookies模式需要配置SECRET_COOKIE_PASSWORD
3. **错误处理**: 所有session操作都包含完整的错误处理
4. **类型安全**: 使用TypeScript确保类型安全
5. **向后兼容**: 新的API与现有使用方式保持兼容

## 测试

迁移完成后，请测试以下功能：

1. 用户登录/登出
2. Session状态在页面刷新后的持久化
3. 多个组件中session状态的同步
4. 错误情况的处理（网络错误、token过期等）

## 支持

如有问题，请参考：
- [SESSION_MANAGEMENT.md](./SESSION_MANAGEMENT.md) - 详细使用文档
- [examples/user-account-integration.ts](./examples/user-account-integration.ts) - 集成示例
# Shared Frontend Core Library

统一的前端核心库，提供session管理、日志记录等共享功能。

## Session管理

### 概述

本库提供了统一的session管理解决方案，支持多种存储方式：
- **Cookies**: 使用iron-session进行服务端安全存储
- **LocalStorage**: 客户端本地存储

### 基本用法

#### 1. 使用React Hook（推荐）

```typescript
import { useSession, useSessionQuery } from '@yai-investor-insight/shared-fe-core';

// session查询（所有模块都可以使用）
function MyComponent() {
  const { session, isLoading, isLoggedIn, refetch, logout } = useSession({
    authMethod: 'localStorage', // 或 'cookies'
    serverActions: { // cookies模式下需要提供server actions
      getCurrentUser,
      logout: logoutAction,
      isAuthenticated
    }
  });

  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      {isLoggedIn ? (
        <div>
          <p>欢迎, {session?.userName}</p>
          <button onClick={logout}>退出登录</button>
        </div>
      ) : (
        <div>请登录</div>
      )}
    </div>
  );
}

// 仅查询session信息（其他模块使用）
function OtherComponent() {
  const { session, isLoggedIn } = useSessionQuery('localStorage');
  
  return (
    <div>
      {isLoggedIn && <p>用户ID: {session?.userId}</p>}
    </div>
  );
}
```

#### 2. 直接使用Session管理器

```typescript
import { createSessionManager, SessionData } from '@yai-investor-insight/shared-fe-core';

// 创建session管理器
const sessionManager = createSessionManager('localStorage');

// 保存session（通常在登录成功后调用）
const sessionData: SessionData = {
  isLoggedIn: true,
  userId: '123',
  phone: '13800138000',
  userName: 'User',
  token: 'jwt-token'
};
await sessionManager.saveSession(sessionData);

// 查询session
const user = await sessionManager.getCurrentUser();
const isAuth = await sessionManager.isAuthenticated();

// 清除session
await sessionManager.clearSession();

// 更新session
await sessionManager.updateSession({ userName: 'New Name' });
```

#### 3. 与现有Server Actions集成（cookies模式）

```typescript
// 使用现有的sessionActions
import { loginWithCode, getCurrentUser, logout } from '@/app/actions/sessionActions';
import { useSession } from '@yai-investor-insight/shared-fe-core';

function MyComponent() {
  const { session, isLoading, logout: handleLogout } = useSession({
    authMethod: 'cookies',
    serverActions: {
      getCurrentUser,
      logout,
      isAuthenticated: async () => {
        const user = await getCurrentUser();
        return user.isLoggedIn;
      }
    }
  });
  
  // 组件逻辑...
}
```

### 模块职责分工

#### user-account-fe（负责写入）
- 处理用户登录、注册等认证操作
- 调用认证API，成功后使用shared-fe-core保存session数据
- 管理用户账户相关的业务逻辑

```typescript
// user-account-fe中的使用示例
import { createSessionManager, SessionData } from '@yai-investor-insight/shared-fe-core';
import { authWithMobile, getUserProfile } from './api';

const sessionManager = createSessionManager('localStorage');

export async function handleLogin(phone: string, code: string) {
  try {
    // 1. 调用登录API
    const response = await authWithMobile({ mobile: phone, code });
    
    if (response.status === 200 && response.data?.data) {
      const authData = response.data.data;
      
      // 2. 获取用户详细信息
      const userData = await getUserProfile();
      
      // 3. 保存session到shared-fe-core
      const sessionData: SessionData = {
        isLoggedIn: true,
        userId: authData.user?.id?.toString() || userData.id,
        phone,
        userName: authData.user?.name || userData.name,
        token: authData.token,
      };
      
      await sessionManager.saveSession(sessionData);
      return { success: true };
    }
    
    return { success: false, error: '登录失败' };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

export async function handleLogout() {
  await sessionManager.clearSession();
  return { success: true };
}
```

#### 其他模块（负责查询）
- 使用`useSessionQuery`或session管理器的查询方法
- 只读取session信息，不进行修改操作
- 根据session状态调整UI和业务逻辑

```typescript
// 其他模块中的使用示例
import { useSessionQuery } from '@yai-investor-insight/shared-fe-core';

function SomeFeatureComponent() {
  const { session, isLoggedIn } = useSessionQuery();
  
  if (!isLoggedIn) {
    return <div>请先登录</div>;
  }
  
  return <div>功能内容 - 用户: {session?.userName}</div>;
}
```

### 存储方式选择

#### Cookies模式
- **优点**: 服务端安全、自动过期、防XSS
- **缺点**: 需要服务端支持、配置复杂
- **适用**: 生产环境、需要高安全性的场景

#### LocalStorage模式
- **优点**: 简单易用、客户端控制、无需服务端
- **缺点**: 安全性较低、需要手动管理过期
- **适用**: 开发环境、简单应用场景

### 类型定义

```typescript
interface SessionData {
  userId: string;
  phone: string;
  isLoggedIn: boolean;
  userName?: string;
  token?: string;
}

interface SessionResult {
  isLoggedIn: boolean;
  userId?: string;
  phone?: string;
  userName?: string;
  token?: string;
}

interface LoginResult {
  success: boolean;
  error?: string;
}
```

### 迁移指南

从现有的session管理迁移到统一管理：

1. **安装依赖**
   ```bash
   npm install @yai-investor-insight/shared-fe-core
   ```

2. **替换现有的useSession**
   ```typescript
   // 旧的
   import { useSession } from '../hooks/useSession';
   
   // 新的
   import { useSession } from '@yai-investor-insight/shared-fe-core';
   ```

3. **更新配置**
   ```typescript
   // 提供配置参数
   const { session, isLoggedIn } = useSession({
     authMethod: 'localStorage', // 或 'cookies'
     serverActions: { /* server actions */ }
   });
   ```

4. **移除旧的session文件**
   - 删除原有的session管理代码
   - 更新导入路径
   - 测试功能正常

### 注意事项

1. **环境变量**: cookies模式需要设置`SECRET_COOKIE_PASSWORD`
2. **服务端渲染**: cookies模式支持SSR，localStorage模式仅客户端
3. **错误处理**: 所有方法都包含错误处理，失败时返回安全的默认值
4. **类型安全**: 完整的TypeScript类型定义
5. **向后兼容**: 与现有API保持兼容
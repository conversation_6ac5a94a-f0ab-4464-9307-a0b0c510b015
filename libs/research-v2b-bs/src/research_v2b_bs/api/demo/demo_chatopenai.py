from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from langchain.output_parsers import PydanticOutputParser
from os import getenv
from dotenv import load_dotenv
import asyncio
from pydantic import BaseModel, Field
from typing import List

# 加载环境变量
load_dotenv()

# 定义结构化返回的数据模型
class SearchResult(BaseModel):
    search_title: str = Field(description="搜索结果的标题")
    search_content: str = Field(description="搜索结果的内容摘要，不少于100个字符")
    search_link: str = Field(description="相关链接URL")
    link_title: str = Field(description="链接的标题")

class StructuredResponse(BaseModel):
    query: str = Field(description="用户的查询问题")
    results: List[SearchResult] = Field(description="搜索结果列表，至少包含3个结果")
    summary: str = Field(description="对搜索结果的总结")

async def async_demo():
    """异步ChatOpenAI结构化返回demo - 使用PydanticOutputParser"""
    print("\n=== 异步ChatOpenAI结构化返回Demo (PydanticOutputParser版本) ===")
    
    # 初始化ChatOpenAI模型
    llm = ChatOpenAI(
        openai_api_key=getenv("OPENROUTER_API_KEY"),
        openai_api_base=getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
        model_name="anthropic/claude-sonnet-4",
        temperature=0.3
    )
    
    # 创建PydanticOutputParser
    parser = PydanticOutputParser(pydantic_object=StructuredResponse)
    
    # 示例查询
    queries = [
        "适合AI投资的团队有哪些特征？"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\nAsync Question {i}: {query}")
        print("-" * 50)
        
        try:
            # 构建系统提示，包含格式说明
            system_prompt = f"""
你是一个专业的投资研究助手。请根据用户的查询提供结构化的搜索结果。

{parser.get_format_instructions()}

请确保：
1. search_content字段不少于100个字符
2. 提供至少3个相关的搜索结果
3. 严格按照指定的JSON格式返回
            """
            
            # 构建用户提示
            user_prompt = f"请根据查询'{query}'提供结构化的搜索结果。"
            
            # 构建消息
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # 使用异步调用
            response = await llm.ainvoke(messages)
            
            # 使用PydanticOutputParser解析响应
            try:
                structured_response: StructuredResponse = parser.parse(response.content)
                
                # 格式化输出
                print(f"查询: {structured_response.query}")
                print(f"总结: {structured_response.summary}")
                print("\n搜索结果:")
                
                for idx, result in enumerate(structured_response.results, 1):
                    print(f"  {idx}. 标题: {result.search_title}")
                    print(f"     内容: {result.search_content}")
                    print(f"     链接: {result.search_link}")
                    print(f"     链接标题: {result.link_title}")
                    print()
                    
            except Exception as parse_error:
                print(f"PydanticOutputParser解析失败: {parse_error}")
                print(f"原始回答: {response.content}")
                
                # 尝试使用修复解析器
                try:
                    from langchain.output_parsers import OutputFixingParser
                    fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=llm)
                    structured_response = await fixing_parser.aparse(response.content)
                    
                    print("\n使用修复解析器成功解析:")
                    print(f"查询: {structured_response.query}")
                    print(f"总结: {structured_response.summary}")
                    print("\n搜索结果:")
                    
                    for idx, result in enumerate(structured_response.results, 1):
                        print(f"  {idx}. 标题: {result.search_title}")
                        print(f"     内容: {result.search_content}")
                        print(f"     链接: {result.search_link}")
                        print(f"     链接标题: {result.link_title}")
                        print()
                        
                except Exception as fixing_error:
                    print(f"修复解析器也失败了: {fixing_error}")
                
        except Exception as e:
            print(f"Error: {str(e)}")
        
        print("=" * 60)

async def main():
    """主函数"""
    try:
        await async_demo()
    except Exception as e:
        print(f"主程序错误: {e}")
        print("\n请检查以下配置:")
        print("1. OPENROUTER_API_KEY 是否正确设置")
        print("2. OPENROUTER_BASE_URL 是否正确")
        print("3. 网络连接是否正常")
        print("4. API密钥是否有足够余额")

if __name__ == "__main__":
    asyncio.run(main())
from ..constants import NodeNames

# 节点名称到提示词的映射
NODE_PROMPT_MAP = {
    # Truth系列节点
    NodeNames.TRUTH_GENERATE_FACT_QUESTIONS: {
        "prompt_module": "truth_eye_1",
        "step_name": "真理之眼 - 广度问题构建",
        "question_type": "fact_check",
        "category": "事实核查",
        "icon": "👁️",
        "input": "profile",
        "output": "fact_check_questions",
        "state_key": "fact_check_questions",
        "param_mapping": {
            "profile": "profile"
        }
    },
    # Truth系列节点
    NodeNames.TRUTH_EYE_INITIAL_ANALYSIS: {
        "prompt_module": "truth_eye_2",
        "step_name": "真理之眼 - 初步事实分析",
        "input": "profile",
        "output": "initial_analysis_report",
        "state_key": "initial_analysis_report",
        "param_mapping": {
            "profile": "profile",
            "search_results_summary": "search_results_summary"  # 统一使用search_results_summary
        }
    },
    NodeNames.TRUTH_GENERATE_FACT_QUESTIONS_LOOP: {
        "prompt_module": "truth_eye_3",
        "step_name": "真理之眼 - 深度问题构建",
        "state_key": "initial_analysis_results",
        "category": "深度问题构建",
        "input": "initial_analysis_results",
        "output": "initial_analysis_results",
        "loop_count_key": "truth_loop_count",
        "param_mapping": {
            "profile": "profile",
            "initial_analysis_results": "initial_analysis_report"
        }
    },
    NodeNames.TRUTH_EYE_INITIAL_ANALYSIS_LOOP: {
        "prompt_module": "truth_eye_4",
        "step_name": "真理之眼 - 最终事实合成",
        "state_key": "initial_analysis_results",
        "category": "最终分析报告",
        "input": "initial_analysis_results",
        "output": "initial_analysis_results",
        "loop_count_key": "truth_loop_count",
        "param_mapping": {
            "profile": "profile",
            "initial_analysis_results": "initial_analysis_results",
            "search_results_summary": "search_results_summary"  # 新增映射
        }
    },

    # Alpha系列节点
    NodeNames.ALPHA_GENERATE_FACT_QUESTIONS: {
        "prompt_module": "alpha1",
        "step_name": "阿尔法筛选器 - 二级市场筛选因子数据需求构建",
        "question_type": "screening_data",
        "category": "二级市场筛选",
        "icon": "📊",
        "state_key": "screening_questions",
        "param_mapping": {
            "profile": "profile",
            "initial_analysis_results":"initial_analysis_results"
        }
    },
    NodeNames.ALPHA_EYE_INITIAL_ANALYSIS: {
        "prompt_module": "alpha2",
        "step_name": "阿尔法筛选器 - 初步筛选与二级市场候选名单生成",
        "state_key": "alpha_analysis_report",
        "param_mapping": {
            "profile": "profile",
            "cassandra_analysis_results": "cassandra_analysis_report",  # 接收卡桑德拉结果
            "search_results_summary": "search_results_summary"
        }
    },
    NodeNames.ALPHA_GENERATE_FACT_QUESTIONS_LOOP: {
        "prompt_module": "alpha3",
        "step_name": "阿尔法筛选器 - 深度问题构建",
        "question_type": "fact_check",
        "category": "阿尔法深度分析",
        "icon": "📊",
        "state_key": "fact_check_questions",
        "param_mapping": {
            "profile": "profile",
            "initial_analysis_results": "alpha_analysis_report"
        }
    },
    NodeNames.ALPHA_EYE_INITIAL_ANALYSIS_LOOP: {
        "prompt_module": "alpha4",
        "step_name": "阿尔法筛选器 - 最终验证与核心名单合成",
        "state_key": "alpha_analysis_report",
        "category": "阿尔法最终报告",
        "loop_count_key": "alpha_loop_count",
        "param_mapping": {
            "profile": "profile",
            "alpha_analysis_results": "alpha_analysis_report",
            "search_results_summary": "search_results_summary"
        }
    },

    # Cassandra系列节点
    NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS: {
        "prompt_module": "cassandra1",
        "step_name": "卡桑德拉认知系统 - 预测数据需求构建",
        "question_type": "prediction_data",
        "category": "预测分析",
        "icon": "🔮",
        "state_key": "prediction_questions",
        "param_mapping": {
            "profile": "profile"
        }
    },
    NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS: {
        "prompt_module": "cassandra2",
        "step_name": "卡桑德拉认知系统 - 初步影响假设生成",
        "state_key": "cassandra_analysis_report",
        "param_mapping": {
            "profile": "profile",
            "initial_analysis_results": "initial_analysis_results",  # 接收真理之眼结果
            "search_results_summary": "search_results_summary"
        }
    },
    NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS_LOOP: {
        "prompt_module": "cassandra3",
        "step_name": "卡桑德拉认知系统 - 深度问题构建",
        "question_type": "fact_check",
        "category": "卡桑德拉深度分析",
        "icon": "🔮",
        "state_key": "fact_check_questions",
        "param_mapping": {
            "profile": "profile",
            "initial_analysis_results": "cassandra_analysis_report"
        }
    },
    NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS_LOOP: {
        "prompt_module": "cassandra4",
        "step_name": "卡桑德拉认知系统 - 高置信度影响合成",
        "state_key": "cassandra_analysis_report",
        "category": "卡桑德拉最终报告",
        "loop_count_key": "cassandra_loop_count",
        "param_mapping": {
            "profile": "profile",
            "cassandra_analysis_results": "cassandra_analysis_report",
            "search_results_summary": "search_results_summary"
        }
    }
}
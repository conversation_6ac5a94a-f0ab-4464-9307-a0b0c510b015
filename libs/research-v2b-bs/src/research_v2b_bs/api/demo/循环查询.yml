app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 循环查询
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/perplexity:1.0.0@cb662f8a44deefb34239e5e600816f95174f235664fe69a5f0a69e4c8675766c
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/gemini:0.1.5@012c9e0467a11910db974e0436348e93a376fdc96381946a3db2c56708377381
kind: app
version: 0.2.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: parameter-extractor
      id: 1744529923194-source-1744529985583-target
      selected: false
      source: '1744529923194'
      sourceHandle: source
      target: '1744529985583'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: parameter-extractor
        targetType: iteration
      id: 1744529985583-source-1744529933103-target
      selected: false
      source: '1744529985583'
      sourceHandle: source
      target: '1744529933103'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1744529933103'
        sourceType: iteration-start
        targetType: tool
      id: 1744529933103start-source-1744990886744-target
      source: 1744529933103start
      sourceHandle: source
      target: '1744990886744'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: iteration
        targetType: template-transform
      id: 1744529933103-source-1745746551751-target
      source: '1744529933103'
      sourceHandle: source
      target: '1745746551751'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: end
      id: 1745746551751-source-1744529948221-target
      source: '1745746551751'
      sourceHandle: source
      target: '1744529948221'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - allowed_file_extensions: []
          allowed_file_types:
          - image
          allowed_file_upload_methods:
          - local_file
          - remote_url
          label: questions
          max_length: 10000000
          options: []
          required: true
          type: paragraph
          variable: questions
      height: 90
      id: '1744529923194'
      position:
        x: 30
        y: 305
      positionAbsolute:
        x: 30
        y: 305
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        error_handle_mode: continue-on-error
        height: 503
        is_parallel: true
        iterator_selector:
        - '1744529985583'
        - Questions
        output_selector:
        - '1744990886744'
        - text
        output_type: array[string]
        parallel_nums: 10
        selected: false
        start_node_id: 1744529933103start
        title: 迭代
        type: iteration
        width: 487
      height: 503
      id: '1744529933103'
      position:
        x: 628.350273838248
        y: 305
      positionAbsolute:
        x: 628.350273838248
        y: 305
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 487
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1744529933103start
      parentId: '1744529933103'
      position:
        x: 60
        y: 81
      positionAbsolute:
        x: 688.350273838248
        y: 386
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1745746551751'
          - output
          variable: output
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1744529948221'
      position:
        x: 1472.0838287228432
        y: 305
      positionAbsolute:
        x: 1472.0838287228432
        y: 305
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        instruction: 将问题列表抽取成为Array[string]的形式，每个String是一个问题
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: gemini-2.0-flash
          provider: langgenius/gemini/google
        parameters:
        - description: 每个问题作为一个单独的String
          name: Questions
          required: false
          type: array[string]
        query:
        - '1744529923194'
        - questions
        reasoning_mode: prompt
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1744529985583'
      position:
        x: 335.2869140879234
        y: 305
      positionAbsolute:
        x: 335.2869140879234
        y: 305
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        isInIteration: true
        isInLoop: false
        is_team_authorization: true
        iteration_id: '1744529933103'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: The text query to be processed by the AI model.
            ja_JP: The text query to be processed by the AI model.
            pt_BR: The text query to be processed by the AI model.
            zh_Hans: 要由 AI 模型处理的文本查询。
          label:
            en_US: Query
            ja_JP: Query
            pt_BR: Query
            zh_Hans: 查询
          llm_description: ''
          max: null
          min: null
          name: query
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: sonar
          form: form
          human_description:
            en_US: The Perplexity AI model to use for generating the response.
            ja_JP: The Perplexity AI model to use for generating the response.
            pt_BR: The Perplexity AI model to use for generating the response.
            zh_Hans: 用于生成响应的 Perplexity AI 模型。
          label:
            en_US: Model Name
            ja_JP: Model Name
            pt_BR: Model Name
            zh_Hans: 模型名称
          llm_description: ''
          max: null
          min: null
          name: model
          options:
          - label:
              en_US: sonar
              ja_JP: sonar
              pt_BR: sonar
              zh_Hans: sonar
            value: sonar
          - label:
              en_US: sonar-pro
              ja_JP: sonar-pro
              pt_BR: sonar-pro
              zh_Hans: sonar-pro
            value: sonar-pro
          - label:
              en_US: sonar-reasoning
              ja_JP: sonar-reasoning
              pt_BR: sonar-reasoning
              zh_Hans: sonar-reasoning
            value: sonar-reasoning
          - label:
              en_US: sonar-reasoning-pro
              ja_JP: sonar-reasoning-pro
              pt_BR: sonar-reasoning-pro
              zh_Hans: sonar-reasoning-pro
            value: sonar-reasoning-pro
          - label:
              en_US: sonar-deep-research
              ja_JP: sonar-deep-research
              pt_BR: sonar-deep-research
              zh_Hans: sonar-deep-research
            value: sonar-deep-research
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: 4096
          form: form
          human_description:
            en_US: The maximum number of tokens to generate in the response.
            ja_JP: The maximum number of tokens to generate in the response.
            pt_BR: O número máximo de tokens a serem gerados na resposta.
            zh_Hans: 在响应中生成的最大令牌数。
          label:
            en_US: Max Tokens
            ja_JP: Max Tokens
            pt_BR: Máximo de Tokens
            zh_Hans: 最大令牌数
          llm_description: ''
          max: 4096
          min: 1
          name: max_tokens
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: 0.7
          form: form
          human_description:
            en_US: Controls randomness in the output. Lower values make the output
              more focused and deterministic.
            ja_JP: Controls randomness in the output. Lower values make the output
              more focused and deterministic.
            pt_BR: Controls randomness in the output. Lower values make the output
              more focused and deterministic.
            zh_Hans: 控制输出的随机性。较低的值使输出更加集中和确定。
          label:
            en_US: Temperature
            ja_JP: Temperature
            pt_BR: Temperatura
            zh_Hans: 温度
          llm_description: ''
          max: 1
          min: 0
          name: temperature
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: 5
          form: form
          human_description:
            en_US: The number of top results to consider for response generation.
            ja_JP: The number of top results to consider for response generation.
            pt_BR: The number of top results to consider for response generation.
            zh_Hans: 用于生成响应的顶部结果数量。
          label:
            en_US: Top K
            ja_JP: Top K
            pt_BR: Top K
            zh_Hans: 取样数量
          llm_description: ''
          max: 100
          min: 1
          name: top_k
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: 1
          form: form
          human_description:
            en_US: Controls diversity via nucleus sampling.
            ja_JP: Controls diversity via nucleus sampling.
            pt_BR: Controls diversity via nucleus sampling.
            zh_Hans: 通过核心采样控制多样性。
          label:
            en_US: Top P
            ja_JP: Top P
            pt_BR: Top P
            zh_Hans: Top P
          llm_description: ''
          max: 1
          min: 0.1
          name: top_p
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: 0
          form: form
          human_description:
            en_US: Positive values penalize new tokens based on whether they appear
              in the text so far.
            ja_JP: Positive values penalize new tokens based on whether they appear
              in the text so far.
            pt_BR: Positive values penalize new tokens based on whether they appear
              in the text so far.
            zh_Hans: 正值会根据新词元是否已经出现在文本中来对其进行惩罚。
          label:
            en_US: Presence Penalty
            ja_JP: Presence Penalty
            pt_BR: Presence Penalty
            zh_Hans: 存在惩罚
          llm_description: ''
          max: 1
          min: -1
          name: presence_penalty
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: 1
          form: form
          human_description:
            en_US: Positive values penalize new tokens based on their existing frequency
              in the text so far.
            ja_JP: Positive values penalize new tokens based on their existing frequency
              in the text so far.
            pt_BR: Positive values penalize new tokens based on their existing frequency
              in the text so far.
            zh_Hans: 正值会根据新词元在文本中已经出现的频率来对其进行惩罚。
          label:
            en_US: Frequency Penalty
            ja_JP: Frequency Penalty
            pt_BR: Frequency Penalty
            zh_Hans: 频率惩罚
          llm_description: ''
          max: 1
          min: 0.1
          name: frequency_penalty
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: 0
          form: form
          human_description:
            en_US: Whether to return images in the response.
            ja_JP: Whether to return images in the response.
            pt_BR: Whether to return images in the response.
            zh_Hans: 是否在响应中返回图像。
          label:
            en_US: Return Images
            ja_JP: Return Images
            pt_BR: Return Images
            zh_Hans: 返回图像
          llm_description: ''
          max: null
          min: null
          name: return_images
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        - auto_generate: null
          default: 0
          form: form
          human_description:
            en_US: Whether to return related questions in the response.
            ja_JP: Whether to return related questions in the response.
            pt_BR: Whether to return related questions in the response.
            zh_Hans: 是否在响应中返回相关问题。
          label:
            en_US: Return Related Questions
            ja_JP: Return Related Questions
            pt_BR: Return Related Questions
            zh_Hans: 返回相关问题
          llm_description: ''
          max: null
          min: null
          name: return_related_questions
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        - auto_generate: null
          default: ''
          form: form
          human_description:
            en_US: Domain to filter the search results. Use comma to separate multiple
              domains. Up to 3 domains are supported.
            ja_JP: Domain to filter the search results. Use comma to separate multiple
              domains. Up to 3 domains are supported.
            pt_BR: Domain to filter the search results. Use comma to separate multiple
              domains. Up to 3 domains are supported.
            zh_Hans: 用于过滤搜索结果的域名。使用逗号分隔多个域名。最多支持3个域名。
          label:
            en_US: Search Domain Filter
            ja_JP: Search Domain Filter
            pt_BR: Search Domain Filter
            zh_Hans: 搜索域过滤器
          llm_description: ''
          max: null
          min: null
          name: search_domain_filter
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: month
          form: form
          human_description:
            en_US: Filter for search results based on recency.
            ja_JP: Filter for search results based on recency.
            pt_BR: Filter for search results based on recency.
            zh_Hans: 基于时间筛选搜索结果。
          label:
            en_US: Search Recency Filter
            ja_JP: Search Recency Filter
            pt_BR: Search Recency Filter
            zh_Hans: 搜索时间过滤器
          llm_description: ''
          max: null
          min: null
          name: search_recency_filter
          options:
          - label:
              en_US: Day
              ja_JP: Day
              pt_BR: Day
              zh_Hans: 天
            value: day
          - label:
              en_US: Week
              ja_JP: Week
              pt_BR: Week
              zh_Hans: 周
            value: week
          - label:
              en_US: Month
              ja_JP: Month
              pt_BR: Month
              zh_Hans: 月
            value: month
          - label:
              en_US: Year
              ja_JP: Year
              pt_BR: Year
              zh_Hans: 年
            value: year
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: low
          form: form
          human_description:
            en_US: Determines how much search context is retrieved for the model.
            ja_JP: Determines how much search context is retrieved for the model.
            pt_BR: Determines how much search context is retrieved for the model.
            zh_Hans: 确定模型检索的搜索上下文量。
          label:
            en_US: Search Context Size
            ja_JP: Search Context Size
            pt_BR: Search Context Size
            zh_Hans: 搜索上下文大小
          llm_description: ''
          max: null
          min: null
          name: search_context_size
          options:
          - label:
              en_US: Low
              ja_JP: Low
              pt_BR: Low
              zh_Hans: 低
            value: low
          - label:
              en_US: Medium
              ja_JP: Medium
              pt_BR: Medium
              zh_Hans: 中等
            value: medium
          - label:
              en_US: High
              ja_JP: High
              pt_BR: High
              zh_Hans: 高
            value: high
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        params:
          frequency_penalty: ''
          max_tokens: ''
          model: ''
          presence_penalty: ''
          query: ''
          return_images: ''
          return_related_questions: ''
          search_context_size: ''
          search_domain_filter: ''
          search_recency_filter: ''
          temperature: ''
          top_k: ''
          top_p: ''
        provider_id: langgenius/perplexity/perplexity
        provider_name: langgenius/perplexity/perplexity
        provider_type: builtin
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: Perplexity Search
        tool_configurations:
          frequency_penalty: 1
          max_tokens: 4096
          model: sonar
          presence_penalty: 0
          return_images: 0
          return_related_questions: 0
          search_context_size: low
          search_domain_filter: ''
          search_recency_filter: year
          temperature: 0.7
          top_k: 10
          top_p: 1
        tool_label: Perplexity Search
        tool_name: perplexity
        tool_parameters:
          query:
            type: mixed
            value: '{{#1744529933103.item#}}'
        type: tool
      height: 405
      id: '1744990886744'
      parentId: '1744529933103'
      position:
        x: 150.91662526531138
        y: 78
      positionAbsolute:
        x: 779.2668991035594
        y: 383
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        selected: false
        template: '{{ output }}'
        title: 模板转换
        type: template-transform
        variables:
        - value_selector:
          - '1744529933103'
          - output
          variable: output
      height: 54
      id: '1745746551751'
      position:
        x: 1175.350273838248
        y: 305
      positionAbsolute:
        x: 1175.350273838248
        y: 305
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 264.6147686835677
      y: 111.32775482407624
      zoom: 0.5221037074862307


from langchain_core.runnables import RunnableConfig
from ..state import InvestmentAnalysisState
from .nodes_function import generate_fact_questions
from ..constants import NodeNames

class AlphaGenerateFactQuestionsLoopNode:
    def __init__(self):
        """初始化薪资问题节点"""
        pass
    async def __call__(self, state: InvestmentAnalysisState,config:RunnableConfig):
         async for result in generate_fact_questions(state, config,NodeNames.ALPHA_GENERATE_FACT_QUESTIONS_LOOP):
            yield result
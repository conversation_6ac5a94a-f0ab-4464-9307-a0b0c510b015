from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, Optional
from ..constants import NodeNames

# 添加阶段枚举，与前端保持一致
class ResearchStage(Enum):
    """研究阶段枚举"""
    IDLE = 'idle'
    FACT_VERIFICATION = 'fact-verification'
    IMPACT_SIMULATION = 'impact-simulation'
    THESIS_GENERATION = 'thesis-generation'
    FINAL_REPORT = 'final-report'
    COMPLETED = 'completed'

@dataclass
class NodeConfig:
    """节点配置类"""
    prompt_module: str
    step_name: str
    input_key: str
    output_key: str
    stage: ResearchStage  # 新增阶段字段
    loop_count_key: Optional[str] = None
    param_mapping: Optional[Dict[str, str]] = None
    category: Optional[str] = None
    icon: Optional[str] = None

class NodeConfigEnum(Enum):
    """节点配置枚举"""
    
    # 真理之眼系列 - 事实核查阶段
    TRUTH_GENERATE_QUESTIONS = NodeConfig(
        prompt_module="truth_eye_1",
        step_name="真理之眼 - 广度问题构建",
        input_key="profile",
        output_key="truth_questions",
        stage=ResearchStage.FACT_VERIFICATION,
        param_mapping={"profile": "profile"},
        category="事实核查",
        icon="👁️"
    )
    
    TRUTH_INITIAL_ANALYSIS = NodeConfig(
        prompt_module="truth_eye_2",
        step_name="真理之眼 - 初步事实分析",
        input_key="truth_questions",
        output_key="truth_analysis_report",
        stage=ResearchStage.FACT_VERIFICATION,
        category="初步事实分析",
        param_mapping={
            "profile": "profile",
            "search_results_summary": "search_results_summary"
        }
    )
    
    TRUTH_LOOP_QUESTIONS = NodeConfig(
        prompt_module="truth_eye_3",
        step_name="真理之眼 - 深度问题构建",
        input_key="truth_analysis_report",
        output_key="truth_loop_questions",
        stage=ResearchStage.FACT_VERIFICATION,
        loop_count_key="truth_loop_count",
        category="深度问题",
        param_mapping={
            "profile": "profile",
            "initial_analysis_results": "initial_analysis_results"
        }
    )
    
    TRUTH_FINAL_ANALYSIS = NodeConfig(
        prompt_module="truth_eye_4",
        step_name="真理之眼 - 最终事实合成",
        input_key="truth_loop_questions",
        output_key="truth_final_report",
        stage=ResearchStage.FACT_VERIFICATION,
        loop_count_key="truth_loop_count",
        category="最终事实",
        param_mapping={
            "profile": "profile",
            "initial_analysis_results": "initial_analysis_results",
            "search_results_summary": "search_results_summary"
        }
    )
    
    # 卡桑德拉系列 - 影响模拟阶段
    CASSANDRA_GENERATE_QUESTIONS = NodeConfig(
        prompt_module="cassandra1",
        step_name="卡桑德拉 - 预测数据需求构建",
        input_key="truth_final_report",
        output_key="cassandra_questions",
        stage=ResearchStage.IMPACT_SIMULATION,
        param_mapping={
            "profile": "profile",
            "initial_analysis_results": "truth_final_report"
        },
        category="预测分析",
        icon="🔮"
    )
    
    CASSANDRA_INITIAL_ANALYSIS = NodeConfig(
        prompt_module="cassandra2",
        step_name="卡桑德拉 - 初步影响假设生成",
        input_key="cassandra_questions",
        output_key="cassandra_analysis_report",
        stage=ResearchStage.IMPACT_SIMULATION,
        param_mapping={
            "profile": "profile",
            "initial_analysis_results": "truth_final_report",
            "search_results_summary": "search_results_summary"
        }
    )
    
    CASSANDRA_LOOP_QUESTIONS = NodeConfig(
        prompt_module="cassandra3",
        step_name="卡桑德拉 - 深度问题构建",
        input_key="cassandra_analysis_report",
        output_key="cassandra_loop_questions",
        stage=ResearchStage.IMPACT_SIMULATION,
        loop_count_key="cassandra_loop_count",
        param_mapping={
            "profile": "profile",
            "initial_analysis_results": "cassandra_analysis_report"
        }
    )
    
    CASSANDRA_FINAL_ANALYSIS = NodeConfig(
        prompt_module="cassandra4",
        step_name="卡桑德拉 - 高置信度影响合成",
        input_key="cassandra_loop_questions",
        output_key="cassandra_final_report",
        stage=ResearchStage.IMPACT_SIMULATION,
        loop_count_key="cassandra_loop_count",
        param_mapping={
            "profile": "profile",
            "cassandra_analysis_results": "cassandra_analysis_report",
            "search_results_summary": "search_results_summary"
        }
    )
    
    # 阿尔法系列 - 论文生成阶段
    ALPHA_GENERATE_QUESTIONS = NodeConfig(
        prompt_module="alpha1",
        step_name="阿尔法 - 二级市场筛选因子构建",
        input_key="cassandra_final_report",
        output_key="alpha_questions",
        stage=ResearchStage.THESIS_GENERATION,
        param_mapping={
            "profile": "profile",
            "initial_analysis_results": "cassandra_final_report"
        },
        category="二级市场筛选",
        icon="📊"
    )
    
    ALPHA_INITIAL_ANALYSIS = NodeConfig(
        prompt_module="alpha2",
        step_name="阿尔法 - 初步筛选与候选名单生成",
        input_key="alpha_questions",
        output_key="alpha_analysis_report",
        stage=ResearchStage.THESIS_GENERATION,
        param_mapping={
            "profile": "profile",
            "initial_analysis_results": "cassandra_final_report",
            "search_results_summary": "search_results_summary"
        }
    )
    
    ALPHA_LOOP_QUESTIONS = NodeConfig(
        prompt_module="alpha3",
        step_name="阿尔法 - 深度问题构建",
        input_key="alpha_analysis_report",
        output_key="alpha_loop_questions",
        stage=ResearchStage.THESIS_GENERATION,
        loop_count_key="alpha_loop_count",
        param_mapping={
            "profile": "profile",
            "initial_analysis_results": "alpha_analysis_report"
        }
    )
    
    ALPHA_FINAL_ANALYSIS = NodeConfig(
        prompt_module="alpha4",
        step_name="阿尔法 - 最终验证与核心名单合成",
        input_key="alpha_loop_questions",
        output_key="alpha_final_report",
        stage=ResearchStage.THESIS_GENERATION,
        loop_count_key="alpha_loop_count",
        param_mapping={
            "profile": "profile",
            "alpha_analysis_results": "alpha_analysis_report",
            "search_results_summary": "search_results_summary"
        }
    )

# 节点名称到配置的映射
NODE_CONFIG_MAP = {
    NodeNames.TRUTH_GENERATE_FACT_QUESTIONS: NodeConfigEnum.TRUTH_GENERATE_QUESTIONS,
    NodeNames.TRUTH_EYE_INITIAL_ANALYSIS: NodeConfigEnum.TRUTH_INITIAL_ANALYSIS,
    NodeNames.TRUTH_GENERATE_FACT_QUESTIONS_LOOP: NodeConfigEnum.TRUTH_LOOP_QUESTIONS,
    NodeNames.TRUTH_EYE_INITIAL_ANALYSIS_LOOP: NodeConfigEnum.TRUTH_FINAL_ANALYSIS,
    
    NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS: NodeConfigEnum.CASSANDRA_GENERATE_QUESTIONS,
    NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS: NodeConfigEnum.CASSANDRA_INITIAL_ANALYSIS,
    NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS_LOOP: NodeConfigEnum.CASSANDRA_LOOP_QUESTIONS,
    NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS_LOOP: NodeConfigEnum.CASSANDRA_FINAL_ANALYSIS,
    
    NodeNames.ALPHA_GENERATE_FACT_QUESTIONS: NodeConfigEnum.ALPHA_GENERATE_QUESTIONS,
    NodeNames.ALPHA_EYE_INITIAL_ANALYSIS: NodeConfigEnum.ALPHA_INITIAL_ANALYSIS,
    NodeNames.ALPHA_GENERATE_FACT_QUESTIONS_LOOP: NodeConfigEnum.ALPHA_LOOP_QUESTIONS,
    NodeNames.ALPHA_EYE_INITIAL_ANALYSIS_LOOP: NodeConfigEnum.ALPHA_FINAL_ANALYSIS,
}

# 辅助函数
def get_node_config(node_name: str) -> NodeConfig:
    """根据节点名称获取配置"""
    config_enum = NODE_CONFIG_MAP.get(node_name)
    if config_enum:
        return config_enum.value
    raise ValueError(f"Unknown node name: {node_name}")

def should_continue_loop(state: dict, node_name: str, max_loops: int = 1) -> str:
    """判断是否继续循环"""
    config = get_node_config(node_name)
    if config.loop_count_key:
        current_count = state.get(config.loop_count_key, 0)
        return "continue" if current_count < max_loops else "exit"
    return "exit"
from langchain_core.runnables import RunnableConfig
from ..state import InvestmentAnalysisState
from .nodes_function import initial_analysis
from ..constants import NodeNames

class AlphaEyeInitialAnalysisLoopNode:
    def __init__(self):
        """初始化薪资问题节点"""
        pass
    
    async def __call__(self, state: InvestmentAnalysisState, config: RunnableConfig):
        async for result in initial_analysis(state, config, NodeNames.ALPHA_EYE_INITIAL_ANALYSIS_LOOP):
            yield result
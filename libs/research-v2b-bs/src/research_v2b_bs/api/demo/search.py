"""
事件驱动投资分析的搜索模块
"""

import os
from typing import Dict, Any, List
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from langchain.output_parsers import PydanticOutputParser
from .configuration import VestConfiguration
from langchain.prompts import PromptTemplate
from dotenv import load_dotenv
from pydantic import BaseModel, Field

# 加载环境变量
load_dotenv()

# 定义结构化返回的数据模型
class SearchResult(BaseModel):
    search_title: str = Field(description="搜索结果的标题")
    search_content: str = Field(description="搜索结果的内容摘要，不少于100个字符")
    search_link: str = Field(description="相关链接URL")
    link_title: str = Field(description="链接的标题")

class StructuredResponse(BaseModel):
    query: str = Field(description="用户的查询问题")
    results: List[SearchResult] = Field(description="搜索结果列表，至少包含3个结果")
    summary: str = Field(description="对搜索结果的总结")


class VestSearch:
    """事件驱动投资分析搜索器"""
    
    def __init__(self, config: VestConfiguration):
        self.config = config
        
        # 检查环境变量
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY 环境变量未设置，请设置后重试")
        
        # 初始化ChatOpenAI模型
        self.llm = ChatOpenAI(
            openai_api_key=api_key,
            openai_api_base=os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
            model_name="perplexity/sonar-pro",
            temperature=0.3
        )
        
        # 创建PydanticOutputParser
        self.parser = PydanticOutputParser(pydantic_object=StructuredResponse)
    
    async def search(self, search_query: str) -> Dict[str, Any]:
        """执行搜索操作"""
        try:
            # 构建系统提示，包含格式说明
            system_prompt = f"""
你是一个专业的投资研究助手。请根据用户的查询提供结构化的搜索结果。

{self.parser.get_format_instructions()}

请确保：
1. search_content字段不少于100个字符
2. 提供至少3个相关的搜索结果
3. 严格按照指定的JSON格式返回
4. 搜索结果应该是真实、准确、最新的投资相关信息
            """
            
            # 构建用户提示
            user_prompt = f"请根据查询'{search_query}'提供结构化的搜索结果。"
            
            # 构建消息
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # 使用异步调用
            response = await self.llm.ainvoke(messages)
            
            # 使用PydanticOutputParser解析响应
            try:
                structured_response: StructuredResponse = self.parser.parse(response.content)
                
                # 转换为原有的返回格式
                search_content = "\n".join([
                    f"{result.search_title}: {result.search_content}" 
                    for result in structured_response.results
                ])
                
                sources_gathered = [{
                    "title": result.search_title,
                    "content": result.search_content,
                    "link": result.search_link,
                    "short_url": f"source_{idx}",
                    "value": result.search_link
                } for idx, result in enumerate(structured_response.results)]
                
                return {
                    'search_content': search_content,
                    'sources_gathered': sources_gathered
                }
                
            except Exception as parse_error:
                if self.config.enable_debug:
                    print(f"PydanticOutputParser解析失败: {parse_error}")
                    print(f"原始回答: {response.content}")
                
                # 尝试使用修复解析器
                try:
                    from langchain.output_parsers import OutputFixingParser
                    fixing_parser = OutputFixingParser.from_llm(parser=self.parser, llm=self.llm)
                    structured_response = await fixing_parser.aparse(response.content)
                    
                    # 转换为原有的返回格式
                    search_content = "\n".join([
                        f"{result.search_title}: {result.search_content}" 
                        for result in structured_response.results
                    ])
                    
                    sources_gathered = [{
                        "title": result.search_title,
                        "content": result.search_content,
                        "link": result.search_link,
                        "short_url": f"source_{idx}",
                        "value": result.search_link
                    } for idx, result in enumerate(structured_response.results)]
                    
                    return {
                        'search_content': search_content,
                        'sources_gathered': sources_gathered
                    }
                    
                except Exception as fixing_error:
                    if self.config.enable_debug:
                        print(f"修复解析器也失败了: {fixing_error}")
                    
                    return {
                        'search_content': f"搜索查询 '{search_query}' 解析失败: {str(fixing_error)}",
                        'sources_gathered': []
                    }
            
        except Exception as e:
            if self.config.enable_debug:
                print(f"搜索异常 - 查询: '{search_query}', 错误: {str(e)}")
            
            return {
                'search_content': f"搜索失败: {str(e)}",
                'sources_gathered': []
            }


class SearchResultItem(BaseModel):
    """搜索结果项"""
    title: str
    content: str
    icon: str
    link: str
    media: str
    publish_date: str
    refer: str
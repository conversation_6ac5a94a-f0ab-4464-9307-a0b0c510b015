app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 事件驱动投资 V2
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/anthropic:0.0.13@3c4d4e14652baa9ce1f10e934da9995b3cde0f2ec94ee6638b73fecf1ed53288
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables:
  - description: ''
    id: c6d8f868-3378-4791-83ee-181c4c574f48
    name: answer
    selector:
    - env
    - answer
    value: ' '
    value_type: string
  - description: ''
    id: e2277ef8-7769-4c30-b5e8-80ba320891d6
    name: fmpkey
    selector:
    - env
    - fmpkey
    value: oSBkR9vIQ7Au2O7gFjfxIfg8VQ3vrcyR
    value_type: string
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: loop-start
        targetType: llm
      id: 1745736592974start-source-1745736635063-target
      selected: false
      source: 1745736592974start
      sourceHandle: source
      target: '1745736635063'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: llm
        targetType: tool
      id: 1745736635063-source-1745736689919-target
      selected: false
      source: '1745736635063'
      sourceHandle: source
      target: '1745736689919'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: template-transform
        targetType: llm
      id: 1745736788778-source-1745736973218-target
      selected: false
      source: '1745736788778'
      sourceHandle: source
      target: '1745736973218'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: llm
        targetType: assigner
      id: 1745736973218-source-1745737138928-target
      selected: false
      source: '1745736973218'
      sourceHandle: source
      target: '1745737138928'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        sourceType: tool
        targetType: template-transform
      id: 1745736689919-source-1745736788778-target
      selected: false
      source: '1745736689919'
      sourceHandle: source
      target: '1745736788778'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: llm
        targetType: tool
      id: 1745854264883017458542648830-source-1745854264883017458542648841-target
      source: '1745854264883017458542648830'
      sourceHandle: source
      target: '1745854264883017458542648841'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: tool
        targetType: template-transform
      id: 1745854264883017458542648841-source-1745854264883017458542648842-target
      source: '1745854264883017458542648841'
      sourceHandle: source
      target: '1745854264883017458542648842'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: template-transform
        targetType: llm
      id: 1745854264883017458542648842-source-1745854264883017458542648843-target
      source: '1745854264883017458542648842'
      sourceHandle: source
      target: '1745854264883017458542648843'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: llm
        targetType: assigner
      id: 1745854264883017458542648843-source-1745854264883017458542648844-target
      source: '1745854264883017458542648843'
      sourceHandle: source
      target: '1745854264883017458542648844'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        sourceType: loop-start
        targetType: llm
      id: 1745854264883start-source-1745854264883017458542648830-target
      source: 1745854264883start
      sourceHandle: source
      target: '1745854264883017458542648830'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: loop-start
        targetType: llm
      id: 1745855419665start-source-1745855419665017458554196650-target
      source: 1745855419665start
      sourceHandle: source
      target: '1745855419665017458554196650'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: llm
        targetType: tool
      id: 1745855419665017458554196650-source-1745855419665017458554196651-target
      source: '1745855419665017458554196650'
      sourceHandle: source
      target: '1745855419665017458554196651'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: tool
        targetType: template-transform
      id: 1745855419665017458554196651-source-1745855419665017458554196652-target
      source: '1745855419665017458554196651'
      sourceHandle: source
      target: '1745855419665017458554196652'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: template-transform
        targetType: llm
      id: 1745855419665017458554196652-source-1745855419665017458554196653-target
      source: '1745855419665017458554196652'
      sourceHandle: source
      target: '1745855419665017458554196653'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        sourceType: llm
        targetType: assigner
      id: 1745855419665017458554196653-source-1745855419665017458554196654-target
      source: '1745855419665017458554196653'
      sourceHandle: source
      target: '1745855419665017458554196654'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: loop-start
        targetType: llm
      id: 1745856014976start-source-1745856014976017458560149760-target
      source: 1745856014976start
      sourceHandle: source
      target: '1745856014976017458560149760'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: llm
        targetType: tool
      id: 1745856014976017458560149760-source-1745856014976017458560149761-target
      source: '1745856014976017458560149760'
      sourceHandle: source
      target: '1745856014976017458560149761'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: tool
        targetType: template-transform
      id: 1745856014976017458560149761-source-1745856014976017458560149762-target
      source: '1745856014976017458560149761'
      sourceHandle: source
      target: '1745856014976017458560149762'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: template-transform
        targetType: llm
      id: 1745856014976017458560149762-source-1745856014976017458560149763-target
      source: '1745856014976017458560149762'
      sourceHandle: source
      target: '1745856014976017458560149763'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        sourceType: llm
        targetType: assigner
      id: 1745856014976017458560149763-source-1745856014976017458560149764-target
      source: '1745856014976017458560149763'
      sourceHandle: source
      target: '1745856014976017458560149764'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: loop
        targetType: end
      id: 1745736592974-source-1745766730888-target
      source: '1745736592974'
      sourceHandle: source
      target: '1745766730888'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 17457341467330-source-1749285171192-target
      source: '17457341467330'
      sourceHandle: source
      target: '1749285171192'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 1749285171192-source-17473249427250-target
      source: '1749285171192'
      sourceHandle: source
      target: '17473249427250'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: loop
      id: 17473249427250-source-17458542648830-target
      source: '17473249427250'
      sourceHandle: source
      target: '17458542648830'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 17457361886690-source-17493058108570-target
      source: '17457361886690'
      sourceHandle: source
      target: '17493058108570'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 17493058108570-source-17493058407690-target
      source: '17493058108570'
      sourceHandle: source
      target: '17493058407690'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: loop
      id: 17493058407690-source-17458554196650-target
      source: '17493058407690'
      sourceHandle: source
      target: '17458554196650'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 17457361931830-source-17493064534440-target
      source: '17457361931830'
      sourceHandle: source
      target: '17493064534440'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 17493064534440-source-17493064676110-target
      source: '17493064534440'
      sourceHandle: source
      target: '17493064676110'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: loop
      id: 17493064676110-source-17458560149760-target
      source: '17493064676110'
      sourceHandle: source
      target: '17458560149760'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 17493068577120-source-17493068668040-target
      source: '17493068577120'
      sourceHandle: source
      target: '17493068668040'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: loop
      id: 17493068668040-source-1745736592974-target
      source: '17493068668040'
      sourceHandle: source
      target: '1745736592974'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 17457340486300-source-17494467467670-target
      source: '17457340486300'
      sourceHandle: source
      target: '17494467467670'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 17494467467670-source-17494467390530-target
      source: '17494467467670'
      sourceHandle: source
      target: '17494467390530'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 17494467390530-source-1749446865010-target
      source: '17494467390530'
      sourceHandle: source
      target: '1749446865010'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: loop
        targetType: end
      id: 17458560149760-source-1749438351154-target
      source: '17458560149760'
      sourceHandle: source
      target: '1749438351154'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: loop
        targetType: llm
      id: 17458542648830-source-17457361886690-target
      source: '17458542648830'
      sourceHandle: source
      target: '17457361886690'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1738727589378-source-17457341467330-target
      source: '1738727589378'
      sourceHandle: source
      target: '17457341467330'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: loop
        targetType: llm
      id: 17458554196650-source-17457361931830-target
      source: '17458554196650'
      sourceHandle: source
      target: '17457361931830'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - allowed_file_extensions: []
          allowed_file_types:
          - image
          allowed_file_upload_methods:
          - local_file
          - remote_url
          label: Profile
          max_length: 200000
          options: []
          required: false
          type: paragraph
          variable: Profile
      height: 90
      id: '1738727589378'
      position:
        x: -752.7436118746388
        y: 17.54449563349405
      positionAbsolute:
        x: -752.7436118746388
        y: 17.54449563349405
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# Meta-Prompt指令：【节点4a】多维分析综合与投资论点构建\n\n## 1. 角色与目标\n\n你将扮演投资公司的**首席投资官（Chief\
            \ Investment Officer, CIO）**。你的办公桌上放着三份经过了严酷审查和深度迭代的最终报告：\n1.  【节点1f】的《宏观与行业深度分析报告》\n\
            2.  【节点2f】的《产业链与利益相关者深度分析报告》\n3.  【节点3f】的《量化投资策略分析报告》\n\n你的唯一任务是，将这三份报告中的所有洞察、数据和结论，**编织（Weave）成一个单一、连贯、有说服力的核心投资论点（Investment\
            \ Thesis）**。你不再是分析师，而是战略家和故事讲述者，需要将所有碎片化的信息融合成一个引人入胜且逻辑严密的整体。\n\n## 2.\
            \ 核心原则\n\n* **叙事的力量（The Power of Narrative）**：一个好的投资论点是一个好故事。这个故事必须清晰地解释：世界发生了什么变化？为什么这个变化创造了一个被错误定价的机会？我们的核心洞察是什么？以及，未来会如何演变，从而让我们获利？\n\
            * **逻辑一致性**：确保从宏观、到产业、再到公司财务的分析是相互支撑、无缝衔接的。例如，宏观报告中提到的政策利好，必须能在产业链报告中找到受益环节，并最终体现在财务模型的收入增长假设中。\n\
            * **识别关键支柱**：明确指出支撑整个投资论点的3-5个最关键的“逻辑支柱”（Key Pillars）。如果其中任何一个支柱被证明是错误的，整个论点就会崩溃。\n\
            * **简洁与精炼**：尽管基础报告内容庞杂，但最终的投资论点必须能够被精炼成一段话（电梯演讲）、一页纸（One-Pager）和一份不超过五页的完整备忘录。\n\
            \n## 3. 投资论点构建评审委员会\n\n你构建的投资论点，必须能够说服公司最高决策层，即以下由超过30位顶级投资家和战略家组成的虚拟委员会。\n\
            \n* **主席团 (5位)**:\n    1.  **瑞·达利欧原型（桥水基金创始人）**: 审查论点的系统性和因果逻辑链。\n   \
            \ 2.  **霍华德·马克斯原型（橡树资本创始人）**: 审查论点是否体现了深刻的“第二层次思维”。\n    3.  **顶级管理咨询公司（如麦肯锡）全球高级合伙人原型**:\
            \ 审查论点的战略清晰度和叙事结构。\n    4.  **伯克希尔·哈撒韦副主席查理·芒格原型**: 审查论点是否基于理性的“普世智慧”和对人性的深刻理解。\n\
            \    5.  **顶级对冲基金创始人原型（如赛斯·卡拉曼）**: 审查论点是否充分体现了“安全边际”原则。\n* **投资组合管理组 (10位)**:\n\
            \    1.  大型共同基金明星基金经理原型（如彼得·林奇）。\n    2.  全球宏观对冲基金投资组合经理原型。\n    3.  价值投资基金首席投资官原型。\n\
            \    4.  成长投资基金首席投资官原型。\n    5.  量化多策略基金首席投资官原型。\n    6.  大型养老基金/主权财富基金首席投资官原型。\n\
            \    7.  家族办公室首席投资官原型。\n    8.  大学捐赠基金首席投资官原型（如耶鲁捐赠基金）。\n    9.  不良资产/特殊机会投资基金经理原型。\n\
            \    10. 风险投资（VC）/私募股权（PE）公司管理合伙人原型。\n* **战略与沟通组 (8位)**:\n    1.  顶级财经刊物（如《经济学人》）主编原型。\n\
            \    2.  上市公司CEO原型。\n    3.  企业战略发展部负责人原型。\n    4.  顶级投资者关系（IR）顾问原型。\n\
            \    5.  政治与公共政策战略家原型。\n    6.  金融史学家原型。\n    7.  顶级广告公司首席创意官原型（擅长提炼核心信息）。\n\
            \    8.  行为经济学家原型（分析论点如何利用或对抗市场心理）。\n* **各节点专家代表组 (8位)**:\n    1.  宏观经济学家代表（来自节点1）。\n\
            \    2.  供应链专家代表（来自节点2）。\n    3.  估值专家代表（来自节点3）。\n    4.  法务会计代表（来自红队）。\n\
            \    5.  做空机构代表（来自红队）。\n    6.  行业专家代表（来自相关节点）。\n    7.  另类数据专家代表。\n \
            \   8.  首席风险官代表。\n\n## 4. 任务指令\n\n1.  **接收输入**：我将向你提供【节点1f】, 【2f】, 【3f】三份最终报告的全文。\n\
            2.  **构建并输出投资论点备忘录**：收到报告后，你必须以CIO的身份，撰写一份结构清晰的投资论点备忘录，内容必须包括：\n    *\
            \ **第一部分：一页纸摘要（One-Page Summary）**\n        * **投资建议**：明确说明是“买入”、“卖出”还是“观望”某个特定标的。\n\
            \        * **核心论点（The Thesis）**：用不超过200字，清晰地阐述整个投资故事和核心逻辑。\n        *\
            \ **关键支柱（Key Pillars）**：列出支撑该论点的3-5个最关键的、可验证的判断。\n        * **主要风险（Key\
            \ Risks）**：列出可能导致该论点失败的3-5个主要风险。\n        * **估值与回报预测**：总结【节点3f】的结论，给出目标价格区间、预期回报率和投资时间框架。\n\
            \    * **第二部分：详细论证（Full Memorandum）**\n        * **情景设定（The Setup）**：详细描述事件如何打破了原有的市场均衡，创造了这次投资机会。引用【节点1f】的宏观和行业洞察。\n\
            \        * **我们的差异化洞察（Our Differentiated Insight）**：详细论述我们看到了什么市场没有看到的东西。深入整合【节点2f】的产业链分析和【节点3f】的量化分析，解释价值是如何被创造或毁灭的。\n\
            \        * **催化剂（Catalysts）**：列出未来可能发生的、能够促使市场重新认识到该标的价值的催化剂事件（如：财报发布、新产品发布、监管变化等）。\n\
            \        * **风险与对冲策略**：详细阐述每个主要风险，并引用【节点3f】的结论，提出初步的对冲思考。\n    * **第三部分：待最终评审的关键问题**\n\
            \        * 站在CIO的高度，提出你认为在最终决策前，必须在下一“谋杀板”节点（4b）被终极拷问的2-3个最核心、最致命的问题。\n\
            \n**输出格式要求**：\n* 使用Markdown格式，标题、列表、粗体等清晰明了。\n* 报告必须是独立的、完整的、高度精炼的。\n\
            * **绝对禁止**任何省略。\n* **不要**用编程的输出格式。\n\n请现在待命，等待我输入三份最终报告。\n\n节点1f:{{#17458542648830.First#}}\n\
            节点2f:{{#17458554196650.Second#}}\n节点3f:{{#17458560149760.Third#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 4
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17457340486300'
      position:
        x: -372.8475041002906
        y: 1026.1072595022215
      positionAbsolute:
        x: -372.8475041002906
        y: 1026.1072595022215
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: '# **AI投资分析师指令 (节点 1/4): 启动“真理之眼” - 广度问题构建**


            **角色设定：**

            你将扮演一个名为“真理之眼（Veritas-Eye）”的超级分析系统。你的身份是一个由超过20位全球顶级专家组成的联合评审委员会。在此初始节点，你的核心任务是：**基于一项给定的初始信息，从所有可能的专业角度，系统性地构建一个全面的、用于信息收集的“问题清单”。**


            **核心原则：**

            1.  **绝对事实中立：** 你提出的所有问题，旨在挖掘事实，而非诱导任何结论。

            2.  **广度优先，杜绝盲点：** 你的目标是全面覆盖，确保没有任何关键领域被遗漏。问题应涵盖公司、技术、市场、财务、法律、团队等所有维度。

            3.  **可操作性：** 问题应清晰、具体，指向需要被收集的明确信息点。

            4.  **多维视角融合：** 你必须模拟下方定义的每一个专家角色，从他们的专业视角出发，贡献独特的问题。


            **本节点任务：**

            根据下方“用户输入区”的初始信息，生成一份结构化的“第一轮信息收集问题清单”。这份清单将作为下一步进行外部信息收集的行动指南。


            **联合事实评审委员会名单 (虚拟)：**

            * **投资决策层 (4位):** 风险投资合伙人, 私募股权投资总监, 并购专家, 天使投资人.

            * **金融与财务分析 (5位):** 股票研究分析师, 法务会计师, 资产评估师, 投资银行家, 信用评级分析师.

            * **行业与技术专家 (6位):** 目标行业资深分析师, 首席技术官(CTO), 供应链管理专家, 产品管理总监, 研发科学家, 数据科学家.

            * **法律、合规与风险管理 (4位):** 公司法律师, 知识产权律师, 监管合规专家, ESG分析师.

            * **市场与战略 (3位):** 顶级战略顾问, 市场研究总监, 公共关系专家.

            * **背景调查专家 (1位):** 专业尽职调查顾问.


            **输出格式要求：**

            请将问题清单进行分类，例如：

            * **A. 核心信息真实性核查**

            * **B. 公司与团队背景**

            * **C. 技术与产品深度**

            * **D. 财务与运营状况**

            * **E. 市场与竞争格局**

            * **F. 法律、合规与风险**


            ---

            ---


            **【用户输入区】**


            **请在此处插入您希望分析的初始信息：{{#1738727589378.Profile#}}**


            ---

            ---


            **【AI输出区】**

            ** 输出不超过30个关键问题，要严格执行**

            **(你将在此处开始生成严格遵循上述所有指令的、结构化的“第一轮信息收集问题清单”)**'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17457341467330'
      position:
        x: -348.07656968587156
        y: 23.616954909370236
      positionAbsolute:
        x: -348.07656968587156
        y: 23.616954909370236
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: '# **AI投资分析师指令 (节点 1/4): 启动“卡桑德拉认知系统” - 预测数据需求构建**


            **角色设定：**

            你将扮演名为“卡桑德拉认知（Cassandra-Cognition）”的战略远见系统。你的身份是一个由超过20位全球顶级战略家和建模专家组成的联合委员会。在此初始节点，你的核心任务是：**基于一份已核实的“事实”，反向设计出一份全面的“信息收集清单”，这份清单的目标不是为了证实已知事实，而是为了收集足够的数据来预测和模拟这个事实可能引发的未来影响。**


            **核心原则：**

            1.  **面向未来提问：** 你提出的问题核心是“为了预测，我们需要测量什么？”。关注变化率、弹性、相关性、历史行为模式等动态指标，而非静态数值。

            2.  **系统变量导向：** 将分析对象视为一个复杂系统，提问旨在获取能定义该系统“初始条件”和“运行规则”的关键变量数据。

            3.  **广度优先，覆盖动态：** 确保问题覆盖所有可能发生相互作用的维度（产业、竞争、技术、资本、消费者、宏观），并着重于它们之间的联系。

            4.  **可操作性：** 问题应指向具体、可供研究和收集的数据点或信息类型。


            **本节点任务：**

            根据下方“用户输入区”的“最终事实全景报告”，生成一份结构化的“第一轮预测性数据需求清单”。这份清单将指导下一步的数据收集工作，其目的是为了建立一个初步的影响预测模型。


            **联合推演委员会名单 (虚拟)：**

            * **战略与决策层 (5位):** 首席战略官(CSO), 情景规划专家, 系统动力学建模专家, 资深风险投资家, 博弈论专家.

            * **金融与市场分析 (5位):** 宏观经济学家, 行为经济学家, 股票研究主管, 量化策略师, 数据策略师.

            * **行业与技术专家 (6位):** 目标行业首席分析师, 首席技术官(CTO), 供应链架构师, 产品战略副总裁, 颠覆性技术研究员,
            消费者洞察总监.

            * **法律、政策与社会 (4位):** 监管政策分析师, 反垄断法律专家, 地缘政治风险顾问, 社会学家.

            * **“红队”专家 (1位):** “红队”未来学家.


            **输出格式要求：**

            请将“数据需求清单”按以下类别进行组织：

            * **A. 市场与消费者动态数据** (如：价格弹性、用户采纳S曲线历史数据、品牌忠诚度量化指标)

            * **B. 竞争对手行为模式数据** (如：对新技术/新价格的历史反应时间与力度、研发投入趋势、高管公开发言分析)

            * **C. 产业链韧性数据** (如：供应商/客户集中度、替代方案成本与可行性、转换成本量化)

            * **D. 技术生态演化数据** (如：互补品发展速度、技术标准形成历史、专利交叉许可情况)

            * **E. 宏观与监管环境数据** (如：相关政策舆论风向、关键议员/机构立场、历史上类似监管的经济影响)


            ---

            ---


            **【用户输入区】**


            **请在此处完整粘贴经过您核实的“最终事实全景报告”：{{#17458542648830.First#}}**

            [在此处粘贴完整的报告内容]


            ---

            ---


            **【AI输出区】**

            ** 输出不超过30个关键问题，要严格执行**

            **(你将在此处开始生成严格遵循上述所有指令的、结构化的“第一轮预测性数据需求清单”)**'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17457361886690'
      position:
        x: -348.07656968587156
        y: 349.075800648781
      positionAbsolute:
        x: -348.07656968587156
        y: 349.075800648781
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# **AI投资分析师指令 (节点 1/4): 启动“阿尔法筛选器” - 二级市场筛选因子数据需求构建**\n\n**角色设定：**\n\
            你将扮演名为“阿尔法筛选器（Alpha-Screener）”的顶级二级市场投研系统。你的身份是一个由超过30位华尔街顶尖投资专家组成的量化策略委员会。在此初始节点，你的核心任务是：**严格专注于全球主要资本市场的公开交易证券（股票），基于一份“影响预判报告”，设计一个用于构建“量化筛选策略”的、具体的“数据字段需求清单”。你思考的不是模糊的机会，而是构建一个可执行的筛选器（Screener）所必需的精确数据点。**\n\
            \n**核心原则：**\n1.  **绝对二级市场聚焦：** 你的世界里只有公开交易的证券。严禁思考或提及任何一级市场、风险投资、未上市公司。你的最小分析单位是“股票代码（Ticker\
            \ Symbol）”。\n2.  **量化与可执行：** 你的提问旨在获取精确、可量化的数据字段，这些字段可以直接输入金融终端（如Bloomberg,\
            \ Refinitiv）的筛选器中。\n3.  **策略导向提问：** 你的数据需求必须服务于发现“成长稳健”和“高弹性”这两类交易策略的目标。\n\
            4.  **全球化视野：** 你的数据需求应考虑到全球主要交易所（如NYSE, NASDAQ, HKEX, LSE等）的上市公司。\n\n\
            **本节点任务：**\n根据下方“用户输入区”的“最终影响与变化预判报告”，生成一份结构化的“第一轮量化筛选数据需求清单”。这份清单的目标是收集足够的数据，以编程方式构建一个广泛的“上市公司候选宇宙”。\n\
            \n**联合投研委员会名单 (虚拟, 超过30位)：**\n* **投资决策委员会 (5位):** 对冲基金CIO, 共同基金PM, 首席投资策略师,\
            \ 全球宏观投资主管, 风险管理委员会主席.\n* **股票研究部 (12位):** 研究部总监, 成长股/价值股/事件驱动策略主管, TMT/医疗/消费/工业/新能源/金融行业首席分析师,\
            \ 小盘股专家, 资深买方分析师.\n* **量化与交易部 (6位):** 量化策略部主管, 统计套利专家, 股票交易部主管, 衍生品交易专家,\
            \ 市场微观结构研究员, 算法交易开发负责人.\n* **外部专家顾问团 (8位):** 顶级卖方研究所所长, 华尔街资深经济学家, 监管政策前沿顾问,\
            \ 法务会计专家, 供应链咨询专家, 技术专利律师, 企业战略顾问, 地缘政治分析师.\n\n**输出格式要求：**\n请将“数据字段需求清单”按以下策略意图进行组织：\n\
            * **A. 构建“成长稳健”候选池的筛选因子需求:**\n    * **需求1:** [例如：针对[相关行业]，请提供一个包含以下字段的数据表：公司名称、股票代码(Ticker)、交易所、市值(Market\
            \ Cap)、过去3年营收复合增长率(Revenue CAGR)、过去3年净利润复合增长率(Net Income CAGR)、最新财报的股本回报率(ROE)和投入资本回报率(ROIC)。]\n\
            \    * **需求2:** [例如：请提供上述公司的分析师评级共识(Analyst Consensus Rating)和平均目标价(Average\
            \ Price Target)。]\n* **B. 构建“高弹性”候选池的筛选因子需求:**\n    * **需求1:** [例如：针对[相关行业]，请提供一个包含以下字段的数据表：公司名称、股票代码、最新财报的毛利率(Gross\
            \ Margin)和营业利润率(Operating Margin)，用于计算经营杠杆。]\n    * **需求2:** [例如：请提供上述公司的最新卖空股数占流通股比例(Short\
            \ Interest Ratio)和贝塔系数(Beta)。]\n* **C. 通用估值与财务健康数据需求:**\n    * **需求1:**\
            \ [例如：请提供上述所有公司的静态市盈率(P/E TTM)、远期市盈率(Forward P/E)、市净率(P/B)以及资产负债率(Debt-to-Equity\
            \ Ratio)。]\n\n---\n---\n\n**【用户输入区】**\n\n**请在此处完整粘贴经过您迭代验证的“最终影响与变化预判报告”：{{#17458554196650.Second#}}**\n\
            [在此处粘贴完整的报告内容]\n\n---\n---\n\n**【AI输出区】**\n** 输出共计不超过30个关键问题，要严格执行**\n\
            **(你将在此处开始生成严格遵循上述所有指令的、结构化的、100%聚焦二级市场的“第一轮量化筛选数据需求清单”)**"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17457361931830'
      position:
        x: -372.8475041002906
        y: 659.0067501745364
      positionAbsolute:
        x: -372.8475041002906
        y: 659.0067501745364
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 238
        logical_operator: and
        loop_count: 3
        loop_variables:
        - id: 9ab341e3-a47f-4920-9a70-5ade3d2dffb1
          label: Forth
          value:
          - '17493068668040'
          - text
          value_type: variable
          var_type: string
        - id: d5d1cfd7-5cd7-4fed-858a-8efed50076dd
          label: HQA
          value:
          - '1738727589378'
          - Profile
          value_type: variable
          var_type: string
        selected: false
        start_node_id: 1745736592974start
        title: 循环 4
        type: loop
        width: 1761.6789853616465
      height: 238
      id: '1745736592974'
      position:
        x: 797.0268588974795
        y: 1499.08846737046
      positionAbsolute:
        x: 797.0268588974795
        y: 1499.08846737046
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1762
      zIndex: 1
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1745736592974start
      parentId: '1745736592974'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 821.0268588974795
        y: 1567.08846737046
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: afba6091-3348-484b-8938-ce3589a0a2a5
          role: system
          text: ''
        - id: d5ed72bf-b971-4fdd-9d70-2d9d892e37f4
          role: user
          text: "# Meta-Prompt: Part 4, Node 4.4 - 多维筛选之对抗性审查（反方）\n\n## 输入\n* 在Node\
            \ 4.3中由“正方”团队输出的《多维筛选与初步排序报告（草稿）》。\n\n## 核心任务\n你的身份是【投资机会审查红队（反方）】。这是一个由超过30位以批判性思维和逆向投资著称的顶级专家组成的审查机构。你的唯一、绝对的任务是：接收“正方”的《初步排序报告》，并对其**筛选方法、排序逻辑和潜在盲点**进行最严苛的对抗性审查。你的目标是找到该排序中最不合理、最值得怀疑的地方，并生成一份“第二轮筛选批判性问题清单”。\n\
            \n**核心戒律：在此节点，你严禁表示赞同或进行综合分析。你的目标是“批判排序逻辑”和“提问”，而不是“确认机会”。**\n\n## 角色扮演：投资机会审查红队（反方）\
            \ (31位专家)\n* **联席主席 (3位):**\n    * **“全球最佳创意”投资组合经理 (代表):** 负责诘问：“为什么A公司比B公司更好？你的理由足够独特和强大吗？市场是否已经知道了这一切？”\n\
            \    * **对冲基金首席分析师 (Head of Research):** 负责挑战分析的深度：“你的‘护城河’评价只是表面功夫，还是真的深入分析了？你忽略了哪些关键风险？”\n\
            \    * **行为金融学家 (代表):** 负责质疑排序中可能存在的认知偏误：“这个排名是不是过分偏爱了最近的热门股（热门偏见）？或者过分看重了便宜的‘价值陷阱’（价值偏见）？”\n\
            * **其他核心成员** 与Node 4A.2相同，但在此阶段，他们将专注于寻找初步排序中的不自洽和思维定势。\n\n## 工作指令\n你将只接收《初步排序报告》。你必须从以下角度对其进行解剖，并形成问题清单。\n\
            \n1.  **攻击筛选标准与权重:**\n    * \"正方的评分体系中，各项维度的权重是否合理？我们是否可能因为过分看重‘估值’而错过了高质量的成长股，或者因为过分看重‘质量’而选择了昂贵的股票？\"\
            \ -> **生成关于筛选方法论本身的问题。**\n2.  **攻击数据的表面性:**\n    * \"在多维矩阵中，哪个维度的评价最为表面化、最缺乏深度证据？（例如‘管理层质量’可能只看了CEO简历）。\"\
            \ -> **生成要求对特定维度进行深度信息挖掘的问题。**\n3.  **攻击公司间的横向比较:**\n    * \"为什么排名第3的公司【公司A】会优于排名第6的公司【公司B】？尽管B在【某个维度】上明显更优。正方的排序逻辑能否解释这个矛盾？\"\
            \ -> **生成要求对关键公司进行“头对头”(Head-to-Head)比较的问题。**\n4.  **寻找“被筛选掉的遗珠”:**\n\
            \    * \"是否存在某些公司，因为某一个硬性指标不达标（例如，资产负债率51%）而被筛掉，但它在其他方面（如颠覆性技术）极具潜力？我们的筛选是否过于僵化？\"\
            \ -> **生成关于“例外案例”的研究问题。**\n5.  **寻找“高排名公司的隐藏风险”:**\n    * \"排名最高的【公司X】，是否存在一个我们的多维矩阵没有捕捉到的、致命的隐藏风险？（如：客户过于集中、面临潜在诉讼、技术路径可能被替代等）。\"\
            \ -> **生成对高排名公司进行“定向排雷”的问题。**\n\n## 输出要求\n* 以Markdown格式，输出一份尖锐、深刻、直击排序逻辑要害的“第二轮筛选批判性问题清单”。\n\
            * 请输出不超过50个关键问题（严格执行）。\n\n《多维筛选与初步排序报告（草稿）》：{{#1745736592974.Forth#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 4
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745736635063'
      parentId: '1745736592974'
      position:
        x: 90.48716684160695
        y: 65
      positionAbsolute:
        x: 887.5140257390865
        y: 1564.08846737046
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        is_team_authorization: true
        loop_id: '1745736592974'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: otherinfo
            ja_JP: otherinfo
            pt_BR: otherinfo
            zh_Hans: otherinfo
          llm_description: ''
          max: null
          min: null
          name: otherinfo
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          otherinfo: ''
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          otherinfo:
            type: mixed
            value: '{{#1738727589378.Profile#}}'
          questions:
            type: mixed
            value: '{{#1745736635063.text#}}'
        type: tool
      height: 54
      id: '1745736689919'
      parentId: '1745736592974'
      position:
        x: 368.55595460468464
        y: 70.01917717128963
      positionAbsolute:
        x: 1165.5828135021643
        y: 1569.1076445417496
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        selected: false
        template: '{{"New QA in this round"}}

          {{ NQA }}

          {{"History QA"}}

          {{ HQA }}'
        title: QA-LIST整合
        type: template-transform
        variables:
        - value_selector:
          - '1745736592974'
          - HQA
          variable: HQA
        - value_selector:
          - '1745736635063'
          - text
          variable: NQA
      height: 54
      id: '1745736788778'
      parentId: '1745736592974'
      position:
        x: 692.5908050098149
        y: 71.7444612918858
      positionAbsolute:
        x: 1489.6176639072944
        y: 1570.8329286623457
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1745736592974'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 8d7a4a86-46ab-4a51-8334-363baf4d6c75
          role: system
          text: ''
        - id: a6639e79-5d07-44a0-8d51-52fe7df6858e
          role: user
          text: "# Meta-Prompt: Part 4, Node 4.6 - 多维筛选之综合研判（合题）\n\n## 输入\n1.  由“正方”在Node\
            \ 4.3中完成的《多维筛选与初步排序报告（草稿）》。\n2.  由“反方”在Node 4.4中生成的“第二轮筛选批判性问题清单”。\n3.\
            \  根据该问题清单、在Node 4.5中挖掘到的【第二轮补充数据和信息】。\n\n## 核心任务\n你的身份是【最终投资机会评审委员会】。这是一个由与Node\
            \ 4A.2相同配置的、超过30位具备极高智慧和决策能力的顶级专家组成的终审机构。你的任务是扮演最终的决策者，同时审视“正方”的初步排序、“反方”的严厉诘问、以及“新一轮的证据”，以达成一个经过辩证过程的、最终的“**Top\
            \ 3-5 优先关注投资机会**”名单，并为这个最终选择提供强有力的支持理由。\n\n## 角色扮演：最终投资机会评审委员会 (31位专家)\n\
            * **联席主席 (3位):**\n    * **乔尔·格林布拉特:** 负责结合量化筛选和定性洞察，做出最终判断。\n    * **“全球最佳创意”投资组合经理:**\
            \ 负责从最终名单中挑选出信念最强、最具吸引力的机会。\n    * **美国最高法院首席大法官 (模拟):** 负责权衡所有正反方证据，引导委员会做出最公正、最理性的最终排序。\n\
            * **其他核心成员** 在此阶段，将展现出自己最成熟、最全面、最客观的判断力。\n\n## 工作指令\n你必须严格按照以下结构，完成并输出最终的《定稿》报告。\n\
            \n1.  **辩证筛选过程概述:**\n    * 简要陈述“正方”的初步排序逻辑。\n    * 简要陈述“反方”提出的最核心的几个质疑（例如：关于权重合理性的质疑、关于横向比较不充分的质疑）。\n\
            \    * 总结“第二轮补充证据”是如何帮助委员会更深入、更平衡地进行比较和排序的。\n2.  **关键争议点的裁决与排序逻辑的修正:**\n\
            \    * 明确指出新的证据如何影响了最初的评分和排序。（例如：“补充证据显示，公司A的管理层远比我们想象的优秀，因此我们上调了其‘管理层质量’评分，其最终排名得以提升。”）\n\
            3.  **最终的“Top 3-5 优先关注投资机会”名单:**\n    * **输出最终名单:** 以Markdown表格形式，列出最终选定的Top\
            \ 3-5个投资机会（可包含做多和做空方向）。\n    | 优先级排名 | 公司名称/代码 | 方向 (做多/做空) | 核心投资亮点 (一句话总结)\
            \ | 主要风险点 (一句话总结) |\n    | :--- | :--- | :--- | :--- | :--- |\n4.  **详细的比较分析与入选理由:**\n\
            \    * 为最终入选的**每一家公司**，撰写一段详尽的“**入选白皮书**”。\n    * 这段白皮书必须清晰地阐述，**为什么这家公司在多维度比较中脱颖而出**，它相对于名单上其他公司（包括未入选的）的独特优势是什么。论述必须结合战略、质量、财务、管理和估值五个维度。\n\
            5.  **被放弃的“荣誉提名”:**\n    * 简要提及1-2家在最后一轮被淘汰的公司，并说明它们虽然优秀，但最终未能入选Top 3-5的核心原因（例如：“公司C虽然质量很高，但估值相比Top\
            \ 3没有吸引力，因此作为荣誉提名，继续观察。”）。这体现了决策的权衡过程。\n\n## 输出要求\n* 以Markdown格式，输出一份逻辑严密、论证清晰、包含明确的Top\
            \ 3-5名单以及详尽比较理由的、最终的《Top 3-5 优先关注投资机会报告》。\n\n《多维筛选与初步排序报告（草稿）》：{{#1745736592974.Forth#}}\n\
            第二轮筛选批判性问题清单：{{#1745736635063.text#}}\n第二轮补充数据和信息：{{#1745736689919.text#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 41
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745736973218'
      parentId: '1745736592974'
      position:
        x: 998.3862351915275
        y: 73.53989147359859
      positionAbsolute:
        x: 1795.413094089007
        y: 1572.6283588440585
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1745736973218'
          - text
          variable_selector:
          - '1745736592974'
          - Forth
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1745736788778'
          - output
          variable_selector:
          - '1745736592974'
          - HQA
          write_mode: over-write
        loop_id: '1745736592974'
        selected: false
        title: 变量赋值 4
        type: assigner
        version: '2'
      height: 116
      id: '1745737138928'
      parentId: '1745736592974'
      position:
        x: 1304.1816653732403
        y: 73.53989147359857
      positionAbsolute:
        x: 2101.20852427072
        y: 1572.6283588440585
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17458542648830'
          - First
          variable: First
        - value_selector:
          - '17458554196650'
          - Second
          variable: Second
        - value_selector:
          - '17458560149760'
          - Third
          variable: Third
        - value_selector:
          - '1745736592974'
          - Forth
          variable: Forth
        selected: false
        title: 结束
        type: end
      height: 168
      id: '1745766730888'
      position:
        x: 2456.077597309536
        y: 991.0844938791156
      positionAbsolute:
        x: 2456.077597309536
        y: 991.0844938791156
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 238
        logical_operator: and
        loop_count: 2
        loop_variables:
        - id: 9ab341e3-a47f-4920-9a70-5ade3d2dffb1
          label: First
          value:
          - '17473249427250'
          - text
          value_type: variable
          var_type: string
        - id: d5d1cfd7-5cd7-4fed-858a-8efed50076dd
          label: HQA
          value:
          - '1738727589378'
          - Profile
          value_type: variable
          var_type: string
        selected: false
        start_node_id: 1745854264883start
        title: 循环 1
        type: loop
        width: 1761.6789853616465
      height: 238
      id: '17458542648830'
      position:
        x: 607.8399755837559
        y: 17.54449563349405
      positionAbsolute:
        x: 607.8399755837559
        y: 17.54449563349405
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1762
      zIndex: 1
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: afba6091-3348-484b-8938-ce3589a0a2a5
          role: system
          text: ''
        - id: d5ed72bf-b971-4fdd-9d70-2d9d892e37f4
          role: user
          text: '# **AI投资分析师指令 (节点 3/4): 启动“真理之眼” - 深度问题钻探**


            **角色设定：**

            你将扮演一个名为“真理之眼（Veritas-Eye）”的超级分析系统。你的身份是一个由超过20位全球顶级专家组成的、进入“红队（Red Team）”模式的联合评审委员会。在此批判性节点，你的核心任务是：**对上一节点生成的“初步事实分析报告”进行压力测试，提出一系列深度的、证伪式的、旨在挑战现有分析和挖掘隐藏风险的“第二轮追问清单”。**


            **核心原则：**

            1.  **批判性思维（证伪导向）：** 你的提问不应是“还需要什么”，而应是“为了验证/推翻某个初步结论，我需要看到什么证据？”。主动挑战报告中的薄弱环节。

            2.  **深度优先，追根究底：** 针对报告中已识别的疑点、矛盾和风险点，进行穷追猛打式的提问，要求更深层次、更具体的数据和事实。

            3.  **情景化与压力测试：** 提出一些基于特定负面情景的假设性问题，以探测分析对象的脆弱性。例如：“如果关键供应商断供，备用方案是什么？请提供相关协议证明。”

            4.  **多维视角融合：** 模拟下方专家团进行一次激烈的内部辩论，从各自最挑剔的角度提出质疑，并将这些质疑转化为具体问题。


            **本节点任务：**

            仔细研读下方“用户输入区”粘贴的“初步事实分析报告”，特别是其“已识别的疑点、矛盾与信息缺口”章节，然后生成一份高度针对性的“第二轮深度追问清单”。


            **联合事实评审委员会名单 (虚拟)：**

            * **投资决策层 (4位):** 风险投资合伙人, 私募股权投资总监, 并购专家, 天使投资人.

            * **金融与财务分析 (5位):** 股票研究分析师, 法务会计师, 资产评估师, 投资银行家, 信用评级分析师.

            * **行业与技术专家 (6位):** 目标行业资深分析师, 首席技术官(CTO), 供应链管理专家, 产品管理总监, 研发科学家, 数据科学家.

            * **法律、合规与风险管理 (4位):** 公司法律师, 知识产权律师, 监管合规专家, ESG分析师.

            * **市场与战略 (3位):** 顶级战略顾问, 市场研究总监, 公共关系专家.

            * **背景调查专家 (1位):** 专业尽职调查顾问.


            **输出格式要求：**

            问题清单应直接关联初步报告中的发现，可以按原报告结构进行分类，但问题本身需更具穿透力和挑战性。


            ---

            ---


            **【用户输入区】**


            **请在此处完整粘贴节点2生成的“初步事实分析报告”：{{#17458542648830.First#}}**

            [在此处粘贴完整的报告内容]


            ---

            ---


            **【AI输出区】**

            ** 输出不超过30个关键问题，要严格执行**

            **(你将在此处开始生成严格遵循上述所有指令的、具有深度和挑战性的“第二轮深度追问清单”)**'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745854264883017458542648830'
      parentId: '17458542648830'
      position:
        x: 93.41622333960004
        y: 65
      positionAbsolute:
        x: 701.2561989233559
        y: 82.54449563349405
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        is_team_authorization: true
        loop_id: '17458542648830'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: otherinfo
            ja_JP: otherinfo
            pt_BR: otherinfo
            zh_Hans: otherinfo
          llm_description: ''
          max: null
          min: null
          name: otherinfo
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          otherinfo: ''
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          otherinfo:
            type: mixed
            value: '{{#1738727589378.Profile#}}'
          questions:
            type: mixed
            value: '{{#1745854264883017458542648830.text#}}'
        type: tool
      height: 54
      id: '1745854264883017458542648841'
      parentId: '17458542648830'
      position:
        x: 368.55595460468464
        y: 65
      positionAbsolute:
        x: 976.3959301884405
        y: 82.54449563349405
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        selected: false
        template: '{{"New QA in this round"}}

          {{ NQA }}

          {{"History QA"}}

          {{ HQA }}'
        title: QA-LIST整合
        type: template-transform
        variables:
        - value_selector:
          - '17458542648830'
          - HQA
          variable: HQA
        - value_selector:
          - '1745854264883017458542648830'
          - text
          variable: NQA
      height: 54
      id: '1745854264883017458542648842'
      parentId: '17458542648830'
      position:
        x: 696.1501419524639
        y: 71.74446129188578
      positionAbsolute:
        x: 1303.9901175362197
        y: 89.28895692537984
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458542648830'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 8d7a4a86-46ab-4a51-8334-363baf4d6c75
          role: system
          text: ''
        - id: a6639e79-5d07-44a0-8d51-52fe7df6858e
          role: user
          text: '# **AI投资分析师指令 (节点 4/4): 启动“真理之眼” - 最终事实合成**


            **角色设定：**

            你将扮演一个名为“真理之眼（Veritas-Eye）”的超级分析系统。你的身份是一个由超过20位全球顶级专家组成的联合评审委员会。在此最终节点，你的核心任务是：**整合所有已知信息（初始信息、第一轮信息、第二轮信息），消除或确认疑点，形成一份最全面、最客观、最接近真相的“最终事实全景报告”。**


            **核心原则：**

            1.  **绝对事实中立：** 这是最终的事实陈述。严禁包含任何投资建议、评级、乐观/悲观的语气或任何形式的价值判断。

            2.  **信息演变追溯：** 报告中需体现分析的动态过程，即明确说明第二轮的信息是如何证实、修正、或推翻了初步分析中的判断。

            3.  **完整性与透明度：** 最终报告必须整合所有轮次的信息，并清晰、诚实地列出即使在深度挖掘后，仍然存在的信息真空或无法验证的断言。

            4.  **多维视角融合：** 你必须模拟下方专家团的最终合议，确保所有专家的视角都被充分整合，形成一个统一、自洽、无懈可击的事实画面。


            **本节点任务：**

            基于下方“用户输入区”提供的“初步事实分析报告”和“第二轮问题回答”，生成终极的、结构化的“最终事实全景报告”。


            **联合事实评审委员会名单 (虚拟)：**

            * **投资决策层 (4位):** 风险投资合伙人, 私募股权投资总监, 并购专家, 天使投资人.

            * **金融与财务分析 (5位):** 股票研究分析师, 法务会计师, 资产评估师, 投资银行家, 信用评级分析师.

            * **行业与技术专家 (6位):** 目标行业资深分析师, 首席技术官(CTO), 供应链管理专家, 产品管理总监, 研发科学家, 数据科学家.

            * **法律、合规与风险管理 (4位):** 公司法律师, 知识产权律师, 监管合规专家, ESG分析师.

            * **市场与战略 (3位):** 顶级战略顾问, 市场研究总监, 公共关系专家.

            * **背景调查专家 (1位):** 专业尽职调查顾问.


            **最终报告结构要求：**

            1.  **最终执行摘要 (纯事实总结)**

            2.  **分析历程演变：** (简述初步发现，以及第二轮信息带来的关键变化)

            3.  **【最终版】主体背景扫描**

            4.  **【最终版】产品与技术解构**

            5.  **【最终版】财务与运营透视**

            6.  **【最终版】市场与竞争全景**

            7.  **【最终版】法律、合规与风险评估**

            8.  **【关键章节】最终未解问题与信息真空地带** (明确列出经过两轮调查后仍无法确认的事项)


            ---

            ---


            **【用户输入区】**


            **请在此处粘贴节点2的报告以及对节点3问题的回答：**


            **1. 初步事实分析报告 (来自节点2的完整输出):{{#17458542648830.First#}}**

            [在此处粘贴完整的报告内容]


            **2. 第二轮信息收集结果 (对节点3问题的回答):{{#1745854264883017458542648841.text#}}**

            [在此处逐条或整体粘贴您为节点3问题清单收集到的所有信息]


            ---

            ---


            **【AI输出区】**


            **(你将在此处开始生成严格遵循上述所有指令的、终极的、结构化的“最终事实全景报告”)**'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 11
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745854264883017458542648843'
      parentId: '17458542648830'
      position:
        x: 998.3862351915275
        y: 73.53989147359859
      positionAbsolute:
        x: 1606.2262107752833
        y: 91.08438710709264
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1745854264883017458542648843'
          - text
          variable_selector:
          - '17458542648830'
          - First
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1745854264883017458542648842'
          - output
          variable_selector:
          - '17458542648830'
          - HQA
          write_mode: over-write
        loop_id: '17458542648830'
        selected: false
        title: 变量赋值 1
        type: assigner
        version: '2'
      height: 116
      id: '1745854264883017458542648844'
      parentId: '17458542648830'
      position:
        x: 1314.983957707877
        y: 73.998579361124
      positionAbsolute:
        x: 1922.8239332916328
        y: 91.54307499461805
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1745854264883start
      parentId: '17458542648830'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 631.8399755837559
        y: 85.54449563349405
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 238
        logical_operator: and
        loop_count: 2
        loop_variables:
        - id: 9ab341e3-a47f-4920-9a70-5ade3d2dffb1
          label: Second
          value:
          - '17493058407690'
          - text
          value_type: variable
          var_type: string
        - id: d5d1cfd7-5cd7-4fed-858a-8efed50076dd
          label: HQA
          value:
          - '1738727589378'
          - Profile
          value_type: variable
          var_type: string
        selected: false
        start_node_id: 1745855419665start
        title: 循环 2
        type: loop
        width: 1761.6789853616465
      height: 238
      id: '17458554196650'
      position:
        x: 598.0057381209135
        y: 361.2732990252214
      positionAbsolute:
        x: 598.0057381209135
        y: 361.2732990252214
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1762
      zIndex: 1
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: afba6091-3348-484b-8938-ce3589a0a2a5
          role: system
          text: ''
        - id: d5ed72bf-b971-4fdd-9d70-2d9d892e37f4
          role: user
          text: "# **AI投资分析师指令 (节点 3/4): 启动“卡桑德拉认知系统” - 预测假设压力测试**\n\n**角色设定：**\n\
            你将扮演名为“卡桑德拉认知（Cassandra-Cognition）”的战略远见系统。你的身份是一个进入“红队（Red Team）”攻击模式的联合推演委员会。在此批判性节点，你的核心任务是：**对上一节点生成的“初步影响预测报告”进行系统性的、证伪式的压力测试，并生成一份旨在验证或推翻核心预测假设的“第二轮深度数据需求清单”。**\n\
            \n**核心原则：**\n1.  **证伪导向：** 你的提问旨在寻找能够“证伪”或“挑战”初步预测的证据。核心思路是：“如果这个预测是错的，我们应该会看到什么样的数据？”\n\
            2.  **攻击关键假设：** 集中火力攻击上一份报告中“核心假设与低置信度环节清单”里列出的条目。\n3.  **深挖因果关系：** 提问应超越相关性，探究因果。例如：“初步预测A和B正相关，请设计一个问题，以收集能证明其因果关系或排除混杂变量的数据。”\n\
            4.  **量化不确定性：** 提问的目标是获取能够缩小预测区间、提高置信度的数据。\n\n**本节点任务：**\n仔细研读下方“用户输入区”粘贴的“初步影响预测报告”，然后生成一份高度针对性的“第二轮深度数据需求清单”。这份清单的唯一目的，就是对初步预测进行最严苛的检验。\n\
            \n**联合推演委员会名单 (虚拟)：**\n* **战略与决策层 (5位):** 首席战略官(CSO), 情景规划专家, 系统动力学建模专家,\
            \ 资深风险投资家, 博弈论专家.\n* **金融与市场分析 (5位):** 宏观经济学家, 行为经济学家, 股票研究主管, 量化策略师,\
            \ 数据策略师.\n* **行业与技术专家 (6位):** 目标行业首席分析师, 首席技术官(CTO), 供应链架构师, 产品战略副总裁,\
            \ 颠覆性技术研究员, 消费者洞察总监.\n* **法律、政策与社会 (4位):** 监管政策分析师, 反垄断法律专家, 地缘政治风险顾问,\
            \ 社会学家.\n* **“红队”专家 (1位):** “红队”未来学家.\n\n**输出格式要求：**\n数据需求清单应直接对应初步报告中的某项预测或假设，并明确说明需要这份数据的“验证目的”。格式如下：\n\
            * **针对【初步预测X】的验证需求：**\n    * **数据需求1：** [具体的数据或信息需求]\n    * **验证目的：**\
            \ [说明该数据如何能够证实、证伪或修正初步预测X]\n\n---\n---\n\n**【用户输入区】**\n\n**请在此处完整粘贴节点2生成的“初步影响预测报告”：{{#17458554196650.Second#}}**\n\
            [在此处粘贴完整的报告内容]\n\n---\n---\n\n**【AI输出区】**\n** 输出不超过30个关键问题，要严格执行**\n**(你将在此处开始生成严格遵循上述所有指令的、以“证伪”和“压力测试”为核心的“第二轮深度数据需求清单”)**"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745855419665017458554196650'
      parentId: '17458554196650'
      position:
        x: 93.41622333960004
        y: 65
      positionAbsolute:
        x: 691.4219614605136
        y: 426.2732990252214
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        is_team_authorization: true
        loop_id: '17458554196650'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: otherinfo
            ja_JP: otherinfo
            pt_BR: otherinfo
            zh_Hans: otherinfo
          llm_description: ''
          max: null
          min: null
          name: otherinfo
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          otherinfo: ''
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          otherinfo:
            type: mixed
            value: '{{#1738727589378.Profile#}}'
          questions:
            type: mixed
            value: '{{#1745855419665017458554196650.text#}}'
        type: tool
      height: 54
      id: '1745855419665017458554196651'
      parentId: '17458554196650'
      position:
        x: 368.55595460468464
        y: 65
      positionAbsolute:
        x: 966.5616927255982
        y: 426.2732990252214
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        selected: false
        template: '{{"New QA in this round"}}

          {{ NQA }}

          {{"History QA"}}

          {{ HQA }}'
        title: QA-LIST整合
        type: template-transform
        variables:
        - value_selector:
          - '17458554196650'
          - HQA
          variable: HQA
        - value_selector:
          - '1745855419665017458554196650'
          - text
          variable: NQA
      height: 54
      id: '1745855419665017458554196652'
      parentId: '17458554196650'
      position:
        x: 692.5908050098149
        y: 71.7444612918858
      positionAbsolute:
        x: 1290.5965431307284
        y: 433.01776031710716
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458554196650'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 8d7a4a86-46ab-4a51-8334-363baf4d6c75
          role: system
          text: ''
        - id: a6639e79-5d07-44a0-8d51-52fe7df6858e
          role: user
          text: '# **AI投资分析师指令 (节点 4/4): 启动“卡桑德拉认知系统” - 高置信度影响合成**


            **角色设定：**

            你将扮演名为“卡桑德拉认知（Cassandra-Cognition）”的战略远见系统。你的身份是一个完成了多轮推演与验证的联合委员会。在此最终节点，你的核心任务是：**整合所有信息（核心事实、两轮数据），对初步影响预测进行修正、确认或推翻，形成一份置信度更高、逻辑链条更完整的“最终影响与变化预判报告”。**


            **核心原则：**

            1.  **综合与裁决：** 基于第二轮的深度数据，对初步预测进行最终“裁决”——是证实了，还是证伪了，亦或是需要进行何种程度的修正。

            2.  **预判的演进：** 报告中需清晰展示预判的演化过程。即，第二轮数据是如何改变了我们对未来的看法。

            3.  **量化置信度：** 对最终的关键预判，给出更新后的“置信度”评级（高/中/低），并简要说明评级依据。

            4.  **聚焦关键，并承认未知：** 最终提炼出经过验证后，仍然成立的“核心影响与关键变化”，同时诚实地列出即使经过两轮调查，仍然存在的关键不确定性和未知变量。


            **本节点任务：**

            基于下方“用户输入区”提供的“初步影响预测报告”和“第二轮数据回答”，生成终极的、结构化的“最终影响与变化预判报告”。


            **联合推演委员会名单 (虚拟)：**

            * **战略与决策层 (5位):** 首席战略官(CSO), 情景规划专家, 系统动力学建模专家, 资深风险投资家, 博弈论专家.

            * **金融与市场分析 (5位):** 宏观经济学家, 行为经济学家, 股票研究主管, 量化策略师, 数据策略师.

            * **行业与技术专家 (6位):** 目标行业首席分析师, 首席技术官(CTO), 供应链架构师, 产品战略副总裁, 颠覆性技术研究员,
            消费者洞察总监.

            * **法律、政策与社会 (4位):** 监管政策分析师, 反垄断法律专家, 地缘政治风险顾问, 社会学家.

            * **“红队”专家 (1位):** “红队”未来学家.


            **最终报告结构要求：**

            1.  **最终执行摘要 (总结经过验证的核心影响预判)**

            2.  **预判演进与修正说明** (说明第二轮数据如何更新了初步报告)

            3.  **【最终版】高置信度影响链分析** (展示经过验证和修正后的一、二、三阶影响)

            4.  **【最终版】关键影响与变化清单** (列出5-8个最终筛选出的、置信度最高的关键影响，并附上置信度评级和简要依据)

            5.  **【关键章节】剩余核心不确定性** (明确列出即使经过深度调查，仍然无法确定、并可能颠覆预判的关键变量)


            ---

            ---


            **【用户输入区】**


            **请在此处粘贴节点2的报告以及对节点3问题的回答：**


            **1. 初步影响预测报告 (来自节点2的完整输出):{{#17458554196650.Second#}}**

            [在此处粘贴完整的报告内容]


            **2. 第二轮深度数据收集结果 (对节点3问题的回答):{{#1745855419665017458554196651.text#}}**

            [在此处逐条或整体粘贴您为节点3数据需求清单收集到的所有信息]


            ---

            ---


            **【AI输出区】**


            **(你将在此处开始生成严格遵循上述所有指令的、终极的、经过迭代验证的“最终影响与变化预判报告”)**'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 21
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745855419665017458554196653'
      parentId: '17458554196650'
      position:
        x: 981.4799283010343
        y: 65
      positionAbsolute:
        x: 1579.4856664219478
        y: 426.2732990252214
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1745855419665017458554196653'
          - text
          variable_selector:
          - '17458554196650'
          - Second
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1745855419665017458554196652'
          - output
          variable_selector:
          - '17458554196650'
          - HQA
          write_mode: over-write
        loop_id: '17458554196650'
        selected: false
        title: 变量赋值 2
        type: assigner
        version: '2'
      height: 116
      id: '1745855419665017458554196654'
      parentId: '17458554196650'
      position:
        x: 1304.1816653732403
        y: 73.53989147359857
      positionAbsolute:
        x: 1902.1874034941538
        y: 434.81319049881995
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1745855419665start
      parentId: '17458554196650'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 622.0057381209135
        y: 429.2732990252214
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 238
        logical_operator: and
        loop_count: 3
        loop_variables:
        - id: 9ab341e3-a47f-4920-9a70-5ade3d2dffb1
          label: Third
          value:
          - '17493064676110'
          - text
          value_type: variable
          var_type: string
        - id: d5d1cfd7-5cd7-4fed-858a-8efed50076dd
          label: HQA
          value:
          - '1738727589378'
          - Profile
          value_type: variable
          var_type: string
        selected: false
        start_node_id: 1745856014976start
        title: 循环 3
        type: loop
        width: 1761.6789853616465
      height: 238
      id: '17458560149760'
      position:
        x: 598.0057381209135
        y: 659.0067501745364
      positionAbsolute:
        x: 598.0057381209135
        y: 659.0067501745364
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1762
      zIndex: 1
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: afba6091-3348-484b-8938-ce3589a0a2a5
          role: system
          text: ''
        - id: d5ed72bf-b971-4fdd-9d70-2d9d892e37f4
          role: user
          text: "# **AI投资分析师指令 (节点 3/4): 启动“阿尔法筛选器” - 二级市场候选名单深度验证数据需求**\n\n**角色设定：**\n\
            你将扮演名为“阿尔法筛选器（Alpha-Screener）”的顶级二级市场投研系统。你的身份是一个进入深度“尽职调查（Due Diligence）”模式的投研委员会。在此关键节点，你的核心任务是：**针对上一节点生成的两份“初步观察名单（草案）”，提出具体的、可从公开市场信息渠道获取的、旨在验证其投资逻辑的“第二轮深度数据需求清单”。**\n\
            \n**核心原则：**\n1.  **绝对二级市场聚焦：** 你的世界里只有公开交易的证券。严禁思考或提及任何一级市场、风险投资、未上市公司。你的最小分析单位是“股票代码（Ticker\
            \ Symbol）”。\n2.  **验证导向：** 每个问题的提出，都必须服务于“验证”或“证伪”该公司入选名单的核心逻辑。问题必须是可回答的，答案必须能从公开信息中找到。\n\
            3.  **使用专业工具与语言：** 你的提问应体现二级市场专家的工作方式，例如要求分析财报电话会（Earnings Call Transcript）、解读券商研报、分析期权链（Option\
            \ Chain）数据等。\n4.  **具体到公司：** 你的提问必须是针对名单上的具体公司，逐一进行。\n\n**本节点任务：**\n仔细研读下方“用户输入区”粘贴的“初步观察名单（草案）”，然后生成一份高度具体的、按公司组织的、完全基于二级市场信息源的“第二轮深度验证数据需求清单”。\n\
            \n**联合投研委员会名单 (虚拟, 超过30位)：**\n* **投资决策委员会 (5位):** 对冲基金CIO, 共同基金PM, 首席投资策略师,\
            \ 全球宏观投资主管, 风险管理委员会主席.\n* **股票研究部 (12位):** 研究部总监, 成长股/价值股/事件驱动策略主管, TMT/医疗/消费/工业/新能源/金融行业首席分析师,\
            \ 小盘股专家, 资深买方分析师.\n* **量化与交易部 (6位):** 量化策略部主管, 统计套利专家, 股票交易部主管, 衍生品交易专家,\
            \ 市场微观结构研究员, 算法交易开发负责人.\n* **外部专家顾问团 (8位):** 顶级卖方研究所所长, 华尔街资深经济学家, 监管政策前沿顾问,\
            \ 法务会计专家, 供应链咨询专家, 技术专利律师, 企业战略顾问, 地缘政治分析师.\n\n**输出格式要求：**\n清单应按公司进行组织，格式如下：\n\
            * **A. 针对“成长稳健”名单的深度验证需求:**\n    * **针对【公司A (股票代码)】的验证:**\n        * **数据需求1:**\
            \ [例如：请分析其最近两次财报电话会的文字记录，总结管理层对未来增长指引的基调（乐观/中性/悲观）及关键理由。]\n        * **验证目的:**\
            \ [验证管理层信心及增长逻辑的内部一致性。]\n* **B. 针对“高弹性”名单的深度验证需求:**\n    * **针对【公司X (股票代码)】的验证:**\n\
            \        * **数据需求1:** [例如：请查询并总结该公司最近6个月的Form 4（内部人交易）文件，分析是否存在显著的内部人净买入或净卖出。]\n\
            \        * **验证目的:** [通过内部人行为，侧面验证公司管理层对前景的看法，以评估“弹性”的可靠性。]\n\n---\n---\n\
            \n**【用户输入区】**\n\n**请在此处完整粘贴节点2生成的“初步观察名单（草案）”及其待验证点：{{#17458560149760.Third#}}**\n\
            [在此处粘贴完整的报告内容]\n\n---\n---\n\n**【AI输出区】**\n** 输出共计不超过20个关键问题，要严格执行**\n\
            **(你将在此处开始生成严格遵循上述所有指令的、针对具体上市公司的“第二轮深度验证数据需求清单”)**"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745856014976017458560149760'
      parentId: '17458560149760'
      position:
        x: 93.41622333960004
        y: 65
      positionAbsolute:
        x: 691.4219614605136
        y: 724.0067501745364
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        is_team_authorization: true
        loop_id: '17458560149760'
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: otherinfo
            ja_JP: otherinfo
            pt_BR: otherinfo
            zh_Hans: otherinfo
          llm_description: ''
          max: null
          min: null
          name: otherinfo
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          otherinfo: ''
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          otherinfo:
            type: mixed
            value: '{{#1738727589378.Profile#}}'
          questions:
            type: mixed
            value: '{{#1745856014976017458560149760.text#}}'
        type: tool
      height: 54
      id: '1745856014976017458560149761'
      parentId: '17458560149760'
      position:
        x: 374.4142785622921
        y: 72.3229049470093
      positionAbsolute:
        x: 972.4200166832056
        y: 731.3296551215457
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        selected: false
        template: '{{"New QA in this round"}}

          {{ NQA }}

          {{"History QA"}}

          {{ HQA }}'
        title: QA-LIST整合
        type: template-transform
        variables:
        - value_selector:
          - '17458560149760'
          - HQA
          variable: HQA
        - value_selector:
          - '1745856014976017458560149760'
          - text
          variable: NQA
      height: 54
      id: '1745856014976017458560149762'
      parentId: '17458560149760'
      position:
        x: 692.5908050098149
        y: 71.7444612918858
      positionAbsolute:
        x: 1290.5965431307284
        y: 730.7512114664222
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '17458560149760'
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 8d7a4a86-46ab-4a51-8334-363baf4d6c75
          role: system
          text: ''
        - id: a6639e79-5d07-44a0-8d51-52fe7df6858e
          role: user
          text: "# **AI投资分析师指令 (节点 4/4): 启动“阿尔法筛选器” - 最终验证与二级市场核心名单合成**\n\n**角色设定：**\n\
            你将扮演名为“阿尔法筛选器（Alpha-Screener）”的顶级二级市场投研系统。你的身份是完成了多轮数据筛选和深度验证的最终投资决策委员会。在此最终节点，你的核心任务是：**整合所有信息，对“初步观察名单”进行最终的确认、修正、替换和重新排序，形成两份经过二级市场数据严格验证的、置信度最高的“最终核心观察名单（Final\
            \ Core Watchlist）”。**\n\n**核心原则：**\n1.  **绝对二级市场聚焦：** 你的世界里只有公开交易的证券。严禁思考或提及任何一级市场、风险投资、未上市公司。你的最小分析单位是“股票代码（Ticker\
            \ Symbol）”。\n2.  **基于证据的最终裁决：** 必须根据第二轮的深度数据，对初步名单上的每个公司做出明确的判断，并在最终的分析要点中体现出新证据的支持。\n\
            3.  **风险收益的再评估：** 在最终排序时，重新评估每个机会的风险收益比，确保排名反映了所有已知信息。\n4.  **可交易的最终产出：**\
            \ 输出的名单必须是简洁、清晰、可直接交付给投资组合经理或交易员，用于下一步决策的最终版本。\n\n**本节点任务：**\n基于下方“用户输入区”提供的“初步观察名单”和“第二轮深度验证数据”，执行最终的投研决策，输出两份经过迭代验证的、可直接用于二级市场投资决策的Top\
            \ 10核心观察名单。\n\n**联合投研委员会名单 (虚拟, 超过30位)：**\n* **投资决策委员会 (5位):** 对冲基金CIO,\
            \ 共同基金PM, 首席投资策略师, 全球宏观投资主管, 风险管理委员会主席.\n* **股票研究部 (12位):** 研究部总监, 成长股/价值股/事件驱动策略主管,\
            \ TMT/医疗/消费/工业/新能源/金融行业首席分析师, 小盘股专家, 资深买方分析师.\n* **量化与交易部 (6位):** 量化策略部主管,\
            \ 统计套利专家, 股票交易部主管, 衍生品交易专家, 市场微观结构研究员, 算法交易开发负责人.\n* **外部专家顾问团 (8位):**\
            \ 顶级卖方研究所所长, 华尔街资深经济学家, 监管政策前沿顾问, 法务会计专家, 供应链咨询专家, 技术专利律师, 企业战略顾问, 地缘政治分析师.\n\
            \n**最终报告结构要求：**\n以简洁、清晰的最终名单形式输出，格式如下：\n\n### **最终核心观察名单：Top 10 成长稳健投资机会\
            \ (二级市场)**\n*(基于多轮公开市场数据验证，具备高确定性增长逻辑的可交易证券)*\n\n1.  **公司名称 (交易所: 股票代码)**\n\
            \    * **核心投资逻辑:** [一句话总结该公司经过验证后的投资逻辑。]\n    * **三大关键驱动点:**\n       \
            \ * **驱动点1 (已验证):** [简要分析第一个关键驱动点，并注明关键验证信息来源，如“据最新10-K报告...”]\n     \
            \   * **驱动点2 (已验证):** [简要分析第二个关键驱动点。]\n        * **驱动点3 (已验证):** [简要分析第三个关键驱动点，如“当前估值低于五年历史均值”。]\n\
            \n...(按此格式，依次输出排名2至10的机会)\n\n---\n\n### **最终核心观察名单：Top 10 高弹性投资机会 (二级市场)**\n\
            *(基于多轮公开市场数据验证，具备高风险收益比和明确催化剂的可交易证券)*\n\n1.  **公司名称 (交易所: 股票代码)**\n  \
            \  * **核心投资逻辑:** [一句话总结该公司经过验证后的高弹性投资逻辑。]\n    * **三大关键驱动点:**\n      \
            \  * **驱动点1 (已验证):** [简要分析第一个关键驱动点，如“市场对其XX风险反应过度，据最新券商研报分析...”]\n   \
            \     .\n        * **驱动点2 (已验证):** [简要分析第二个关键驱动点，如“其高经营杠杆将在营收改善时显著放大盈利”。]\n\
            \        * **驱动点3 (已验证):** [简要分析第三个关键驱动点，如“未来X个月内XX事件是明确的关键催化剂”。]\n\n\
            ...(按此格式，依次输出排名2至10的机会)\n\n---\n---\n\n**【用户输入区】**\n\n**请在此处粘贴节点2的报告以及对节点3问题的回答：**\n\
            \n**1. 初步观察名单（草案） (来自节点2的完整输出):{{#17458560149760.Third#}}**\n[在此处粘贴完整的报告内容]\n\
            \n**2. 第二轮深度验证数据收集结果 (对节点3问题的回答):{{#1745856014976017458560149761.text#}}**\n\
            [在此处逐条或整体粘贴您为节点3数据需求清单收集到的所有信息]\n\n---\n---\n\n**【AI输出区】**\n\n**(你将在此处开始生成严格遵循上述所有指令的、两份最终的、经过二级市场数据迭代验证的“核心观察名单”)**"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: LLM 31
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '1745856014976017458560149763'
      parentId: '17458560149760'
      position:
        x: 1036.4586033506334
        y: 81.15436510541986
      positionAbsolute:
        x: 1634.464341471547
        y: 740.1611152799562
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1745856014976017458560149763'
          - text
          variable_selector:
          - '17458560149760'
          - Third
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1745856014976017458560149762'
          - output
          variable_selector:
          - '17458560149760'
          - HQA
          write_mode: over-write
        loop_id: '17458560149760'
        selected: false
        title: 变量赋值 3
        type: assigner
        version: '2'
      height: 116
      id: '1745856014976017458560149764'
      parentId: '17458560149760'
      position:
        x: 1339.5578958544825
        y: 82.76710514091599
      positionAbsolute:
        x: 1937.563633975396
        y: 741.7738553154524
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1745856014976start
      parentId: '17458560149760'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 622.0057381209135
        y: 727.0067501745364
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: '# **AI投资分析师指令 (节点 2/4): 启动“真理之眼” - 初步事实分析**


            **角色设定：**

            你将扮演一个名为“真理之眼（Veritas-Eye）”的超级分析系统。你的身份是一个由超过20位全球顶级专家组成的联合评审委员会。在此分析节点，你的核心任务是：**基于初始信息和第一轮收集到的信息，进行一次全面的、纯事实的初步分析，并形成一份“初步事实分析报告”。**


            **核心原则：**

            1.  **绝对事实中立：** 你唯一的任务是整合、组织和呈现事实。严禁做出任何形式的投资建议、价值判断或未来预测。

            2.  **信息来源标注：** 对所有关键信息，尽可能注明其来源和可信度。

            3.  **识别疑点与矛盾：** 在分析过程中，你的一个关键职责是主动识别并明确指出当前信息中存在的矛盾点、不一致之处以及明显的空白区域。

            4.  **多维视角融合：** 你必须模拟下方定义的每一个专家角色，对信息进行综合解读和事实串联。


            **本节点任务：**

            根据下方“用户输入区”提供的“初始信息”和“第一轮问题回答”，生成一份结构化的“初步事实分析报告”。这份报告不仅要总结已知事实，还必须包含一个专门的章节，用于列出分析过程中发现的疑点、风险和信息缺口，为下一轮的深度提问做准备。


            **联合事实评审委员会名单 (虚拟)：**

            * **投资决策层 (4位):** 风险投资合伙人, 私募股权投资总监, 并购专家, 天使投资人.

            * **金融与财务分析 (5位):** 股票研究分析师, 法务会计师, 资产评估师, 投资银行家, 信用评级分析师.

            * **行业与技术专家 (6位):** 目标行业资深分析师, 首席技术官(CTO), 供应链管理专家, 产品管理总监, 研发科学家, 数据科学家.

            * **法律、合规与风险管理 (4位):** 公司法律师, 知识产权律师, 监管合规专家, ESG分析师.

            * **市场与战略 (3位):** 顶级战略顾问, 市场研究总监, 公共关系专家.

            * **背景调查专家 (1位):** 专业尽职调查顾问.


            **输出报告结构要求：**

            1.  **执行摘要 (纯事实)**

            2.  **主体背景扫描**

            3.  **产品与技术分析**

            4.  **财务与运营分析**

            5.  **市场与行业分析**

            6.  **初步风险与合规扫描**

            7.  **【关键章节】已识别的疑点、矛盾与信息缺口**


            ---

            ---


            **【用户输入区】**


            **请在此处粘贴初始信息以及第一轮问题的所有回答：**


            **1. 初始信息:{{#1738727589378.Profile#}}**

            [在此处粘贴节点1使用的初始信息]


            **2. 第一轮信息收集结果 (对节点1问题的回答):{{#1749285171192.text#}}**

            [在此处逐条或整体粘贴您为节点1问题清单收集到的所有信息]


            ---

            ---


            **【AI输出区】**


            **(你将在此处开始生成严格遵循上述所有指令的、结构化的“初步事实分析报告”)**'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 1 - 1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17473249427250'
      position:
        x: 266.5340346750728
        y: 23.616954909370236
      positionAbsolute:
        x: 266.5340346750728
        y: 23.616954909370236
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询 - 1
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          questions:
            type: mixed
            value: '{{#17457341467330.text#}}'
        type: tool
      height: 54
      id: '1749285171192'
      position:
        x: -27.352055685469708
        y: 23.616954909370236
      positionAbsolute:
        x: -27.352055685469708
        y: 23.616954909370236
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询 - 2
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          questions:
            type: mixed
            value: '{{#17457361886690.text#}}'
        type: tool
      height: 54
      id: '17493058108570'
      position:
        x: -39.823948291313
        y: 349.075800648781
      positionAbsolute:
        x: -39.823948291313
        y: 349.075800648781
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: '# **AI投资分析师指令 (节点 2/4): 启动“卡桑德拉认知系统” - 初步影响假设生成**


            **角色设定：**

            你将扮演名为“卡桑德拉认知（Cassandra-Cognition）”的战略远见系统。你的身份是一个由超过20位全球顶级专家组成的联合推演委员会。在此分析节点，你的核心任务是：**基于“核心事实”和第一轮收集到的“预测性数据”，构建一个初步的、附带明确假设和置信度评估的“影响预测报告”。**


            **核心原则：**

            1.  **生成假设，而非结论：** 本报告的所有内容均为基于当前数据的“初步假设”，必须明确标注其不确定性。

            2.  **透明化假设：** 对于因数据不足而进行推演的部分，必须明确列出所依赖的“核心假设”。例如：“假设竞争对手将在6个月内做出反应...”。

            3.  **逻辑链条清晰：** 使用“一阶 -> 二阶 -> 三阶”影响链的结构，清晰展示每个预测的推导过程。

            4.  **初步量化与评级：** 在可能的情况下，对影响的“量级”（大/中/小）和“发生概率”（高/中/低）进行初步评级，形成“初步影响矩阵”。


            **本节点任务：**

            根据下方“用户输入区”提供的“核心事实报告”和“第一轮数据回答”，生成一份结构化的“初步影响预测报告”。报告的核心是构建出影响的逻辑链条，并诚实地暴露其中的假设和不确定性，为下一轮的“压力测试”提供靶子。


            **联合推演委员会名单 (虚拟)：**

            * **战略与决策层 (5位):** 首席战略官(CSO), 情景规划专家, 系统动力学建模专家, 资深风险投资家, 博弈论专家.

            * **金融与市场分析 (5位):** 宏观经济学家, 行为经济学家, 股票研究主管, 量化策略师, 数据策略师.

            * **行业与技术专家 (6位):** 目标行业首席分析师, 首席技术官(CTO), 供应链架构师, 产品战略副总裁, 颠覆性技术研究员,
            消费者洞察总监.

            * **法律、政策与社会 (4位):** 监管政策分析师, 反垄断法律专家, 地缘政治风险顾问, 社会学家.

            * **“红队”专家 (1位):** “红队”未来学家.


            **输出报告结构要求：**

            1.  **执行摘要 (总结核心影响假设)**

            2.  **初步影响链推演** (分维度展示一、二、三阶影响假设)

            3.  **初步影响矩阵** (以图表或列表形式展示各影响的 概率 vs. 量级)

            4.  **【关键章节】核心假设与低置信度环节清单** (明确列出本报告成立所依赖的关键假设，以及数据最缺乏、最不确定的推演环节)


            ---

            ---


            **【用户输入区】**


            **请在此处粘贴核心事实报告以及第一轮数据的所有回答：**


            **1. 核心事实报告 (来自事实发现流程的最终输出):{{#17458542648830.First#}}**

            [在此处粘贴完整的报告内容]


            **2. 第一轮预测性数据收集结果 (对节点1问题的回答):{{#17493058108570.text#}}**

            [在此处逐条或整体粘贴您为节点1数据需求清单收集到的所有信息]


            ---

            ---


            **【AI输出区】**


            **(你将在此处开始生成严格遵循上述所有指令的、结构化的“初步影响预测报告”)**'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 2 - 1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17493058407690'
      position:
        x: 266.5340346750728
        y: 349.075800648781
      positionAbsolute:
        x: 266.5340346750728
        y: 349.075800648781
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询 - 3
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          questions:
            type: mixed
            value: '{{#17457361931830.text#}}'
        type: tool
      height: 54
      id: '17493064534440'
      position:
        x: -54.15774247482392
        y: 673.6280606132798
      positionAbsolute:
        x: -54.15774247482392
        y: 673.6280606132798
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# **AI投资分析师指令 (节点 2/4): 启动“阿尔法筛选器” - 初步筛选与二级市场候选名单生成**\n\n**角色设定：**\n\
            你将扮演名为“阿尔法筛选器（Alpha-Screener）”的顶级二级市场投研系统。你的身份是一个由超过30位华尔街顶尖投资专家组成的投研委员会。在此分析节点，你的核心任务是：**将第一轮收集到的结构化数据输入你的筛选模型，进行初步量化筛选和定性判断，生成两份初步的、明确标注为“草案”的“Top\
            \ 10 投资机会观察名单”。**\n\n**核心原则：**\n1.  **绝对二级市场聚焦：** 你的世界里只有公开交易的证券。严禁思考或提及任何一级市场、风险投资、未上市公司。你的最小分析单位是“股票代码（Ticker\
            \ Symbol）”。\n2.  **草案性质与疑点暴露：** 输出的名单是“初步草案”，用于缩小研究范围。在分析每个机会时，必须主动提出“为确认此逻辑，需在下一轮验证的关键二级市场信息点”。\n\
            3.  **估值与情绪考量：** “三点法则”分析中，必须至少包含一条关于估值水平、市场预期或近期股价表现的讨论，体现二级市场特性。\n4.\
            \  **标准化输出：** 每个机会都必须包含明确的公司名称、交易所和股票代码。\n\n**本节点任务：**\n根据下方“用户输入区”提供的“核心影响报告”和“第一轮量化筛选数据”，执行高效筛选，并按“成长稳健”与“高弹性”分类，生成两份Top\
            \ 10名单草案，并汇总下一步需要验证的关键问题。\n\n**联合投研委员会名单 (虚拟, 超过30位)：**\n* **投资决策委员会 (5位):**\
            \ 对冲基金CIO, 共同基金PM, 首席投资策略师, 全球宏观投资主管, 风险管理委员会主席.\n* **股票研究部 (12位):** 研究部总监,\
            \ 成长股/价值股/事件驱动策略主管, TMT/医疗/消费/工业/新能源/金融行业首席分析师, 小盘股专家, 资深买方分析师.\n* **量化与交易部\
            \ (6位):** 量化策略部主管, 统计套利专家, 股票交易部主管, 衍生品交易专家, 市场微观结构研究员, 算法交易开发负责人.\n*\
            \ **外部专家顾问团 (8位):** 顶级卖方研究所所长, 华尔街资深经济学家, 监管政策前沿顾问, 法务会计专家, 供应链咨询专家, 技术专利律师,\
            \ 企业战略顾问, 地缘政治分析师.\n\n**输出报告结构要求：**\n分别输出两份名单，每份名单的格式如下：\n1.  **Top 10\
            \ 名单 (草案)**\n    * **1. 公司名称 (交易所: 股票代码)**\n        * **核心逻辑概述:** ...\n\
            \        * **三大关键驱动点:**\n            * 驱动点1: [业务/基本面相关分析]\n          \
            \  * 驱动点2: [竞争格局/护城河相关分析]\n            * 驱动点3: [估值/市场情绪/股价表现相关分析]\n  \
            \      * **二级市场待验证点:** [例如：需通过解读其最新10-Q财报，核实其XX业务部门的真实利润率。]\n    * ...\
            \ (依次排列2至10)\n2.  **【关键章节】初步名单核心待验证问题汇总** (将所有公司的“二级市场待验证点”汇总于此，为节点3做准备)\n\
            \n---\n---\n\n**【用户输入区】**\n\n**请在此处粘贴核心影响报告以及第一轮数据的所有回答：**\n\n**1. 核心影响报告\
            \ (来自影响推演流程的最终输出):{{#17458554196650.Second#}}**\n[在此处粘贴完整的报告内容]\n\n**2.\
            \ 第一轮量化筛选数据收集结果 (对节点1问题的回答):{{#17493064534440.text#}}**\n[在此处粘贴您为节点1数据需求清单收集到的所有信息]\n\
            \n---\n---\n\n**【AI输出区】**\n\n**(你将在此处开始生成严格遵循上述所有指令的、两份带有明确“草案”标识和“二级市场待验证点”的初步观察名单)**"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 3 - 1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17493064676110'
      position:
        x: 260.3617402905029
        y: 684.2045285984262
      positionAbsolute:
        x: 260.3617402905029
        y: 684.2045285984262
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Questionlist
            ja_JP: Questionlist
            pt_BR: Questionlist
            zh_Hans: Questionlist
          label:
            en_US: questions
            ja_JP: questions
            pt_BR: questions
            zh_Hans: questions
          llm_description: Questionlist
          max: null
          min: null
          name: questions
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          questions: ''
        provider_id: 2502b958-a969-4e7c-b5d2-21598f1562cd
        provider_name: 循环查询
        provider_type: workflow
        selected: false
        title: 循环查询 - 4
        tool_configurations: {}
        tool_description: 输入一个问题清单和辅助查询的信息，输出答案列表
        tool_label: 循环查询
        tool_name: QuestionAnswer_Loop
        tool_parameters:
          questions:
            type: mixed
            value: '{{#17457340486300.text#}}'
        type: tool
      height: 54
      id: '17493068577120'
      position:
        x: 38.73441267787905
        y: 1617.5504087367424
      positionAbsolute:
        x: 38.73441267787905
        y: 1617.5504087367424
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# Meta-Prompt: Part 4, Node 4.3 - 多维筛选之构建性分析（正方）\n\n## 输入\n1.  所有先前的定稿报告（Part\
            \ 1, 2, 3）。\n2.  在Node 4.1中生成的《潜在投资标的观察长名单》。\n3.  在Node 4.2中依据“多维数据画像”指令挖掘到的【长名单中所有公司的第一轮数据】。\n\
            \n## 核心任务\n你的身份是【多维筛选与初步排序委员会（正方）】。这是一个由超过30位顶级投资专家组成的分析机构。你的唯一、绝对的任务是：基于输入的所有公司数据，进行一次全面的、多维度的横向比较，并构建出一个初步的“**Top\
            \ 10 潜力股**”排名论述。你必须像一个基金的投研总监，向投资组合经理呈现一份逻辑清晰、有理有据的初步筛选结果。\n\n**核心戒律：在此节点，你严禁进行任何形式的自我批判。你的目标是基于当前数据，构建一个最合理的初步排序。**\n\
            \n## 角色扮演：多维筛选与初步排序委员会（正方） (31位专家)\n* **联席主席 (3位):** 乔尔·格林布拉特, GARP策略基金经理,\
            \ 顶级咨询公司（BCG）资深合伙人。\n* **核心成员 (28位):** 量化策略师(10), 价值投资者(5), 质量/成长投资者(5),\
            \ 首席风险官(4), 信用分析师(4)。\n\n## 工作指令\n你必须严格按照以下结构，完成并输出一份分析草稿。\n\n1.  **构建“多维比较分析矩阵”:**\n\
            \    * 创建一个Markdown表格，行为长名单中的所有公司，列为Node 4.1中定义的五个核心维度（战略契合度、商业质量、财务健康度、管理层质量、估值水平）。\n\
            \    * 在表格的每个单元格中，填入从Node 4.2获取的关键数据或定性评价的摘要。\n2.  **进行初步评分与排序:**\n  \
            \  * 为每个公司的五个维度进行评分（例如，使用1-5分制）。\n    * 计算每个公司的综合得分，并据此得出一个初步的Top 10排名（包含做多和做空方向）。\n\
            3.  **撰写初步筛选报告:**\n    * **概述筛选方法:** 简要说明你的评分逻辑和权重。\n    * **论证Top 3-5的入选理由:**\
            \ 为排名最靠前的3-5家公司，分别撰写一段“初步看好（或看空）理由”，清晰阐述它们在多维矩阵中的突出优势（或劣势）。\n4.  **形成初步结论:**\n\
            \    * 最终输出一份逻辑清晰、有数据支持的《多维筛选与初步排序报告（草稿）》。\n\n## 输出要求\n* 以Markdown格式，输出一份包含“多维比较分析矩阵”和初步Top\
            \ 10排序及理由的《多维筛选与初步排序报告（草稿）》。这份草稿将成为下一个节点（Node 4.4）进行批判的唯一靶子。\n\nPart 1:{{#17458542648830.First#}}\n\
            Part 2:{{#17458554196650.Second#}}\nPart 3:{{#17458560149760.Third#}}\n\
            《潜在投资标的观察长名单》：{{#17457340486300.text#}}\n长名单中所有公司的第一轮数据：{{#17493068577120.text#}}\n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 4 - 1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17493068668040'
      position:
        x: 411.6777895262613
        y: 1571.1043311603908
      positionAbsolute:
        x: 411.6777895262613
        y: 1571.1043311603908
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17458542648830'
          - First
          variable: First
        - value_selector:
          - '17458554196650'
          - Second
          variable: Second
        - value_selector:
          - '17458560149760'
          - Third
          variable: Third
        selected: false
        title: 结束 2
        type: end
      height: 142
      id: '1749438351154'
      position:
        x: 2567.6004980726366
        y: 673.6280606132798
      positionAbsolute:
        x: 2567.6004980726366
        y: 673.6280606132798
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# Meta-Prompt指令：【节点4c】可执行投资与监控计划制定\n\n## 1. 角色与目标\n\n你将扮演投资公司的**投资执行与风险管理委员会**的核心团队，由首席投资组合经理、交易主管和首席风险官领导。你刚刚收到了“谋杀板”对【节点4a】投资论点的最终评审结论——“批准投资”或“有条件批准”。\n\
            \n你的任务不再是分析“为什么”或“是什么”，而是制定一个**关于“如何做”的、极其详尽、具体、可操作的行动计划**。你要将一个投资思想，转化为一系列在真实世界中可以被精确执行的指令和纪律。\n\
            \n## 2. 核心原则\n\n* **执行优于分析**：在这一阶段，完美的执行纪律比更多的分析更重要。\n* **事前计划，事中跟踪，事后复盘**：你的计划必须覆盖投资的全生命周期。\n\
            * **非对称性**：寻找风险/回报不对称的执行方式。如何用最小的风险，去博取最大的潜在收益？\n* **风险预算**：任何投资都必须有明确的风险预算（愿意承受的最大亏损额），并有相应的止损机制。\n\
            * **如果……那么……（If-Then）**：计划的核心是一系列“如果-那么”规则，用于应对未来各种可能发生的情况，避免情绪化决策。\n\n\
            ## 3. 投资执行与风险管理委员会\n\n这个委员会由一群每天在市场一线战斗的专业人士组成，他们关心的是点差、流动性、风险敞口和执行成本。\n\
            \n* **主席团 (5位)**:\n    1.  **首席投资组合经理（Head of Portfolio Management）原型**:\
            \ 负责从整个投资组合的角度，决定该笔投资的头寸规模和风险配置。\n    2.  **交易主管（Head of Trading）原型**:\
            \ 负责制定具体的、能最小化市场冲击和交易成本的建仓/平仓策略。\n    3.  **首席风险官（Chief Risk Officer,\
            \ CRO）原型**: 负责设定和监控所有风险限额，并审批对冲策略。\n    4.  **首席合规官（Chief Compliance Officer,\
            \ CCO）原型**: 确保整个执行过程符合所有法律法规。\n    5.  **量化投资组合分析师原型**: 负责计算该笔投资对整个组合的风险因子敞口（如Beta、动量、价值等）的影响。\n\
            * **执行与交易组 (8位)**:\n    1.  资深股票交易员（负责股票执行）。\n    2.  衍生品交易员（负责期权、期货等对冲工具的执行）。\n\
            \    3.  固定收益交易员（如果涉及债券）。\n    4.  外汇交易员（如果涉及跨币种）。\n    5.  算法交易（Algo\
            \ Trading）策略师。\n    6.  交易成本分析（TCA）专家。\n    7.  大宗经纪（Prime Brokerage）关系经理。\n\
            \    8.  证券借贷（Securities Lending）专家（如果涉及做空）。\n* **风险与监控组 (8位)**:\n   \
            \ 1.  市场风险经理。\n    2.  信用风险经理。\n    3.  运营风险经理。\n    4.  投资组合风险分析师。\n\
            \    5.  模型风险验证专家。\n    6.  压力测试与情景分析专家。\n    7.  数据科学家（负责监控另类数据信号）。\n\
            \    8.  绩效归因分析师。\n* **法律与合规组 (5位)**:\n    1.  证券法律师。\n    2.  交易合规顾问。\n\
            \    3.  信息披露专家。\n    4.  反内幕交易专家。\n    5.  托管与清算专家。\n* **投后管理组 (5位)**:\n\
            \    1.  负责该项投资的分析师（即CIO团队成员）。\n    2.  私募股权投后管理专家原型。\n    3.  投资者关系（IR）专家（负责与标的公司沟通）。\n\
            \    4.  公司治理专家。\n    5.  外部行业顾问。\n\n## 4. 任务指令\n\n1.  **接收输入**：我将向你提供【节点4a】的《核心投资论点备忘录》和【节点4b】的《终极评审质询记录与结论》。\n\
            2.  **制定并输出行动计划**：收到输入后，你必须制定一份详尽的《投资执行与监控计划》，内容必须包括：\n    * **第一部分：头寸与风险预算（Position\
            \ Sizing & Risk Budgeting）**\n        * **头寸规模**：基于投资论点的确定性和投资组合的整体风险，明确建议的初始头寸规模（占总资产的百分比）。\n\
            \        * **风险预算**：明确该笔投资愿意承受的最大亏损额度（以金额或百分比表示）。一旦触及，必须启动重新评估或止损程序。\n\
            \    * **第二部分：建仓/加仓策略（Entry Strategy）**\n        * **建仓方式**：是一次性建仓，还是分批次、在不同价格水平上建仓？\n\
            \        * **执行策略**：使用何种交易算法（如VWAP, TWAP）来最小化市场冲击？\n        * **价格区间**：明确理想的建仓价格区间。\n\
            \        * **加仓条件**：如果未来发生什么情况（如股价回调至某支撑位、某催化剂事件发生），我们将如何进行加仓？\n    *\
            \ **第三部分：平仓/减仓策略（Exit Strategy）**\n        * **目标兑现**：当股价达到我们的目标估值区间时，我们的退出策略是什么？是一次性卖出还是分批卖出？\n\
            \        * **论点失效止损**：明确定义哪些“关键支柱”的失效，将直接触发我们无条件退出。这是“基于基本面的止损”。\n   \
            \     * **价格止损**：设定一个技术性的价格止损位或移动止损策略。这是“基于价格的止损”。\n        * **减仓条件**：如果出现部分风险，或股价涨幅过快，我们将如何进行部分减仓以锁定利润？\n\
            \    * **第四部分：风险对冲计划（Hedging Plan）**\n        * 具体说明将采用何种金融工具（如买入看跌期权、卖出股指期货等）来对冲哪些特定的、已识别的风险。明确对冲的规模和时机。\n\
            \    * **第五部分：投后监控计划（Monitoring Plan）**\n        * **关键指标仪表盘（Dashboard）**：列出一个需要每周/每月跟踪的关键指标清单，包括：\n\
            \            * **财务与运营指标**：来自公司财报和公告。\n            * **产业链验证指标**：来自上下游的数据。\n\
            \            * **另类数据信号**：来自第三方数据提供商。\n            * **市场情绪与资金流指标**。\n\
            \        * **“如果-那么”剧本**：制定一个简明的剧本，规定当监控的关键指标发生超预期变化时，我们应该采取什么行动。\n\n\
            **输出格式要求**：\n* 使用Markdown格式，结构清晰，语言精确，如同真正的交易指令。\n* 计划必须是可量化的、可执行的。\n\
            * **绝对禁止**任何模糊不清的表述或分析性内容。\n* **不要**用编程的输出格式。\n\n请现在待命，等待我输入【节点4a】和【节点4b】的最终文件。\n\
            \n节点4a：{{#17457340486300.text#}}\n节点4b：{{#17494467467670.text#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 4 (2)
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17494467390530'
      position:
        x: 354.93183337223843
        y: 1044.111407900149
      positionAbsolute:
        x: 354.93183337223843
        y: 1044.111407900149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-7-sonnet-20250219
          provider: langgenius/anthropic/anthropic
        prompt_template:
        - id: 6923e840-3673-4091-8a61-b69620311d25
          role: system
          text: ''
        - id: 40bdc8c2-f82d-4454-a367-fa89a2613c73
          role: user
          text: "# Meta-Prompt指令：【节点4b】终极投资论点“谋杀板”评审\n\n## 1. 角色与目标\n\n你将扮演投资公司最高风险决策机构——**“投资论点谋杀板”（Investment\
            \ Thesis Murder Board）**。你不再是任何一个领域的专家，而是整个投资组合的**最终守护者**。你的成员是公司最资深、最成功、也最苛刻的传奇人物。\n\
            \n你的唯一任务是，接收上一节点（4a）产出的《核心投资论点备忘录》，并以**杀死这个投资想法为唯一目的**，对其进行终极的、无情的、全方位的拷问。你不是在寻找优点，你只在寻找那个可以一击致命的弱点。如果经过你的“谋杀”后这个想法还能幸存，那它才真正值得投资。\n\
            \n## 2. 核心原则\n\n* **有罪推定（Presumption of Guilt）**：假设这个投资论点是错误的，报告撰写者（CIO）的任务是证明它是正确的。你的任务是找出他证明不了的地方。\n\
            * **寻找“致命缺陷”（The Fatal Flaw）**：你不在乎那些细枝末节的小问题。你要寻找的是那个一旦发生，就会导致灾难性亏损的“致命缺陷”。\n\
            * **逆向工作法（Working Backwards）**：想象一下，一年后这笔投资亏损了50%。你需要写一份事后报告，解释当初为什么会做出这个愚蠢的决定。现在，请把这份想象中的报告里的理由，作为你今天的提问。\n\
            * **能力圈拷问（Circle of Competence）**：这个投资论点，是否真的在我们公司的核心能力圈之内？我们真的比市场上所有人都更懂这个东西吗？还是我们只是在“听故事”？\n\
            * **机会成本**：即使这个想法不错，但它是否足够好，值得占用我们宝贵的资本和关注力？有没有更好、更确定的机会？\n\n## 3. 投资论点谋杀板评审委员会\n\
            \n这是公司里最令人望而生畏的房间，由超过30位传奇与终极怀疑论者构成。\n\n* **主席团 (5位)**:\n    1.  **沃伦·巴菲特原型（伯克希尔·哈撒韦CEO）**:\
            \ 主席。负责关于“能力圈”、“护城河”和“管理层品质”的终极拷问。\n    2.  **查理·芒格原型（伯克希尔·哈撒韦副主席）**:\
            \ 联席主席。负责从人性的弱点、心理偏误和跨学科的普世智慧角度，寻找论点中的愚蠢之处。\n    3.  **乔治·索罗斯原型（索罗斯基金创始人）**:\
            \ 负责拷问论点在地缘政治、宏观反身性和市场非理性方面的盲点。\n    4.  **公司创始人/传奇投资人原型**: 代表公司的核心价值观和长期风险偏好。\n\
            \    5.  **顶级对冲基金首席风险官（CRO）原型**: 负责对所有风险进行最终的、量化的压力测试。\n* **各节点红队主席组 (5位)**:\n\
            \    1.  宏观分析红队主席（来自节点1d）。\n    2.  产业链分析红队主席（来自节点2d）。\n    3.  量化模型红队主席（来自节点3d）。\n\
            \    4.  著名做空机构创始人原型（代表所有怀疑派）。\n    5.  顶级调查记者原型（代表所有对事实真实性的怀疑）。\n* **终极决策者组\
            \ (10位)**:\n    1.  首席执行官（CEO）。\n    2.  首席投资官（CIO，即报告的提交者，在此接受拷问）。\n\
            \    3.  首席财务官（CFO）。\n    4.  首席法务官（CLO）。\n    5.  投资委员会所有其他资深合伙人（5位）。\n\
            * **外部挑战者组 (11位)**:\n    1.  **纳西姆·塔勒布原型**: 再次出场，专门负责拷问“黑天鹅”风险和模型脆弱性。\n\
            \    2.  **丹尼尔·卡尼曼原型**: 再次出场，专门负责诊断整个决策流程中的认知偏误。\n    3.  **伊隆·马斯克原型**:\
            \ 再次出场，用第一性原理拷问：“你们说的这些行业规则和壁垒，真的不能被打破吗？”\n    4.  该行业最成功的竞争对手公司的前任CEO原型。\n\
            \    5.  最了解该公司的、最悲观的卖方分析师原型。\n    6.  一位资深的、经历过多次市场崩溃的交易主管原型。\n    7.\
            \  一位研究该行业的、持完全相反观点的学者原型。\n    8.  一位代表小股东利益的激进投资者原型。\n    9.  一位代表监管机构立场的退休高级官员原型。\n\
            \    10. 一位专门处理该行业破产案件的律师原型。\n    11. 一位完全不懂这个行业但充满智慧的“聪明局外人”原型。\n\n##\
            \ 4. 任务指令\n\n1.  **接收输入**：我将向你提供【节点4a】的《核心投资论点备忘录》。\n2.  **进行谋杀板评审**：收到备忘录后，你必须以“谋杀板”的身份，输出一份极其尖锐、直击要害的**《终极评审质询记录》**。记录必须包含以下内容：\n\
            \    * **一、致命缺陷质询**：针对备忘录中列出的“关键支柱”，逐一提出可能使其崩溃的致命性质疑。\n    * **二、认知偏误诊断**：诊断整个投资论点中可能存在的集体性认知偏误（如：确认偏误、故事偏见、过度自信等）。\n\
            \    * **三、“反悔药丸”测试**：提出一系列“如果……会怎样？”的极端情景，测试投资论点的脆弱性。（例如：“如果明天中国宣布禁止出口XX关键材料，我们的投资会怎样？”）\n\
            \    * **四、能力圈与机会成本拷问**：提出关于“我们为什么能赢？”和“这笔投资是否是当下最好的选择？”的质询。\n    * **五、最终投票与结论**：模拟委员会的最终投票。每个主席团成员都要明确表态（“同意，但有保留”、“强烈反对”、“有条件通过”等），并给出简短但深刻的理由。最后，给出一个总的评审结论：\n\
            \        * **A. 批准投资（Go）**: 论点足够稳健，可以进入执行阶段。\n        * **B. 有条件批准（Conditional\
            \ Go）**: 批准，但必须在满足特定条件或进行某些对冲后才能执行。\n        * **C. 驳回（No Go）**: 论点存在致命缺陷，投资想法被“杀死”。\n\
            \n**输出格式要求**：\n* 使用Markdown格式，以对话或记录体形式呈现。\n* 质询必须尖锐、深刻，直指人心。\n* **绝对禁止**任何省略。\n\
            * **不要**用编程的输出格式。\n\n请现在待命，等待我输入【节点4a】的投资论点备忘录:{{#17457340486300.text#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 5000
        selected: false
        title: 节点 4 (1)
        type: llm
        variables: []
        vision:
          enabled: false
      height: 119
      id: '17494467467670'
      position:
        x: -4.2114849930079
        y: 1044.111407900149
      positionAbsolute:
        x: -4.2114849930079
        y: 1044.111407900149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17458542648830'
          - First
          variable: First
        - value_selector:
          - '17458554196650'
          - Second
          variable: Second
        - value_selector:
          - '17458560149760'
          - Third
          variable: Third
        - value_selector:
          - '17457340486300'
          - text
          variable: text
        - value_selector:
          - '17494467467670'
          - text
          variable: text1
        - value_selector:
          - '17494467390530'
          - text
          variable: text2
        selected: false
        title: 结束 3
        type: end
      height: 220
      id: '1749446865010'
      position:
        x: 658.9318333722384
        y: 1044.111407900149
      positionAbsolute:
        x: 658.9318333722384
        y: 1044.111407900149
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17458542648830'
          - First
          variable: First
        - value_selector:
          - '17458554196650'
          - Second
          variable: Second
        selected: false
        title: 结束 4
        type: end
      height: 116
      id: '1749653100477'
      position:
        x: 2728.3693458352
        y: 5.823585087994132
      positionAbsolute:
        x: 2728.3693458352
        y: 5.823585087994132
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 252.929282542201
      y: 171.80467936766757
      zoom: 0.35862022532615245

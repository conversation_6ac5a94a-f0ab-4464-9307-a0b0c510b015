"""
事件驱动投资分析图构建器
"""

from .nodes.alpha_eye_initial_analysis_node import AlphaEyeInitialAnalysisNode
from .nodes.alpha_generate_fact_questions_node import AlphaGenerateFactQuestionsNode
from .nodes.alpha_macro_analysis_loop_node import AlphaMacroAnalysisLoopNode
from .nodes.cassandra_generate_fact_questions_node import CassandraGenerateFactQuestionsNode
from .nodes.cassandra_macro_analysis_loop_node import CassandraMacroAnalysisLoopNode
from .nodes.cassandra_eye_initial_analysis_node import CassandraEyeInitialAnalysisNode
from .nodes.truth_macro_analysis_loop_node import TruthMacroAnalysisLoopNode
from .nodes.truth_eye_initial_analysis_node import TruthEyeInitialAnalysisNode
from .nodes.truth_generate_fact_questions_node import TruthGenerateFactQuestionsNode
from .nodes.truth_generate_fact_questions_loop_node import TruthGenerateFactQuestionsLoopNode
from .nodes.truth_eye_initial_analysis_loop_node import TruthEyeInitialAnalysisLoopNode
from .nodes.cassandra_generate_fact_questions_loop_node import CassandraGenerateFactQuestionsLoopNode
from .nodes.cassandra_eye_initial_analysis_loop_node import CassandraEyeInitialAnalysisLoopNode
from .nodes.alpha_generate_fact_questions_loop_node import AlphaGenerateFactQuestionsLoopNode
from .nodes.alpha_eye_initial_analysis_loop_node import AlphaEyeInitialAnalysisLoopNode
from dotenv import load_dotenv
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langgraph.checkpoint.memory import MemorySaver
from .state import InvestmentAnalysisState
from .configuration import VestConfiguration
from .constants import NodeNames
from .nodes.nodes_function import should_continue_truth_loop, should_continue_industry_loop,should_continue_alpha_loop,human_feedback,decide_next_after_human_feedback

load_dotenv()


# 图配置常量
class GraphConfig:
    """图配置常量"""
    GRAPH_NAME = "event-driven-investment-analysis"


# 创建事件驱动投资分析图
builder = StateGraph(InvestmentAnalysisState, config_schema=VestConfiguration)

# 定义节点
builder.add_node(NodeNames.TRUTH_GENERATE_FACT_QUESTIONS, TruthGenerateFactQuestionsNode())
builder.add_node(NodeNames.TRUTH_EYE_INITIAL_ANALYSIS, TruthEyeInitialAnalysisNode())
builder.add_node(NodeNames.TRUTH_GENERATE_FACT_QUESTIONS_LOOP, TruthGenerateFactQuestionsLoopNode())
builder.add_node(NodeNames.TRUTH_EYE_INITIAL_ANALYSIS_LOOP, TruthEyeInitialAnalysisLoopNode())

builder.add_node(NodeNames.HUMAN_FEEDBACK, human_feedback)


builder.add_node(NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS, CassandraGenerateFactQuestionsNode())
builder.add_node(NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS, CassandraEyeInitialAnalysisNode())
builder.add_node(NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS_LOOP, CassandraGenerateFactQuestionsLoopNode())
builder.add_node(NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS_LOOP, CassandraEyeInitialAnalysisLoopNode())


builder.add_node(NodeNames.ALPHA_GENERATE_FACT_QUESTIONS, AlphaGenerateFactQuestionsNode())
builder.add_node(NodeNames.ALPHA_EYE_INITIAL_ANALYSIS, AlphaEyeInitialAnalysisNode())
builder.add_node(NodeNames.ALPHA_GENERATE_FACT_QUESTIONS_LOOP, AlphaGenerateFactQuestionsLoopNode())
builder.add_node(NodeNames.ALPHA_EYE_INITIAL_ANALYSIS_LOOP, AlphaEyeInitialAnalysisLoopNode())


# 设置入口点
builder.add_edge(START, NodeNames.TRUTH_GENERATE_FACT_QUESTIONS)
builder.add_edge(NodeNames.TRUTH_GENERATE_FACT_QUESTIONS, NodeNames.TRUTH_EYE_INITIAL_ANALYSIS)
builder.add_edge(NodeNames.TRUTH_EYE_INITIAL_ANALYSIS, NodeNames.TRUTH_GENERATE_FACT_QUESTIONS_LOOP)
builder.add_edge(NodeNames.TRUTH_GENERATE_FACT_QUESTIONS_LOOP, NodeNames.TRUTH_EYE_INITIAL_ANALYSIS_LOOP)

builder.add_conditional_edges(
    NodeNames.TRUTH_EYE_INITIAL_ANALYSIS_LOOP,
    should_continue_truth_loop,
    {
        "continue": NodeNames.TRUTH_GENERATE_FACT_QUESTIONS_LOOP,  # 回到循环开始
        "exit": NodeNames.HUMAN_FEEDBACK
    }
)

# 在构建图时使用条件边
builder.add_conditional_edges(
    NodeNames.HUMAN_FEEDBACK,
    decide_next_after_human_feedback,
    {
        "CASSANDRA_GENERATE_FACT_QUESTIONS": NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS,
        "ALPHA_GENERATE_FACT_QUESTIONS": NodeNames.ALPHA_GENERATE_FACT_QUESTIONS
    }
)

builder.add_edge(NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS, NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS)
builder.add_edge(NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS, NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS_LOOP)
builder.add_edge(NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS_LOOP, NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS_LOOP)
builder.add_conditional_edges(
    NodeNames.CASSANDRA_EYE_INITIAL_ANALYSIS_LOOP,
    should_continue_industry_loop,
    {
        "continue": NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS_LOOP,  # 回到循环开始
        "exit": NodeNames.HUMAN_FEEDBACK
    }
)

builder.add_edge(NodeNames.ALPHA_GENERATE_FACT_QUESTIONS, NodeNames.ALPHA_EYE_INITIAL_ANALYSIS)
builder.add_edge(NodeNames.ALPHA_EYE_INITIAL_ANALYSIS, NodeNames.ALPHA_GENERATE_FACT_QUESTIONS_LOOP)
builder.add_edge(NodeNames.ALPHA_GENERATE_FACT_QUESTIONS_LOOP, NodeNames.ALPHA_EYE_INITIAL_ANALYSIS_LOOP)
builder.add_conditional_edges(
    NodeNames.ALPHA_EYE_INITIAL_ANALYSIS_LOOP,
    should_continue_alpha_loop,
    {
        "continue": NodeNames.ALPHA_GENERATE_FACT_QUESTIONS_LOOP,  # 回到循环开始
        "exit": END
    }
)
memory = MemorySaver()
graph = builder.compile(name=GraphConfig.GRAPH_NAME, checkpointer=memory)
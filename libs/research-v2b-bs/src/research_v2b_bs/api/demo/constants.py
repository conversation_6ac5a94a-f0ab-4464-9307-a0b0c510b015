"""
Constants for the investment analysis demo
"""

class NodeNames:
    """图节点名称常量"""
    HUMAN_FEEDBACK = "human_feedback"

    TRUTH_GENERATE_FACT_QUESTIONS = "truth_generate_fact_questions"
    TRUTH_EYE_INITIAL_ANALYSIS = "truth_eye_initial_analysis"

    TRUTH_GENERATE_FACT_QUESTIONS_LOOP = "truth_generate_fact_questions_loop"
    TRUTH_EYE_INITIAL_ANALYSIS_LOOP = "truth_eye_initial_analysis_loop"

    TRUTH_MACRO_ANALYSIS_LOOP = "truth_macro_analysis_loop"
    
    CASSANDRA_GENERATE_FACT_QUESTIONS = "cassandra_generate_fact_questions"
    CASSANDRA_EYE_INITIAL_ANALYSIS = "cassandra_eye_initial_analysis"
    CASSANDRA_GENERATE_FACT_QUESTIONS_LOOP = "cassandra_generate_fact_questions_loop"
    CASSANDRA_EYE_INITIAL_ANALYSIS_LOOP = "cassandra_eye_initial_analysis_loop"


    CASSANDRA_MACRO_ANALYSIS_LOOP = "cassandra_macro_analysis_loop"

    ALPHA_GENERATE_FACT_QUESTIONS = "alpha_generate_fact_questions"
    ALPHA_EYE_INITIAL_ANALYSIS = "alpha_eye_initial_analysis"

    ALPHA_GENERATE_FACT_QUESTIONS_LOOP = "alpha_generate_fact_questions_loop"
    ALPHA_EYE_INITIAL_ANALYSIS_LOOP = "alpha_eye_initial_analysis_loop"

    ALPHA_MACRO_ANALYSIS_LOOP = "alpha_macro_analysis_loop"

    FINAL_SYNTHESIS_LOOP = "final_synthesis_loop"
    FINALIZE_INVESTMENT_THESIS = "finalize_investment_thesis"
# """
# 事件驱动投资分析图的节点函数
# """

# import os
# from datetime import datetime  # 添加这行
# from typing import List, Dict, Any
# from langchain_core.messages import AIMessage, HumanMessage
# from langgraph.types import Send
# from langchain_core.runnables import RunnableConfig
# from langchain_core.output_parsers import PydanticOutputParser
# from python_shared.llm import LLMClientUtils

# from .tools_and_schemas import QuestionList, AnalysisResult, InvestmentThesis, LoopContinuation
# from .state import (
#     InvestmentAnalysisState,
#     QuestionGenerationState,
#     LoopAnalysisState,
# )
# from .configuration import VestConfiguration
# from .search import VestSearch
# from .prompts import (
#     FACT_CHECK_PROMPT,
#     PREDICTION_PROMPT,
#     QUANTITATIVE_PROMPT,
#     MACRO_ANALYSIS_PROMPT,
#     INDUSTRY_ANALYSIS_PROMPT,
#     QUANTITATIVE_ANALYSIS_PROMPT,
#     FINAL_SYNTHESIS_PROMPT,
#     LOOP_EVALUATION_PROMPT,
# )


# # 在文件顶部添加导入
# from ..core.events import EventEmitter
# from .promotes import truth_eye_4  as final_synthesis_prompt
# from .promotes import truth_eye_2 as initial_analysis_prompt
# from .promotes import truth_eye_3 as deep_drilling_prompt
# # 节点名称常量
# class NodeNames:
#     """图节点名称常量"""
#     GENERATE_FACT_QUESTIONS = "generate_fact_questions"
#     TRUTH_EYE_INITIAL_ANALYSIS = "truth_eye_initial_analysis"
#     GENERATE_PREDICTION_QUESTIONS = "generate_prediction_questions"
#     CASSANDRA_INITIAL_ANALYSIS = "cassandra_initial_analysis"  # 新增
#     GENERATE_QUANTITATIVE_QUESTIONS = "generate_quantitative_questions"
#     MACRO_ANALYSIS_LOOP = "macro_analysis_loop"
#     INDUSTRY_ANALYSIS_LOOP = "industry_analysis_loop"  # 改造为卡桑德拉循环
#     QUANTITATIVE_ANALYSIS_LOOP = "quantitative_analysis_loop"
#     FINAL_SYNTHESIS_LOOP = "final_synthesis_loop"
#     FINALIZE_INVESTMENT_THESIS = "finalize_investment_thesis"


# # 问题生成节点
# async def generate_fact_questions(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """生成事实核查问题"""
#     configurable = VestConfiguration.from_runnable_config(config)
    

#     # 创建PydanticOutputParser
#     parser = PydanticOutputParser(pydantic_object=QuestionList)
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.question_generation_model,
#         temperature=0.7,
#         streaming=False
#     )
    
#     # 在prompt中添加格式说明
#     formatted_prompt = FACT_CHECK_PROMPT.format(
#         profile=state["profile"]
#     ) + "\n\n" + parser.get_format_instructions()
    
#     try:
#         # 调用LLM获取响应
#         response = await llm.ainvoke(formatted_prompt)
        
#         # 使用parser解析响应
#         result = parser.parse(response.content)
        
#         # 发送问题卡片数据事件
#         yield {
#             "type": "CustomEvent",
#             "data": {
#                 "type": "QUESTIONS_GENERATED",
#                 "step_name": "生成事实核查问题",
#                 "questions": [
#                     {
#                         "id": f"fact_q_{i+1}",
#                         "title": f"事实核查问题 {i+1}",
#                         "content": question,
#                         "type": "fact_check",
#                         "status": "generated",
#                         "category": "事实核查",
#                         "icon": "🔍"
#                     }
#                     for i, question in enumerate(result.questions)
#                 ],
#                 "total_count": len(result.questions),
#                 "category": "事实核查问题"
#             }
#         }
        
#         # 发送步骤完成事件
#         yield EventEmitter.create_step_finished_event(
#             step_name="生成事实核查问题",
#             description=f"成功生成 {len(result.questions)} 个事实核查问题",
#             result_summary=f"生成了 {len(result.questions)} 个问题"
#         )
        
#         # 返回结果
#         yield {
#             "fact_check_questions": result.questions,
#         }
        
#     except Exception as e:
#         # 发送步骤失败事件
#         yield EventEmitter.create_step_finished_event(
#             step_name="生成事实核查问题",
#             description="事实核查问题生成失败",
#             error=str(e)
#         )
#         if configurable.enable_debug:
#             print(f"事实核查问题生成异常: {str(e)}")
#         raise

# # 问题生成节点
# async def initial_analysis(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """生成事实核查问题"""
#     configurable = VestConfiguration.from_runnable_config(config)
    

#     # 创建PydanticOutputParser
#     parser = PydanticOutputParser(pydantic_object=QuestionList)
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.question_generation_model,
#         temperature=0.7,
#         streaming=False
#     )
    
#     # 在prompt中添加格式说明
#     formatted_prompt = FACT_CHECK_PROMPT.format(
#         profile=state["profile"]
#     ) + "\n\n" + parser.get_format_instructions()
    
#     try:
#         # 调用LLM获取响应
#         response = await llm.ainvoke(formatted_prompt)
        
#         # 使用parser解析响应
#         result = parser.parse(response.content)
        
#         # 发送问题卡片数据事件
#         yield {
#             "type": "CustomEvent",
#             "data": {
#                 "type": "QUESTIONS_GENERATED",
#                 "step_name": "生成事实核查问题",
#                 "questions": [
#                     {
#                         "id": f"fact_q_{i+1}",
#                         "title": f"事实核查问题 {i+1}",
#                         "content": question,
#                         "type": "fact_check",
#                         "status": "generated",
#                         "category": "事实核查",
#                         "icon": "🔍"
#                     }
#                     for i, question in enumerate(result.questions)
#                 ],
#                 "total_count": len(result.questions),
#                 "category": "事实核查问题"
#             }
#         }
        
#         # 发送步骤完成事件
#         yield EventEmitter.create_step_finished_event(
#             step_name="生成事实核查问题",
#             description=f"成功生成 {len(result.questions)} 个事实核查问题",
#             result_summary=f"生成了 {len(result.questions)} 个问题"
#         )
        
#         # 返回结果
#         yield {
#             "fact_check_questions": result.questions,
#         }
        
#     except Exception as e:
#         # 发送步骤失败事件
#         yield EventEmitter.create_step_finished_event(
#             step_name="生成事实核查问题",
#             description="事实核查问题生成失败",
#             error=str(e)
#         )
#         if configurable.enable_debug:
#             print(f"事实核查问题生成异常: {str(e)}")
#         raise



# async def generate_prediction_questions(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """生成预测问题 - 使用卡桑德拉广度问题构建"""
#     configurable = VestConfiguration.from_runnable_config(config)
    
#     # 导入卡桑德拉广度问题构建提示词
#     from .promotes.cassandra1 import PROMPT as CASSANDRA_QUESTIONS_PROMPT
    
#     parser = PydanticOutputParser(pydantic_object=QuestionList)
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.question_generation_model,
#         temperature=0.7,
#         streaming=False
#     )
    
#     formatted_prompt = CASSANDRA_QUESTIONS_PROMPT.format(
#         profile=state["profile"]
#     ) + "\n\n" + parser.get_format_instructions()
    
#     try:
#         response = await llm.ainvoke(formatted_prompt)
#         result = parser.parse(response.content)
        
#         # 发送问题卡片数据事件
#         yield {
#             "type": "CustomEvent",
#             "data": {
#                 "type": "QUESTIONS_GENERATED",
#                 "step_name": "生成卡桑德拉预测问题",
#                 "questions": [
#                     {
#                         "id": f"prediction_q_{i+1}",
#                         "title": f"预测问题 {i+1}",
#                         "content": question,
#                         "type": "prediction",
#                         "status": "generated",
#                         "category": "预测分析",
#                         "icon": "🔮"
#                     }
#                     for i, question in enumerate(result.questions)
#                 ],
#                 "total_count": len(result.questions),
#                 "category": "卡桑德拉预测问题"
#             }
#         }
        
#         yield {
#             "prediction_questions": result.questions
#         }
        
#     except Exception as e:
#         if configurable.enable_debug:
#             print(f"生成预测问题异常: {str(e)}")
#         yield {
#             "prediction_questions": ["生成预测问题过程中出现异常"]
#         }
#     """生成预测数据需求问题"""
#     configurable = VestConfiguration.from_runnable_config(config)
    
#     # 发送步骤开始事件
#     yield EventEmitter.create_step_started_event(
#         step_name="生成预测性问题",
#         description="正在生成投资预测相关的问题..."
#     )
    
#     # 创建PydanticOutputParser
#     parser = PydanticOutputParser(pydantic_object=QuestionList)
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.question_generation_model,
#         temperature=0.7,
#         streaming=False
#     )
    
#     # 使用宏观分析结果作为输入
#     macro_summary = "\n".join(state.get("macro_analysis_results", []))
    
#     # 在prompt中添加格式说明
#     formatted_prompt = PREDICTION_PROMPT.format(
#         macro_analysis=macro_summary or "暂无宏观分析结果"
#     ) + "\n\n" + parser.get_format_instructions()
    
#     try:
#         # 调用LLM获取响应
#         response = await llm.ainvoke(formatted_prompt)
        
#         # 使用parser解析响应
#         result = parser.parse(response.content)
        
#         # 发送问题卡片数据事件
#         yield {
#             "type": "CustomEvent",
#             "data": {
#                 "type": "QUESTIONS_GENERATED",
#                 "step_name": "生成预测性问题",
#                 "questions": [
#                     {
#                         "id": f"pred_q_{i+1}",
#                         "title": f"预测性问题 {i+1}",
#                         "content": question,
#                         "type": "prediction",
#                         "status": "generated",
#                         "category": "预测分析",
#                         "icon": "📈"
#                     }
#                     for i, question in enumerate(result.questions)
#                 ],
#                 "total_count": len(result.questions),
#                 "category": "预测性问题"
#             }
#         }
        
#         # 发送步骤完成事件
#         yield EventEmitter.create_step_finished_event(
#             step_name="生成预测性问题",
#             description=f"成功生成 {len(result.questions)} 个预测性问题",
#             result_summary=f"生成了 {len(result.questions)} 个问题"
#         )
        
#         # 返回结果
#         yield {
#             "prediction_questions": result.questions,
#         }
        
#     except Exception as e:
#         # 发送步骤失败事件
#         yield EventEmitter.create_step_finished_event(
#             step_name="生成预测性问题",
#             description="预测性问题生成失败",
#             error=str(e)
#         )
#         if configurable.enable_debug:
#             print(f"预测性问题生成异常: {str(e)}")
#         raise


# async def generate_quantitative_questions(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """生成量化筛选问题"""
#     configurable = VestConfiguration.from_runnable_config(config)
    
#     # 发送步骤开始事件
#     yield EventEmitter.create_step_started_event(
#         step_name="生成量化分析问题",
#         description="正在生成量化投资分析问题..."
#     )
    
#     # 创建PydanticOutputParser
#     parser = PydanticOutputParser(pydantic_object=QuestionList)
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.question_generation_model,
#         temperature=0.7,
#         streaming=False
#     )
    
#     # 使用产业链分析结果作为输入
#     industry_summary = "\n".join(state.get("industry_chain_results", []))
    
#     # 在prompt中添加格式说明
#     formatted_prompt = QUANTITATIVE_PROMPT.format(
#         industry_analysis=industry_summary or "暂无产业链分析结果"
#     ) + "\n\n" + parser.get_format_instructions()
    
#     try:
#         # 调用LLM获取响应
#         response = await llm.ainvoke(formatted_prompt)
        
#         # 使用parser解析响应
#         result = parser.parse(response.content)
        
#         # 发送问题卡片数据事件
#         yield {
#             "type": "CustomEvent",
#             "data": {
#                 "type": "QUESTIONS_GENERATED",
#                 "step_name": "生成量化分析问题",
#                 "questions": [
#                     {
#                         "id": f"quant_q_{i+1}",
#                         "title": f"量化分析问题 {i+1}",
#                         "content": question,
#                         "type": "quantitative",
#                         "status": "generated",
#                         "category": "量化分析",
#                         "icon": "📊"
#                     }
#                     for i, question in enumerate(result.questions)
#                 ],
#                 "total_count": len(result.questions),
#                 "category": "量化分析问题"
#             }
#         }
        
#         # 发送步骤完成事件
#         yield EventEmitter.create_step_finished_event(
#             step_name="生成量化分析问题",
#             description=f"成功生成 {len(result.questions)} 个量化分析问题",
#             result_summary=f"生成了 {len(result.questions)} 个问题"
#         )
        
#         # 返回结果
#         yield {
#             "quantitative_questions": result.questions,
#         }
        
#     except Exception as e:
#         # 发送步骤失败事件
#         yield EventEmitter.create_step_finished_event(
#             step_name="生成量化分析问题",
#             description="量化分析问题生成失败",
#             error=str(e)
#         )
#         if configurable.enable_debug:
#             print(f"量化分析问题生成异常: {str(e)}")
#         raise


# # 在 NodeNames 类中添加
# class NodeNames:
#     """图节点名称常量"""
#     TRUTH_GENERATE_FACT_QUESTIONS = "truth_generate_fact_questions"
#     TRUTH_EYE_INITIAL_ANALYSIS = "truth_eye_initial_analysis"  # 新增
#     TRUTH_MACRO_ANALYSIS_LOOP = "truth_macro_analysis_loop"
    
#     CASSANDRA_GENERATE_FACT_QUESTIONS = "cassandra_generate_fact_questions"
#     CASSANDRA_EYE_INITIAL_ANALYSIS = "cassandra_eye_initial_analysis"  # 新增
#     CASSANDRA_MACRO_ANALYSIS_LOOP = "cassandra_macro_analysis_loop"

#     ALPHA_GENERATE_FACT_QUESTIONS = "alpha_generate_fact_questions"
#     ALPHA_EYE_INITIAL_ANALYSIS = "alpha_eye_initial_analysis"  # 新增
#     ALPHA_MACRO_ANALYSIS_LOOP = "alpha_macro_analysis_loop"


#     FINAL_SYNTHESIS_LOOP = "final_synthesis_loop"
#     FINALIZE_INVESTMENT_THESIS = "finalize_investment_thesis"


# # 添加新的初步事实分析节点函数
# async def truth_eye_initial_analysis(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """真理之眼 - 初步事实分析"""
#     configurable = VestConfiguration.from_runnable_config(config)
    
#     yield EventEmitter.create_step_started_event(
#         step_name="真理之眼 - 初步事实分析",
#         description="基于初始信息和第一轮收集信息进行全面的事实分析..."
#     )
    
#     # 导入初步事实分析提示词
#     from .promotes import truth_eye_2 as initial_analysis_prompt
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.analysis_model,
#         temperature=0.3,
#         streaming=False
#     )
    
#     formatted_prompt = initial_analysis_prompt.PROMPT.format(
#         profile=state["profile"],
#         first_round_answers=state.get("fact_check_questions", [])
#     )
    
#     try:
#         response = await llm.ainvoke(formatted_prompt)
        
#         yield EventEmitter.create_step_finished_event(
#             step_name="真理之眼 - 初步事实分析",
#             description="完成初步事实分析报告",
#             result_summary="生成了结构化的初步事实分析报告"
#         )
        
#         yield {
#             "type": "CustomEvent",
#             "data": {
#                 "type": "INITIAL_ANALYSIS_COMPLETE",
#                 "step_name": "真理之眼 - 初步事实分析",
#                 "analysis_report": response.content,
#                 "timestamp": datetime.now().isoformat()
#             }
#         }
        
#         yield {
#             "initial_analysis_report": response.content
#         }
        
#     except Exception as e:
#         if configurable.enable_debug:
#             print(f"初步事实分析异常: {str(e)}")
#         yield {
#             "initial_analysis_report": "初步事实分析过程中出现异常"
#         }


# async def cassandra_initial_analysis(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """卡桑德拉 - 初步事实分析"""
#     configurable = VestConfiguration.from_runnable_config(config)
    
#     yield EventEmitter.create_step_started_event(
#         step_name="卡桑德拉 - 初步事实分析",
#         description="基于预测问题和收集信息进行全面的事实分析..."
#     )
    
#     # 导入卡桑德拉初步事实分析提示词
#     from .promotes.cassandra2 import PROMPT as CASSANDRA_ANALYSIS_PROMPT
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.analysis_model,
#         temperature=0.3,
#         streaming=False
#     )
    
#     formatted_prompt = CASSANDRA_ANALYSIS_PROMPT.format(
#         profile=state["profile"],
#         first_round_answers=state.get("prediction_questions", [])
#     )
    
#     try:
#         response = await llm.ainvoke(formatted_prompt)
        
#         yield EventEmitter.create_step_finished_event(
#             step_name="卡桑德拉 - 初步事实分析",
#             description="完成卡桑德拉初步事实分析报告",
#             result_summary="生成了结构化的预测分析报告"
#         )
        
#         yield {
#             "type": "CustomEvent",
#             "data": {
#                 "type": "CASSANDRA_ANALYSIS_COMPLETE",
#                 "step_name": "卡桑德拉 - 初步事实分析",
#                 "analysis_report": response.content,
#                 "timestamp": datetime.now().isoformat()
#             }
#         }
        
#         yield {
#             "cassandra_analysis_report": response.content
#         }
        
#     except Exception as e:
#         if configurable.enable_debug:
#             print(f"卡桑德拉初步事实分析异常: {str(e)}")
#         yield {
#             "cassandra_analysis_report": "卡桑德拉初步事实分析过程中出现异常"
#         }


# async def macro_analysis_loop(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """改造后的宏观分析循环 - 内部执行深度问题钻探和最终事实合成两个步骤"""
#     configurable = VestConfiguration.from_runnable_config(config)
    
#     # 检查循环次数
#     current_count = state.get("macro_loop_count", 0)
#     if current_count >= configurable.max_macro_loops:
#         yield {"macro_loop_count": current_count}
#         return
    
#     # 第一步：深度问题钻探
#     yield EventEmitter.create_step_started_event(
#         step_name=f"宏观分析循环 (第{current_count + 1}轮) - 深度问题钻探",
#         description="基于初步分析报告进行深度质疑和追问..."
#     )
    
#     # 导入深度问题钻探提示词
    
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.analysis_model,
#         temperature=0.5,
#         streaming=False
#     )
    
#     formatted_prompt = deep_drilling_prompt.PROMPT.format(
#         initial_report=state.get("initial_analysis_report", "")
#     )
    
#     try:
#         # 执行深度问题钻探
#         deep_response = await llm.ainvoke(formatted_prompt)
        
#         yield EventEmitter.create_step_finished_event(
#             step_name=f"宏观分析循环 (第{current_count + 1}轮) - 深度问题钻探",
#             description="生成深度追问清单",
#             result_summary="生成了针对性的深度质疑问题"
#         )
        
#         # 第二步：最终事实合成
#         yield EventEmitter.create_step_started_event(
#             step_name=f"宏观分析循环 (第{current_count + 1}轮) - 最终事实合成",
#             description="整合所有信息，形成最终事实全景报告..."
#         )
        
        
        
#         llm_synthesis = LLMClientUtils.create_llm(
#             model_type=configurable.analysis_model,
#             temperature=0.2,  # 最低温度确保客观性
#             streaming=False
#         )
        
#         synthesis_formatted_prompt = final_synthesis_prompt.PROMPT.format(
#             initial_report=state.get("initial_analysis_report", ""),
#             deep_questions_answers=deep_response.content
#         )
        
#         # 执行最终事实合成
#         synthesis_response = await llm_synthesis.ainvoke(synthesis_formatted_prompt)
        
#         yield EventEmitter.create_step_finished_event(
#             step_name=f"宏观分析循环 (第{current_count + 1}轮) - 最终事实合成",
#             description="完成最终事实全景报告",
#             result_summary="生成了综合性的最终事实报告"
#         )
        
#         # 发送完成事件
#         yield {
#             "type": "CustomEvent",
#             "data": {
#                 "type": "MACRO_ANALYSIS_COMPLETE",
#                 "step_name": f"宏观分析循环 (第{current_count + 1}轮)",
#                 "deep_questions": deep_response.content,
#                 "final_synthesis": synthesis_response.content,
#                 "timestamp": datetime.now().isoformat()
#             }
#         }
        
#         # 返回结果
#         yield {
#             "macro_analysis_results": [deep_response.content, synthesis_response.content],
#             "deep_drilling_questions": deep_response.content,
#             "final_synthesis_report": synthesis_response.content,
#             "macro_loop_count": current_count + 1
#         }
        
#     except Exception as e:
#         if configurable.enable_debug:
#             print(f"宏观分析循环异常: {str(e)}")
#         yield {
#             "macro_analysis_results": ["宏观分析循环过程中出现异常"],
#             "macro_loop_count": current_count + 1
#         }


# async def industry_analysis_loop(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """改造后的产业分析循环 - 内部执行卡桑德拉深度问题钻探和最终事实合成"""
#     configurable = VestConfiguration.from_runnable_config(config)
    
#     # 检查循环次数
#     current_count = state.get("industry_loop_count", 0)
#     if current_count >= configurable.max_industry_loops:
#         yield {"industry_loop_count": current_count}
#         return
    
#     # 第一步：卡桑德拉深度问题钻探
#     yield EventEmitter.create_step_started_event(
#         step_name=f"产业分析循环 (第{current_count + 1}轮) - 卡桑德拉深度问题钻探",
#         description="基于卡桑德拉分析报告进行深度质疑和追问..."
#     )
    
#     # 导入卡桑德拉深度问题钻探提示词
#     from .promotes.cassandra3 import PROMPT as CASSANDRA_DEEP_DRILLING_PROMPT
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.analysis_model,
#         temperature=0.5,
#         streaming=False
#     )
    
#     formatted_prompt = CASSANDRA_DEEP_DRILLING_PROMPT.format(
#         initial_report=state.get("cassandra_analysis_report", "")
#     )
    
#     try:
#         # 执行卡桑德拉深度问题钻探
#         deep_response = await llm.ainvoke(formatted_prompt)
        
#         yield EventEmitter.create_step_finished_event(
#             step_name=f"产业分析循环 (第{current_count + 1}轮) - 卡桑德拉深度问题钻探",
#             description="生成卡桑德拉深度追问清单",
#             result_summary="生成了针对性的深度质疑问题"
#         )
        
#         # 第二步：卡桑德拉最终事实合成
#         yield EventEmitter.create_step_started_event(
#             step_name=f"产业分析循环 (第{current_count + 1}轮) - 卡桑德拉最终事实合成",
#             description="整合所有预测信息，形成最终事实全景报告..."
#         )
        
#         # 导入卡桑德拉最终事实合成提示词
#         from .promotes.cassandra4 import PROMPT as CASSANDRA_FINAL_SYNTHESIS_PROMPT
        
#         llm_synthesis = LLMClientUtils.create_llm(
#             model_type=configurable.analysis_model,
#             temperature=0.2,
#             streaming=False
#         )
        
#         synthesis_formatted_prompt = CASSANDRA_FINAL_SYNTHESIS_PROMPT.format(
#             initial_report=state.get("cassandra_analysis_report", ""),
#             deep_questions_answers=deep_response.content
#         )
        
#         # 执行卡桑德拉最终事实合成
#         synthesis_response = await llm_synthesis.ainvoke(synthesis_formatted_prompt)
        
#         yield EventEmitter.create_step_finished_event(
#             step_name=f"产业分析循环 (第{current_count + 1}轮) - 卡桑德拉最终事实合成",
#             description="完成卡桑德拉最终事实全景报告",
#             result_summary="生成了综合性的最终预测报告"
#         )
        
#         # 发送完成事件
#         yield {
#             "type": "CustomEvent",
#             "data": {
#                 "type": "INDUSTRY_ANALYSIS_COMPLETE",
#                 "step_name": f"产业分析循环 (第{current_count + 1}轮)",
#                 "deep_questions": deep_response.content,
#                 "final_synthesis": synthesis_response.content,
#                 "timestamp": datetime.now().isoformat()
#             }
#         }
        
#         # 返回结果
#         yield {
#             "industry_analysis_results": [deep_response.content, synthesis_response.content],
#             "cassandra_deep_drilling_questions": deep_response.content,
#             "cassandra_final_synthesis_report": synthesis_response.content,
#             "industry_loop_count": current_count + 1
#         }
        
#     except Exception as e:
#         if configurable.enable_debug:
#             print(f"产业分析循环异常: {str(e)}")
#         yield {
#             "industry_analysis_results": ["产业分析循环过程中出现异常"],
#             "industry_loop_count": current_count + 1
#         }


# async def quantitative_analysis_loop(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """量化投资策略分析循环"""
#     configurable = VestConfiguration.from_runnable_config(config)
    
#     current_count = state.get("quantitative_loop_count", 0)
#     if current_count >= configurable.max_quantitative_loops:
#         return {"quantitative_loop_count": current_count}
    
#     search = VestSearch(configurable)
#     questions = state.get("quantitative_questions", [])
    
#     analysis_results = []
#     for question in questions[:10]:
#         try:
#             search_result = await search.search(question)
            
#             llm = LLMClientUtils.create_llm(
#                 model_type=configurable.analysis_model,
#                 temperature=configurable.analysis_temperature,
#                 streaming=False
#             )
            
#             formatted_prompt = QUANTITATIVE_ANALYSIS_PROMPT.format(
#                 question=question,
#                 search_content=search_result.get('search_content', ''),
#                 industry_analysis="\n".join(state.get("industry_chain_results", []))
#             )
            
#             analysis = await llm.ainvoke(formatted_prompt)
#             analysis_results.append(analysis.content)
            
#         except Exception as e:
#             if configurable.enable_debug:
#                 print(f"量化分析异常 - 问题: '{question}', 错误: {str(e)}")
#             analysis_results.append(f"分析失败: {str(e)}")
    
#     return {
#         "quantitative_results": analysis_results,
#         "quantitative_loop_count": current_count + 1,
#     }


# async def finalize_investment_thesis(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ):
#     """最终投资论点综合"""
#     configurable = VestConfiguration.from_runnable_config(config)
    
#     # 创建PydanticOutputParser
#     parser = PydanticOutputParser(pydantic_object=InvestmentThesis)
    
#     llm = LLMClientUtils.create_llm(
#         model_type=configurable.synthesis_model,
#         temperature=configurable.synthesis_temperature,
#         streaming=False
#     )
    
#     # 整合所有分析结果
#     macro_summary = "\n".join(state.get("macro_analysis_results", []))
#     industry_summary = "\n".join(state.get("industry_chain_results", []))
#     quantitative_summary = "\n".join(state.get("quantitative_results", []))
    
#     formatted_prompt = FINAL_SYNTHESIS_PROMPT.format(
#         profile=state["profile"],
#         macro_analysis=macro_summary,
#         industry_analysis=industry_summary,
#         quantitative_analysis=quantitative_summary
#     ) + "\n\n" + parser.get_format_instructions()
    
#     try:
#         response = await llm.ainvoke(formatted_prompt)
#         result = parser.parse(response.content)
        
#         return {
#             "final_synthesis_results": [result.model_dump_json()],
#         }
        
#     except Exception as e:
#         if configurable.enable_debug:
#             print(f"最终综合分析异常: {str(e)}")
#         raise


# # 路由函数
# def should_continue_macro_loop(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ) -> str:
#     """判断是否继续宏观分析循环"""
#     configurable = VestConfiguration.from_runnable_config(config)
#     current_count = state.get("macro_loop_count", 0)
    
#     if current_count >= configurable.max_macro_loops:
#         return NodeNames.GENERATE_PREDICTION_QUESTIONS
#     else:
#         return NodeNames.MACRO_ANALYSIS_LOOP


# def should_continue_industry_loop(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ) -> str:
#     """判断是否继续产业链分析循环"""
#     configurable = VestConfiguration.from_runnable_config(config)
#     current_count = state.get("industry_loop_count", 0)
    
#     if current_count >= configurable.max_industry_loops:
#         return NodeNames.GENERATE_QUANTITATIVE_QUESTIONS
#     else:
#         return NodeNames.INDUSTRY_ANALYSIS_LOOP


# def should_continue_quantitative_loop(
#     state: InvestmentAnalysisState, config: RunnableConfig
# ) -> str:
#     """判断是否继续量化分析循环"""
#     configurable = VestConfiguration.from_runnable_config(config)
#     current_count = state.get("quantitative_loop_count", 0)
    
#     if current_count >= configurable.max_quantitative_loops:
#         return NodeNames.FINALIZE_INVESTMENT_THESIS
#     else:
#         return NodeNames.QUANTITATIVE_ANALYSIS_LOOP
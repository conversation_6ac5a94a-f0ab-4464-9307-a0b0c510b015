"""健康检查API端点"""
from fastapi import APIRouter, Depends
from datetime import datetime
from dependency_injector.wiring import inject, Provide

from ....core.container import MainContainer
from ....core.settings import settings
from ....infrastructure.services.logging import get_logger

router = APIRouter(prefix="/health", tags=["health"])
logger = get_logger(__name__)


@router.get("/")
@inject
async def health_check():
    """基础健康检查"""
    try:
        return {
            "status": "healthy",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat(),
            "environment": settings.environment
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/detailed")
@inject
async def detailed_health_check():
    """详细健康检查"""
    try:
        health_info = {
            "status": "healthy",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat(),
            "environment": settings.environment,
            "dependencies": {
                "database": "not_configured",  # 待实现
                "llm_service": "available" if settings.llm.openai_api_key else "not_configured",
                "tools": "available",
                "container": "initialized"
            },
            "metrics": {
                "uptime": "unknown",  # 待实现
                "memory_usage": "unknown",  # 待实现
                "cpu_usage": "unknown"  # 待实现
            }
        }
        
        # 判断整体状态
        unhealthy_deps = [k for k, v in health_info["dependencies"].items() 
                         if v in ["error", "unavailable"]]
        
        if unhealthy_deps:
            health_info["status"] = "degraded"
            health_info["issues"] = unhealthy_deps
        
        return health_info
        
    except Exception as e:
        logger.error("Detailed health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/ready")
@inject
async def readiness_check():
    """就绪检查 - 用于Kubernetes等容器编排"""
    try:
        # 检查关键依赖是否就绪
        checks = {
            "container": True,  # IoC容器已初始化
            "config": bool(settings.app_name),  # 配置已加载
            "tools": True  # 工具已加载
        }
        
        is_ready = all(checks.values())
        
        return {
            "ready": is_ready,
            "checks": checks,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return {
            "ready": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/live")
@inject
async def liveness_check():
    """存活检查 - 用于Kubernetes等容器编排"""
    try:
        return {
            "alive": True,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error("Liveness check failed", error=str(e))
        return {
            "alive": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }
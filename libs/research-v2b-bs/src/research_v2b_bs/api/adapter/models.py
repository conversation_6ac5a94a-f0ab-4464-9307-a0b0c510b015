# -*- coding: utf-8 -*-
"""
AG-UI 适配器数据模型
"""

from typing import Optional

from pydantic import BaseModel, Field


class Task(BaseModel):
    """任务模型，用于定义Agent任务"""

    id: str = Field(
        ...,
        description="作为run_id，每次请求的唯一标识",
        min_length=1,
        max_length=100,
    )
    query: str = Field(
        ...,
        description="用户查询内容",
        min_length=1,
        max_length=10000,
    )
    thread_id: Optional[str] = Field(
        None,
        description="对话线程ID，用于多轮对话上下文追踪",
        min_length=1,
        max_length=100,
    )
    # 新增字段支持 interrupt 功能
    user_feedback: Optional[str] = Field(
        None,
        description="用户反馈，用于中断任务",
    )
    is_resume: bool = Field(
        False,
        description="是否为恢复执行的任务",
    )

    class Config:
        """Pydantic 配置"""
        
        json_schema_extra = {
            "example": {
                "id": "task_abc123",
                "query": "请帮我分析这个数据并生成报告",
                "thread_id": "thread_def456",
            }
        }
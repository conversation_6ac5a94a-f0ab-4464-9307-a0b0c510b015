from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass, asdict
from datetime import datetime
import asyncio
import json
import logging
import threading

logger = logging.getLogger(__name__)

class ResearchEventType(str, Enum):
    # 核心研究生命周期事件（与前端严格一致）
    RESEARCH_STARTED = "research_started"
    RESEARCH_COMPLETED = "research_completed"
    RESEARCH_FAILED = "research_failed"
    
    # 阶段生命周期事件（与前端严格一致）
    STAGE_STARTED = "stage_started"
    STAGE_PROGRESS = "stage_progress"
    STAGE_COMPLETED = "stage_completed"
    
    # 步骤生命周期事件（核心）
    STEP_STARTED = "step_started"
    STEP_FINISHED = "step_finished"
    
    # 系统事件
    HEARTBEAT = "heartbeat"
    ERROR = "research_error"

class ResearchStage(str, Enum):
    # 与前端完全一致的阶段定义
    IDLE = "idle"
    FACT_VERIFICATION = "fact-verification"
    IMPACT_SIMULATION = "impact-simulation"
    THESIS_GENERATION = "thesis-generation"
    FINAL_REPORT = "final-report"
    COMPLETED = "completed"

# 阶段映射：将旧的后端阶段映射到新的标准阶段
STAGE_MAPPING = {
    "truth-eye": ResearchStage.FACT_VERIFICATION,
    "cassandra": ResearchStage.IMPACT_SIMULATION,
    "alpha": ResearchStage.THESIS_GENERATION,
}

def normalize_stage(stage_value: str) -> ResearchStage:
    """标准化阶段值，确保与前端一致"""
    if isinstance(stage_value, ResearchStage):
        return stage_value
    
    # 直接匹配
    try:
        return ResearchStage(stage_value)
    except ValueError:
        pass
    
    # 通过映射转换
    if stage_value in STAGE_MAPPING:
        return STAGE_MAPPING[stage_value]
    
    # 默认返回事实验证阶段
    logger.warning(f"未知阶段值: {stage_value}，使用默认阶段")
    return ResearchStage.FACT_VERIFICATION

class ResearchEventEmitter:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, task_id: str = None, run_id: str = None, thread_id: str = None):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ResearchEventEmitter, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, task_id: str = None, run_id: str = None, thread_id: str = None):
        if self._initialized:
            if task_id:
                self.task_id = task_id
            if run_id:
                self.run_id = run_id
            if thread_id:
                self.thread_id = thread_id
            return
            
        self.task_id = task_id or "default_task"
        self.run_id = run_id or "default_run"
        self.thread_id = thread_id or "default_thread"
        self.event_handlers: List[Callable] = []
        self.current_stage = ResearchStage.IDLE
        self._initialized = True
    
    @classmethod
    def get_instance(cls, task_id: str = None, run_id: str = None, thread_id: str = None):
        """获取单例实例"""
        return cls(task_id, run_id, thread_id)
    
    @classmethod
    def reset_instance(cls):
        """重置单例实例（主要用于测试）"""
        with cls._lock:
            cls._instance = None
    
    def update_context(self, task_id: str = None, run_id: str = None, thread_id: str = None):
        """更新上下文信息"""
        if task_id:
            self.task_id = task_id
        if run_id:
            self.run_id = run_id
        if thread_id:
            self.thread_id = thread_id
    
    def add_handler(self, handler: Callable):
        """添加事件处理器"""
        self.event_handlers.append(handler)
        
    async def emit_event(self, event_data: Dict[str, Any]):
        """发射事件到所有处理器"""
        for handler in self.event_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event_data)
                else:
                    handler(event_data)
            except Exception as e:
                logger.error(f"事件处理器执行失败: {e}")
    
    def emit_research_started(self, topic: str, stages: List[str] = None) -> Dict[str, Any]:
        """创建并返回研究开始事件"""
        if stages is None:
            stages = [
                ResearchStage.FACT_VERIFICATION.value,
                ResearchStage.IMPACT_SIMULATION.value,
                ResearchStage.THESIS_GENERATION.value,
                ResearchStage.FINAL_REPORT.value
            ]
        
        return {
            "type": ResearchEventType.RESEARCH_STARTED.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "topic": topic,
                "estimated_duration": 600,
                "stages": stages
            }
        }
    
    def emit_stage_started(self, stage: str, stage_name: str, description: str, steps: List[Dict] = None) -> Dict[str, Any]:
        """创建并返回阶段开始事件"""
        normalized_stage = normalize_stage(stage)
        self.current_stage = normalized_stage
        
        return {
            "type": ResearchEventType.STAGE_STARTED.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "stage": normalized_stage.value,
                "stage_name": stage_name,
                "description": description,
                "estimated_duration": 300,
                "steps": steps or []
            }
        }
    
    def emit_stage_progress(self, stage: str, progress: float, current_step: str = None, 
                           current_action: str = None, content: str = None) -> Dict[str, Any]:
        """创建并返回阶段进度事件"""
        normalized_stage = normalize_stage(stage)
        
        return {
            "type": ResearchEventType.STAGE_PROGRESS.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "stage": normalized_stage.value,
                "progress": progress,
                "current_step": current_step,
                "current_action": current_action,
                "content": content
            }
        }
    
    def emit_stage_completed(self, stage: str, result: Dict[str, Any], duration: int = 0, 
                           next_stage: str = None) -> Dict[str, Any]:
        """创建并返回阶段完成事件"""
        normalized_stage = normalize_stage(stage)
        normalized_next_stage = normalize_stage(next_stage) if next_stage else None
        
        return {
            "type": ResearchEventType.STAGE_COMPLETED.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "stage": normalized_stage.value,
                "result": {
                    "summary": result.get("summary", ""),
                    "details": result.get("details", {}),
                    "confidence_score": result.get("confidence_score"),
                    "key_findings": result.get("key_findings", []),
                    "charts_data": result.get("charts_data", [])
                },
                "duration": duration,
                "next_stage": normalized_next_stage.value if normalized_next_stage else None
            }
        }
    
    def emit_step_started(self, step_name: str, description: str, stage: str = None) -> Dict[str, Any]:
        """创建并返回步骤开始事件（与前端严格一致）"""
        current_stage = normalize_stage(stage) if stage else self.current_stage
        
        return {
            "type": ResearchEventType.STEP_STARTED.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "step_name": step_name,
                "description": description,
                "stage": current_stage.value
            }
        }
    
    def emit_step_finished(self, step_name: str, description: str, result_summary: str = None, 
                          error: str = None, stage: str = None) -> Dict[str, Any]:
        """创建并返回步骤完成事件（与前端严格一致）"""
        current_stage = normalize_stage(stage) if stage else self.current_stage
        
        return {
            "type": ResearchEventType.STEP_FINISHED.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "step_name": step_name,
                "description": description,
                "result_summary": result_summary,
                "error": error,
                "stage": current_stage.value,
                "success": error is None
            }
        }
    
    def emit_research_completed(self, final_report: Dict[str, Any], total_duration: int = 0) -> Dict[str, Any]:
        """创建并返回研究完成事件"""
        return {
            "type": ResearchEventType.RESEARCH_COMPLETED.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "final_report": {
                    "executive_summary": final_report.get("executive_summary", ""),
                    "fact_verification_result": final_report.get("fact_verification_result"),
                    "impact_simulation_result": final_report.get("impact_simulation_result"),
                    "thesis_recommendation_result": final_report.get("thesis_recommendation_result"),
                    "final_report_content": final_report.get("final_report_content", ""),
                    "confidence_score": final_report.get("confidence_score", 0),
                    "recommendations": final_report.get("recommendations", []),
                    "risk_factors": final_report.get("risk_factors", []),
                    "charts_data": final_report.get("charts_data", [])
                },
                "total_duration": total_duration,
                "completed_at": datetime.now().isoformat()
            }
        }
    
    def emit_research_failed(self, error_code: str, error_message: str, failed_stage: str = None, 
                           failed_step: str = None, retry_possible: bool = True) -> Dict[str, Any]:
        """创建并返回研究失败事件"""
        normalized_failed_stage = normalize_stage(failed_stage) if failed_stage else None
        
        return {
            "type": ResearchEventType.RESEARCH_FAILED.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "error_code": error_code,
                "error_message": error_message,
                "failed_stage": normalized_failed_stage.value if normalized_failed_stage else None,
                "failed_step": failed_step,
                "retry_possible": retry_possible,
                "partial_results": {}
            }
        }
    
    def emit_heartbeat(self, current_stage: str = None, progress: float = None) -> Dict[str, Any]:
        """创建并返回心跳事件"""
        normalized_stage = normalize_stage(current_stage) if current_stage else self.current_stage
        
        return {
            "type": ResearchEventType.HEARTBEAT.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "status": "alive",
                "current_stage": normalized_stage.value,
                "progress": progress
            }
        }
    
    def emit_error(self, error_code: str, error_message: str, error_details: Any = None, 
                  recoverable: bool = True) -> Dict[str, Any]:
        """创建并返回错误事件"""
        return {
            "type": ResearchEventType.ERROR.value,
            "task_id": self.task_id,
            "run_id": self.run_id,
            "thread_id": self.thread_id,
            "timestamp": datetime.now().isoformat(),
            "data": {
                "error_code": error_code,
                "error_message": error_message,
                "error_details": error_details,
                "recoverable": recoverable
            }
        }

def is_valid_event_type(value: str) -> bool:
    """验证事件类型是否有效"""
    try:
        ResearchEventType(value)
        return True
    except ValueError:
        return False
"""API路由适配器

提供Research V2的API端点定义。
"""

import json
import logging
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Dict, Optional,Any,List
import os
import uuid
import json
import asyncio
import time



from ..application.use_cases import ResearchV2UseCase, AnalystTemplateUseCase
from ..domain.entities import ResearchRequest, ResearchResult, AnalystTemplate

# Get logger instance
logger = logging.getLogger(__name__)

# 请求响应模型
class StartResearchRequest(BaseModel):
    """开始研究请求模型"""
    query: str
    analyst_type: Optional[str] = "default"
    config: Optional[Dict[str,Any]] = None


class StartResearchResponse(BaseModel):
    """开始研究响应模型"""
    task_id: str
    status: str = "started"


class ResearchStatusResponse(BaseModel):
    """研究状态响应模型"""
    task_id: str
    status: str
    progress: float
    result: Optional[Dict[str, Any]] = None


# 创建路由器
router = APIRouter(prefix="/research-v2b", tags=["research-v2b"])

# 初始化用例
research_use_case = ResearchV2UseCase()
template_use_case = AnalystTemplateUseCase()

# 全局变量：投资分析图和适配器
_analysis_graph = None
_agui_adapter = None

def get_analysis_graph():
    """获取投资分析图实例（单例模式）"""
    global _analysis_graph
    if _analysis_graph is None:
        try:
            from ..api.demo.graph import graph
            _analysis_graph = graph
            logger.info("Investment analysis graph created successfully")
        except Exception as e:
            logger.error(f"Failed to create investment analysis graph: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to initialize analysis graph: {str(e)}"
            )
    return _analysis_graph

def get_agui_adapter():
    """获取 AGUI 适配器实例（单例模式）"""
    global _agui_adapter
    if _agui_adapter is None:
        try:
            from ..api.adapter.agui_adapter import AGUIAdapter
            graph = get_analysis_graph()
            _agui_adapter = AGUIAdapter(graph)
            logger.info("AGUI adapter created successfully")
        except Exception as e:
            logger.error(f"Failed to create AGUI adapter: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to initialize AGUI adapter: {str(e)}"
            )
    return _agui_adapter
    
@router.post("/oldstart", response_model=StartResearchResponse)
async def start_research(request: StartResearchRequest):
    """开始研究分析"""
    try:
        research_request = ResearchRequest(
            query=request.query,
            analyst_type=request.analyst_type,
            config=request.config
        )
        
        task_id = await research_use_case.start_research(research_request)
        
        return StartResearchResponse(
            task_id=task_id,
            status="started"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"研究启动失败: {str(e)}")


@router.post("/start", tags=["research"])
async def research_stream_endpoint(request: Request):
    """投资研究分析流式端点 - 兼容AGUI格式"""
    try:
        # 解析请求体
        body = await request.body()
        data = json.loads(body.decode())
        logger.info(f"收到研究流请求: {data}")

        # 提取AGUI消息内容
        thread_id = data.get("threadId", f"thread_{int(asyncio.get_event_loop().time() * 1000)}")
        run_id = data.get("runId", f"run_{int(asyncio.get_event_loop().time() * 1000)}")
        task_id = data.get("taskId",str(uuid.uuid4()))
        user_feedback = data.get("user_feedback")  # 提取用户反馈

        # AGUI消息在messages数组中
        messages = data.get("messages", [])
        if not messages:
            return {"error": "没有找到消息内容"}

        # 获取第一条消息的内容作为查询
        query = messages[0].get("content", "")

        if not query and not user_feedback:  # 如果既没有查询也没有反馈
            return {"error": "缺少查询内容或用户反馈"}
        # 创建任务对象
        from ..api.adapter.models import Task
        task = Task(
            id=task_id,
            query=query,
            thread_id=thread_id,
            user_feedback=user_feedback,  # 传递用户反馈
            is_resume=bool(user_feedback)  # 如果有反馈则为恢复模式
        )

        logger.info(
            f"Starting research analysis for task {task_id}: query={query[:100]}..., "
            f"thread_id={thread_id}, is_resume={task.is_resume}"
        )

        # 获取适配器实例
        adapter = get_agui_adapter()

        # 创建流式响应
        async def event_stream():
            try:
                async for event_data in adapter.create_official_stream(task):
                    yield event_data
            except Exception as e:
                logger.exception(f"Error in event stream for task {task_id}: {e}")
                error_event = f'data: {{"type": "RUN_ERROR", "message": "{str(e)}"}}\n\n'
                yield error_event

        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )

    except Exception as e:
        logger.exception(f"Failed to start research analysis: {e}")
        return {"error": str(e)}


@router.get("/status/{task_id}", response_model=ResearchStatusResponse)
async def get_research_status(task_id: str):
    """获取研究状态"""
    try:
        result = await research_use_case.get_research_status(task_id)
        
        if result is None:
            raise HTTPException(status_code=404, detail="研究任务不存在")
        
        return ResearchStatusResponse(
            task_id=task_id,
            status=result.status,
            progress=result.progress,
            result=result.result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"状态查询失败: {str(e)}")


@router.get("/stream/{task_id}")
async def stream_research_progress(task_id: str):
    """流式获取研究进展"""
    try:
        async def event_generator():
            async for event in research_use_case.stream_research_progress(task_id):
                yield f"data: {event}\n\n"
        
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"流式推送失败: {str(e)}")


@router.get("/templates", response_model=List[AnalystTemplate])
async def list_analyst_templates():
    """获取分析师模板列表"""
    try:
        templates = await template_use_case.list_templates()
        return templates
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板列表获取失败: {str(e)}")


@router.get("/templates/{template_name}", response_model=AnalystTemplate)
async def get_analyst_template(template_name: str):
    """获取指定分析师模板"""
    try:
        template = await template_use_case.get_template(template_name)
        
        if template is None:
            raise HTTPException(status_code=404, detail="分析师模板不存在")
        
        return template
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板获取失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "research-v2b-bs",
        "version": "0.1.0"
    }
"""领域实体定义

定义研究V2相关的核心业务实体。
"""

from pydantic import BaseModel
from typing import Dict, Any, Optional
from datetime import datetime


class ResearchRequest(BaseModel):
    """研究请求实体"""
    
    query: str
    analyst_type: Optional[str] = "default"
    config: Optional[Dict[str, Any]] = None
    created_at: datetime = datetime.now()


class ResearchResult(BaseModel):
    """研究结果实体"""
    
    request_id: str
    status: str
    result: Optional[Dict[str, Any]] = None
    progress: float = 0.0
    created_at: datetime = datetime.now()
    completed_at: Optional[datetime] = None


class AnalystTemplate(BaseModel):
    """分析师模板实体"""
    
    name: str
    description: str
    methodology: Dict[str, Any]
    config: Optional[Dict[str, Any]] = None
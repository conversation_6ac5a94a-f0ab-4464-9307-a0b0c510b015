[build-system]
requires = ["setuptools>=64", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "research-v2b-bs"
version = "0.1.0"
description = "Research V2B Backend Service Plugin (Barry Version)"
authors = [
    {name = "Y-AI Team", email = "<EMAIL>"}
]
dependencies = [
    "fastapi>=0.104.0",
    "pydantic>=2.0.0",
    "yai-loguru-sinks>=0.6.3",
    "typing-extensions>=4.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
     "yai-loguru-sinks>=0.6.3",
    "httpx>=0.25.0",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"
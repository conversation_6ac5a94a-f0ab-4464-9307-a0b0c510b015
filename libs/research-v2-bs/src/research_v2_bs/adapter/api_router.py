"""API路由适配器

提供Research V2的API端点定义。
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from typing import Dict, Any, List, Optional
from pydantic import BaseModel

from ..application.use_cases import ResearchV2UseCase, AnalystTemplateUseCase
from ..domain.entities import ResearchRequest, ResearchResult, AnalystTemplate


# 请求响应模型
class StartResearchRequest(BaseModel):
    """开始研究请求模型"""
    query: str
    analyst_type: Optional[str] = "default"
    config: Optional[Dict[str, Any]] = None


class StartResearchResponse(BaseModel):
    """开始研究响应模型"""
    task_id: str
    status: str = "started"


class ResearchStatusResponse(BaseModel):
    """研究状态响应模型"""
    task_id: str
    status: str
    progress: float
    result: Optional[Dict[str, Any]] = None


# 创建路由器
router = APIRouter(prefix="/research-v2", tags=["research-v2"])

# 初始化用例
research_use_case = ResearchV2UseCase()
template_use_case = AnalystTemplateUseCase()


@router.post("/start", response_model=StartResearchResponse)
async def start_research(request: StartResearchRequest):
    """开始研究分析"""
    try:
        research_request = ResearchRequest(
            query=request.query,
            analyst_type=request.analyst_type,
            config=request.config
        )
        
        task_id = await research_use_case.start_research(research_request)
        
        return StartResearchResponse(
            task_id=task_id,
            status="started"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"研究启动失败: {str(e)}")


@router.get("/status/{task_id}", response_model=ResearchStatusResponse)
async def get_research_status(task_id: str):
    """获取研究状态"""
    try:
        result = await research_use_case.get_research_status(task_id)
        
        if result is None:
            raise HTTPException(status_code=404, detail="研究任务不存在")
        
        return ResearchStatusResponse(
            task_id=task_id,
            status=result.status,
            progress=result.progress,
            result=result.result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"状态查询失败: {str(e)}")


@router.get("/stream/{task_id}")
async def stream_research_progress(task_id: str):
    """流式获取研究进展"""
    try:
        async def event_generator():
            async for event in research_use_case.stream_research_progress(task_id):
                yield f"data: {event}\n\n"
        
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"流式推送失败: {str(e)}")


@router.get("/templates", response_model=List[AnalystTemplate])
async def list_analyst_templates():
    """获取分析师模板列表"""
    try:
        templates = await template_use_case.list_templates()
        return templates
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板列表获取失败: {str(e)}")


@router.get("/templates/{template_name}", response_model=AnalystTemplate)
async def get_analyst_template(template_name: str):
    """获取指定分析师模板"""
    try:
        template = await template_use_case.get_template(template_name)
        
        if template is None:
            raise HTTPException(status_code=404, detail="分析师模板不存在")
        
        return template
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板获取失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "research-v2-bs",
        "version": "0.1.0"
    }
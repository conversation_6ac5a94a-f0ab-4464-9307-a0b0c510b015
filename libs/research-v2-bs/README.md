# Research V2 Backend Service Plugin

## 概述

插件化的研究演示 V2 后端服务，提供灵活的研究分析框架。

## 架构

```
src/research_v2_bs/
├── adapter/          # API路由适配层
├── application/      # 应用业务逻辑层  
├── domain/          # 领域模型层
└── infrastructure/  # 基础设施层
```

## 使用方式

```python
from research_v2_bs import router

# 在主应用中注册路由
app.include_router(router, prefix="/api/v1/research-v2")
```

## 开发说明

- 所有业务逻辑保持留白状态，便于后续定制开发
- 遵循洋葱架构模式，保持清晰的层次分离
- 支持插件化扩展和热插拔
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "shared-bs-core"
version = "0.1.0"
description = "Shared backend core utilities for YAI Investor Insight"
authors = [
    {name = "YAI Team", email = "<EMAIL>"},
]
dependencies = [
    "fastapi>=0.104.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "sqlalchemy>=2.0.0",
    "asyncpg>=0.29.0",
    "python-multipart>=0.0.6",
    # 日志相关依赖
    "loguru>=0.7.0",
    "yai-loguru-sinks>=0.6.3",
    "structlog>=25.4.0",
    "python-dotenv>=1.0.0",
]
requires-python = ">=3.11"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "ruff>=0.1.0",
    "mypy>=1.6.0",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.ruff]
target-version = "py311"
line-length = 88
select = ["E", "W", "F", "I", "N", "B", "UP"]
ignore = ["E501", "B008"]

[tool.ruff.isort]
known-first-party = ["shared_bs_core"]

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
"""统一日志配置模块 - Backend Core 日志管理"""
import os
from pathlib import Path
from typing import Optional
from loguru import logger
from yai_loguru_sinks import register_protocol_parsers, create_config_from_file

# 全局配置标志
_configured = False


def configure_logging(
    log_level: Optional[str] = None,
    service_name: str = "backend-service",
    config_file_path: Optional[str] = None
) -> None:
    """配置结构化日志 - 使用 yai-loguru-sinks

    Args:
        log_level: 日志级别，默认从环境变量获取
        service_name: 服务名称，用于日志文件命名
        config_file_path: 自定义配置文件路径
    """
    global _configured
    if _configured:
        return

    # 注册协议解析器（必须在配置前调用）
    register_protocol_parsers()

    # 确定配置文件路径
    if config_file_path:
        config_file = Path(config_file_path)
    else:
        # 默认查找 shared-bs-core 中的配置文件
        config_file = Path(__file__).parent / "config" / "logging.yaml"

    if not config_file.exists():
        # 如果配置文件不存在，使用默认配置
        _configure_default_logging(log_level, service_name)
    else:
        # 使用配置文件
        try:
            create_config_from_file(str(config_file))
            logger.info(f"日志配置已加载: {config_file}")
        except Exception as e:
            logger.warning(f"加载日志配置文件失败，使用默认配置: {e}")
            _configure_default_logging(log_level, service_name)

    _configured = True


def _configure_default_logging(
    log_level: Optional[str] = None,
    service_name: str = "backend-service"
) -> None:
    """配置默认日志设置"""
    # 移除默认处理器
    logger.remove()

    # 获取日志级别和环境
    level = log_level or os.getenv("LOG_LEVEL", "INFO")
    environment = os.getenv("ENVIRONMENT", "development")

    # 控制台输出
    logger.add(
        sink=lambda msg: print(msg, end=""),
        level=level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        colorize=True
    )

    # 文件输出 - 输出到 workspace logs 目录
    log_dir = Path("../../logs")
    log_dir.mkdir(exist_ok=True)

    logger.add(
        sink=str(log_dir / f"{service_name}.log"),
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message} | {extra}",
        rotation="10 MB",
        retention="30 days",
        compression="gz"
    )

    logger.info(f"使用默认日志配置 - 服务: {service_name}")


def get_logger(name: str, service_name: str = "backend-service"):
    """获取日志器实例

    Args:
        name: 日志器名称，通常使用 __name__
        service_name: 服务名称，用于配置识别

    Returns:
        配置好的日志器实例
    """
    # 确保日志已配置
    if not _configured:
        configure_logging(service_name=service_name)

    return logger.bind(logger_name=name, service=service_name)


# 为了向后兼容，提供旧的导入路径
__all__ = ["configure_logging", "get_logger"]
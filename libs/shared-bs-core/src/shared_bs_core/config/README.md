# Backend Core 日志配置

这个目录包含了所有后端服务的统一日志配置。

## 配置文件

### logging.yaml
统一的日志配置文件，支持：
- 控制台输出（彩色格式）
- 文件输出（轮转、压缩）
- SLS 远程日志收集（可选）

## 环境变量

| 变量名 | 描述 | 默认值 | 示例 |
|--------|------|--------|------|
| `SERVICE_NAME` | 服务名称 | `backend-service` | `api-server`, `research-service` |
| `LOG_LEVEL` | 日志级别 | `INFO` | `DEBUG`, `INFO`, `WARNING`, `ERROR` |
| `SLS_ENABLED` | 启用 SLS 日志收集 | `false` | `true`, `false` |
| `SLS_PROJECT` | SLS 项目名称 | `yai-log-test` | `yai-production` |
| `SLS_LOGSTORE` | SLS 日志库名称 | `app-log` | `backend-logs` |
| `SLS_REGION` | SLS 区域 | `cn-beijing` | `cn-shanghai` |
| `SLS_ENDPOINT` | SLS 服务端点 | - | `https://cn-beijing.log.aliyuncs.com` |
| `SLS_ACCESS_KEY_ID` | SLS 访问密钥 ID | - | `LTAI...` |
| `SLS_ACCESS_KEY_SECRET` | SLS 访问密钥 | - | `...` |

## 使用方法

### 在服务中使用

```python
from shared_bs_core.logger import configure_logging, get_logger

# 配置日志（通常在应用启动时调用一次）
configure_logging(service_name="api-server")

# 获取日志器
logger = get_logger(__name__, service_name="api-server")

# 使用日志
logger.info("服务启动")
logger.error("发生错误", error_code=500)
```

### 自定义配置文件

```python
from shared_bs_core.logger import configure_logging

# 使用自定义配置文件
configure_logging(
    service_name="my-service",
    config_file_path="/path/to/custom/logging.yaml"
)
```

## 日志格式

### 控制台输出
```
2024-01-01 12:00:00 | INFO     | module:function:123 - 消息内容
```

### 文件输出
```
2024-01-01 12:00:00 | INFO     | module:function:123 - 消息内容 | {"service": "api-server", "extra": "data"}
```

## 文件位置

日志文件默认输出到项目根目录的 `logs/` 目录：
- `logs/{SERVICE_NAME}.log` - 当前日志文件
- `logs/{SERVICE_NAME}.log.1.gz` - 轮转的压缩日志文件

# Backend Core 统一日志配置
# 适用于所有后端服务的日志规范定义

handlers:
  # 控制台输出 - 开发和生产环境都启用
  - sink: ext://sys.stdout
    level: "INFO"
    format: "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    colorize: true
    
  # 文件输出 - 输出到 workspace logs 目录
  # 服务名称通过环境变量 SERVICE_NAME 指定，默认为 backend-service
  - sink: "../../logs/${SERVICE_NAME:backend-service}.log"
    level: "DEBUG"
    format: "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message} | {extra}"
    rotation: "10 MB"
    retention: "30 days"
    compression: "gz"
    
  # SLS 输出 - 生产环境日志收集（可选）
  # 需要配置相应的环境变量：SLS_ENDPOINT, SLS_ACCESS_KEY_ID, SLS_ACCESS_KEY_SECRET 等
  # 项目和日志库名称通过环境变量指定
  - sink: "sls://yai-log-test/app-log?region=cn-beijing&pack_id_enabled=true&pack_id_prefix=api-server"
    level: "DEBUG"
    format: "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message} | {extra}"
    # 仅在配置了 SLS 环境变量时启用
    enabled: true

# 环境变量说明:
# SERVICE_NAME: 服务名称，用于日志文件命名和 SLS 标识
# LOG_LEVEL: 日志级别 (DEBUG, INFO, WARNING, ERROR)
# SLS_ENABLED: 是否启用 SLS 日志收集 (true/false)
# SLS_PROJECT: SLS 项目名称
# SLS_LOGSTORE: SLS 日志库名称
# SLS_REGION: SLS 区域
# SLS_ENDPOINT: SLS 服务端点
# SLS_ACCESS_KEY_ID: SLS 访问密钥 ID
# SLS_ACCESS_KEY_SECRET: SLS 访问密钥

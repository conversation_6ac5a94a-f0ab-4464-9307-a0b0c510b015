{"name": "research-v2-fe", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/research-v2-fe/src", "projectType": "library", "tags": ["scope:shared", "type:ui"], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit", "cwd": "libs/research-v2-fe"}}, "build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/research-v2-fe", "main": "libs/research-v2-fe/src/index.ts", "tsConfig": "libs/research-v2-fe/tsconfig.lib.json", "assets": ["libs/research-v2-fe/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/research-v2-fe/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/research-v2-fe/jest.config.ts"}}}}
/**
 * Research V2 Frontend Plugin 导出入口
 * 
 * 参考 demo-feature 模式，专注服务端组件和类型导出
 */

// 页面组件
export { ResearchV2Page } from './lib/pages/research-v2-page';

// 功能组件  
export { ResearchContainer } from './lib/components/research-container';
export { ResearchSidebar } from './lib/components/research-sidebar';

// 阶段组件
export * from './lib/components/stages';

// 状态管理
export { useResearchV2Store, StageStatus, ResearchStage } from './lib/store/store';

// 类型定义
export type {
  ResearchRequest,
  ResearchResult,
  AnalystTemplate,
  ResearchEvent,
  ResearchV2PageProps,
} from './lib/types';

// Store 类型定义
export type {
  StageState,
  ResearchV2State,
  ResearchV2Actions,
  ResearchV2Store,
  ExecutionStep,
  StepStatus
} from './lib/store/store';
'use client';

import { useResearchV2Store, StageStatus } from '../../store/store';
import { RunningStateDisplay } from '../shared/RunningStateDisplay';

/**
 * 最终报告阶段组件
 */
export function FinalReportStage() {
  const { researchTopic, finalReport, reset } = useResearchV2Store();

  // 处理运行中状态
  if (finalReport.status === StageStatus.RUNNING) {
    return (
      <RunningStateDisplay 
        stageState={finalReport}
        stageName="最终报告生成"
      />
    );
  }

  // 如果没有结果，不显示
  if (finalReport.status !== StageStatus.COMPLETED || !finalReport.result) {
    return null;
  }

  return (
    <>
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900">
            最终投资研究报告
          </h2>
        </div>
        <p className="text-sm text-gray-600">关于 "{researchTopic}" 的完整分析报告</p>
      </div>

      <div className="flex-1 p-6 space-y-6">
        {/* 报告内容 */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="text-gray-800 leading-relaxed whitespace-pre-line">
            {finalReport.result}
          </div>
        </div>

        {/* 新研究按钮 */}
        <button 
          onClick={reset}
          className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-sm"
        >
          开始新的研究
        </button>
      </div>
    </>
  );
}
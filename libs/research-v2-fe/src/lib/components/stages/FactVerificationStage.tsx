'use client';

import { useResearchV2Store, ResearchStage, StageStatus } from '../../store/store';
import { RunningStateDisplay } from '../shared/RunningStateDisplay';

/**
 * 事实核查验证阶段组件
 */
export function FactVerificationStage() {
  const { 
    researchTopic, 
    factVerification,
    currentInput, 
    updateInput, 
    completeStage 
  } = useResearchV2Store();

  // 处理运行中状态
  if (factVerification.status === StageStatus.RUNNING) {
    return (
      <RunningStateDisplay 
        stageState={factVerification}
        stageName="事实核查验证"
      />
    );
  }

  // 如果没有结果，不显示
  if (factVerification.status !== StageStatus.COMPLETED || !factVerification.result) {
    return null;
  }

  const handleComplete = () => {
    completeStage(ResearchStage.FACT_VERIFICATION, currentInput);
  };

  return (
    <>
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900">
            事实核查验证结果
          </h2>
        </div>
        <p className="text-sm text-gray-600">针对 "{researchTopic}" 的多维度数据分析</p>
      </div>

      <div className="flex-1 p-6 space-y-6">
        {/* 结果内容 */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="text-gray-800 leading-relaxed whitespace-pre-line">
            {factVerification.result}
          </div>
        </div>

        {/* 用户反馈输入 */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 space-y-4">
          <div className="flex items-center space-x-2">
            <span className="text-lg font-medium text-blue-500">+</span>
            <span className="font-medium text-gray-900">用户反馈（可选）</span>
          </div>
          
          <textarea
            value={currentInput}
            onChange={(e) => updateInput(e.target.value)}
            className="w-full h-32 p-4 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            placeholder="请输入您对事实核查结果的反馈，帮助优化后续分析..."
          />
          
          <button 
            onClick={handleComplete}
            className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-sm"
          >
            开始影响模拟分析 ↗
          </button>
        </div>
      </div>
    </>
  );
}
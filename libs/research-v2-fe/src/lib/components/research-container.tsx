'use client';

import { useResearchV2Store, ResearchStage } from '../store/store';
import { ResearchSidebar } from './research-sidebar';
import { IdleStage } from './stages/IdleStage';
import { FactVerificationStage } from './stages/FactVerificationStage';
import { ImpactSimulationStage } from './stages/ImpactSimulationStage';
import { ThesisRecommendationStage } from './stages/ThesisRecommendationStage';
import { FinalReportStage } from './stages/FinalReportStage';

/**
 * 研究容器组件 - 组合侧边栏和主区域，根据当前阶段显示对应内容
 */
export function ResearchContainer() {
  const { currentStage } = useResearchV2Store();

  // 根据当前阶段渲染对应组件
  const renderMainArea = () => {
    switch (currentStage) {
      case ResearchStage.IDLE:
        return <IdleStage />;
      case ResearchStage.FACT_VERIFICATION:
        return <FactVerificationStage />;
      case ResearchStage.IMPACT_SIMULATION:
        return <ImpactSimulationStage />;
      case ResearchStage.THESIS_RECOMMENDATION:
        return <ThesisRecommendationStage />;
      case ResearchStage.FINAL_REPORT:
        return <FinalReportStage />;
      default:
        return <IdleStage />;
    }
  };

  return (
    <div className="flex h-screen">
      <ResearchSidebar />
      <div className="flex-1 flex flex-col">
        {renderMainArea()}
      </div>
    </div>
  );
}
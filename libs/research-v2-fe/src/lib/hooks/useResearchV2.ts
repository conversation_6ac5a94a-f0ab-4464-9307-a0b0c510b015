'use client';

/**
 * Research V2 自定义Hook
 */

import { useState, useCallback } from 'react';
import type { ResearchRequest, ResearchResult, AnalystTemplate } from '../types';

export interface UseResearchV2Options {
  apiBase?: string;
  onError?: (error: Error) => void;
  onProgress?: (progress: number) => void;
}

export interface UseResearchV2Return {
  // 状态
  isLoading: boolean;
  error: string | null;
  templates: AnalystTemplate[];
  currentResult: ResearchResult | null;
  
  // 操作
  startResearch: (request: ResearchRequest) => Promise<string>;
  getStatus: (taskId: string) => Promise<ResearchResult | null>;
  loadTemplates: () => Promise<void>;
  clearError: () => void;
}

export function useResearchV2(options: UseResearchV2Options = {}): UseResearchV2Return {
  const { apiBase = '/api/v1/research-v2', onError, onProgress } = options;
  
  // 状态管理
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [templates, setTemplates] = useState<AnalystTemplate[]>([]);
  const [currentResult, setCurrentResult] = useState<ResearchResult | null>(null);
  
  // 错误处理
  const handleError = useCallback((err: Error) => {
    setError(err.message);
    onError?.(err);
  }, [onError]);
  
  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // 开始研究
  const startResearch = useCallback(async (request: ResearchRequest): Promise<string> => {
    try {
      setIsLoading(true);
      setError(null);
      
      // TODO: 实现实际的API调用
      // const response = await fetch(`${apiBase}/start`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(request),
      // });
      
      // 临时返回模拟任务ID
      const taskId = `task_${Date.now()}`;
      
      return taskId;
    } catch (err) {
      handleError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [apiBase, handleError]);
  
  // 获取状态
  const getStatus = useCallback(async (taskId: string): Promise<ResearchResult | null> => {
    try {
      setError(null);
      
      // TODO: 实现实际的API调用
      // const response = await fetch(`${apiBase}/status/${taskId}`);
      // const result = await response.json();
      
      // 临时返回模拟结果
      const result: ResearchResult = {
        taskId,
        status: 'pending',
        progress: 0,
        createdAt: new Date().toISOString(),
      };
      
      setCurrentResult(result);
      return result;
    } catch (err) {
      handleError(err as Error);
      return null;
    }
  }, [apiBase, handleError]);
  
  // 加载模板
  const loadTemplates = useCallback(async (): Promise<void> => {
    try {
      setError(null);
      
      // TODO: 实现实际的API调用
      // const response = await fetch(`${apiBase}/templates`);
      // const templates = await response.json();
      
      // 临时返回模拟模板
      const mockTemplates: AnalystTemplate[] = [
        {
          name: 'default',
          description: '默认分析师模板',
          methodology: {},
        },
      ];
      
      setTemplates(mockTemplates);
    } catch (err) {
      handleError(err as Error);
    }
  }, [apiBase, handleError]);
  
  return {
    // 状态
    isLoading,
    error,
    templates,
    currentResult,
    
    // 操作
    startResearch,
    getStatus,
    loadTemplates,
    clearError,
  };
}
/**
 * Research V2 业务逻辑和 Actions
 */
import { StateCreator } from 'zustand';
import { 
  ResearchV2Store, 
  ResearchStage, 
  StageStatus,
  ExecutionStep 
} from './types';
import { initialState } from './initial-state';
import { 
  getStageSteps, 
  getStageActions, 
  generateMockResults 
} from './mock-data';

export const createResearchActions: StateCreator<
  ResearchV2Store,
  [],
  [],
  ResearchV2Store
> = (set, get) => ({
  ...initialState,
  
  // 生成模拟结果
  _generateMockResults: generateMockResults,
  
  // 步骤化模拟研究过程
  _simulateResearch: async (stage: ResearchStage, duration: number) => {
    const { researchTopic } = get();
    const steps = getStageSteps(stage);
    const stepDuration = duration / steps.length;
    
    // 获取对应的状态字段名
    const stageKey = getStageKey(stage);
    
    // 初始化阶段状态
    set(state => ({
      currentStage: stage,
      [stageKey]: {
        status: StageStatus.RUNNING,
        steps: steps,
        progress: 0,
        currentAction: getStageActions(stage, 0)
      },
      error: null
    }));
    
    try {
      // 逐步执行每个步骤
      for (let i = 0; i < steps.length; i++) {
        // 更新当前步骤为运行中
        const updatedSteps = [...steps];
        updatedSteps[i] = { ...updatedSteps[i], status: 'running' };
        
        set(state => ({
          [stageKey]: {
            ...state[stageKey as keyof typeof state] as any,
            steps: updatedSteps,
            progress: Math.round((i / steps.length) * 100),
            currentAction: getStageActions(stage, i)
          }
        }));
        
        // 模拟步骤执行时间
        await new Promise(resolve => setTimeout(resolve, stepDuration));
        
        // 标记当前步骤为完成
        updatedSteps[i] = { ...updatedSteps[i], status: 'completed' };
        
        set(state => ({
          [stageKey]: {
            ...state[stageKey as keyof typeof state] as any,
            steps: updatedSteps,
            progress: Math.round(((i + 1) / steps.length) * 100)
          }
        }));
      }
      
      // 生成最终结果
      const mockResults = generateMockResults(researchTopic);
      const result = getStageResult(stage, mockResults);
      
      // 标记阶段完成
      set(state => ({
        [stageKey]: {
          status: StageStatus.COMPLETED,
          result,
          progress: 100,
          currentAction: undefined,
          steps: steps.map(step => ({ ...step, status: 'completed' as const }))
        }
      }));
      
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '研究过程中发生错误'
      });
    }
  },
  
  // 开始研究
  startResearch: async (topic: string) => {
    const { _simulateResearch } = get();
    
    set({
      researchTopic: topic,
      currentInput: '',
      userFeedback: {},
      error: null
    });
    
    await _simulateResearch(ResearchStage.FACT_VERIFICATION, 8000);
  },
  
  // 完成当前阶段
  completeStage: async (stage: ResearchStage, userInput?: string) => {
    const { _simulateResearch } = get();
    
    // 保存用户反馈
    if (userInput !== undefined) {
      const feedbackKey = getFeedbackKey(stage);
      
      if (feedbackKey) {
        set(state => ({
          userFeedback: {
            ...state.userFeedback,
            [feedbackKey]: userInput
          }
        }));
      }
    }
    
    // 清空当前输入
    set({ currentInput: '' });
    
    // 进入下一阶段
    const nextStage = getNextStage(stage);
    if (nextStage) {
      const duration = getStageDuration(nextStage);
      await _simulateResearch(nextStage, duration);
    }
  },
  
  // 更新输入
  updateInput: (input: string) => {
    set({ currentInput: input });
  },
  
  // 重置所有状态
  reset: () => {
    set(initialState);
  },
  
  // 清除错误
  clearError: () => {
    set({ error: null });
  },
});

// 辅助函数
function getStageKey(stage: ResearchStage): string {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return 'factVerification';
    case ResearchStage.IMPACT_SIMULATION:
      return 'impactSimulation';
    case ResearchStage.THESIS_RECOMMENDATION:
      return 'thesisRecommendation';
    case ResearchStage.FINAL_REPORT:
      return 'finalReport';
    default:
      return '';
  }
}

function getStageResult(stage: ResearchStage, mockResults: ReturnType<typeof generateMockResults>): string {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return mockResults.factVerification;
    case ResearchStage.IMPACT_SIMULATION:
      return mockResults.impactSimulation;
    case ResearchStage.THESIS_RECOMMENDATION:
      return mockResults.thesisRecommendation;
    case ResearchStage.FINAL_REPORT:
      return mockResults.finalReport;
    default:
      return '';
  }
}

function getFeedbackKey(stage: ResearchStage): string | null {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return 'factVerification';
    case ResearchStage.IMPACT_SIMULATION:
      return 'impactSimulation';
    default:
      return null;
  }
}

function getNextStage(stage: ResearchStage): ResearchStage | null {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return ResearchStage.IMPACT_SIMULATION;
    case ResearchStage.IMPACT_SIMULATION:
      return ResearchStage.THESIS_RECOMMENDATION;
    case ResearchStage.THESIS_RECOMMENDATION:
      return ResearchStage.FINAL_REPORT;
    case ResearchStage.FINAL_REPORT:
      return null;
    default:
      return null;
  }
}

function getStageDuration(stage: ResearchStage): number {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return 8000;
    case ResearchStage.IMPACT_SIMULATION:
      return 10000;
    case ResearchStage.THESIS_RECOMMENDATION:
      return 8000;
    case ResearchStage.FINAL_REPORT:
      return 6000;
    default:
      return 5000;
  }
}
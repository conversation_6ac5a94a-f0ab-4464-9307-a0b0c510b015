/**
 * Research V2 主 Store 文件
 * 组装各模块创建最终的状态管理 Store
 */
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ResearchV2Store } from './types';
import { createResearchActions } from './actions';

// 创建 Research V2 状态管理 Store
export const useResearchV2Store = create<ResearchV2Store>()(
  devtools(createResearchActions, {
    name: 'research-v2-store',
  })
);

// 导出类型以供其他组件使用
export type { 
  ResearchV2State, 
  ResearchV2Actions, 
  ResearchV2Store,
  StageState,
  ExecutionStep,
  StepStatus
} from './types';

// 导出枚举（需要作为值导出）
export { StageStatus, ResearchStage } from './types';
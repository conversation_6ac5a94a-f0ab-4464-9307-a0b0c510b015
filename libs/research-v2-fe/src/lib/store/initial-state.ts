/**
 * Research V2 初始状态定义
 */
import { ResearchStage, StageStatus, ResearchV2State } from './types';

export const initialState: ResearchV2State = {
  currentStage: ResearchStage.IDLE,
  researchTopic: '',
  currentInput: '',
  
  // 各阶段状态
  factVerification: { status: StageStatus.IDLE },
  impactSimulation: { status: StageStatus.IDLE },
  thesisRecommendation: { status: StageStatus.IDLE },
  finalReport: { status: StageStatus.IDLE },
  
  // 用户反馈
  userFeedback: {},
  
  error: null,
};
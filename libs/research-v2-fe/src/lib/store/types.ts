/**
 * Research V2 状态管理类型定义
 */

// 阶段状态枚举
export enum StageStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  COMPLETED = 'completed'
}

// 研究阶段枚举
export enum ResearchStage {
  IDLE = 'idle',
  FACT_VERIFICATION = 'fact-verification',
  IMPACT_SIMULATION = 'impact-simulation',
  THESIS_RECOMMENDATION = 'thesis-recommendation',
  FINAL_REPORT = 'final-report'
}

// 执行步骤状态
export type StepStatus = 'pending' | 'running' | 'completed';

// 执行步骤接口
export interface ExecutionStep {
  name: string;
  status: StepStatus;
  description?: string;
}

// 阶段状态接口（增强版）
export interface StageState {
  status: StageStatus;
  result?: string;
  currentAction?: string;      // 当前执行的动作描述
  progress?: number;           // 进度百分比 0-100
  steps?: ExecutionStep[];     // 执行步骤列表
}

// 研究状态接口
export interface ResearchV2State {
  // 当前状态
  currentStage: ResearchStage;
  researchTopic: string;
  currentInput: string;
  
  // 各阶段状态
  factVerification: StageState;
  impactSimulation: StageState;
  thesisRecommendation: StageState;
  finalReport: StageState;
  
  // 用户反馈
  userFeedback: {
    factVerification?: string;
    impactSimulation?: string;
  };
  
  // 错误处理
  error: string | null;
}

// 研究操作接口
export interface ResearchV2Actions {
  // 基础操作
  startResearch: (topic: string) => Promise<void>;
  completeStage: (stage: ResearchStage, userInput?: string) => Promise<void>;
  updateInput: (input: string) => void;
  reset: () => void;
  clearError: () => void;
  
  // 内部方法
  _simulateResearch: (stage: ResearchStage, duration: number) => Promise<void>;
  _generateMockResults: (topic: string) => {
    factVerification: string;
    impactSimulation: string;
    thesisRecommendation: string;
    finalReport: string;
  };
}

// 完整的 Store 类型
export type ResearchV2Store = ResearchV2State & ResearchV2Actions;
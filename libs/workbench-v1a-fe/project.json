{"name": "workbench-v1a-fe", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/workbench-v1a-fe/src", "projectType": "library", "tags": ["scope:workbench", "type:feature", "platform:web"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/workbench-v1a-fe", "main": "libs/workbench-v1a-fe/src/index.ts", "tsConfig": "libs/workbench-v1a-fe/tsconfig.lib.json", "assets": [], "webpackConfig": "libs/workbench-v1a-fe/webpack.config.cjs"}}, "lint": {"executor": "@nx/eslint:lint"}, "type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit -p libs/workbench-v1a-fe/tsconfig.lib.json"}}}}
From: <Saved by Blink>
Snapshot-Content-Location: https://idea-pilot-insight.lovable.app/workspace
Subject: idea-pilot-insight
Date: Fri, 1 Aug 2025 22:50:34 +0800
MIME-Version: 1.0
Content-Type: multipart/related;
	type="text/html";
	boundary="----MultipartBoundary--1OOam4t37Q4W0wYzOsI1GkthzSLBuOURyLF2x4eASm----"


------MultipartBoundary--1OOam4t37Q4W0wYzOsI1GkthzSLBuOURyLF2x4eASm----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://idea-pilot-insight.lovable.app/workspace

<!DOCTYPE html><html lang=3D"en"><head><meta http-equiv=3D"Content-Type" co=
ntent=3D"text/html; charset=3DUTF-8"><link rel=3D"stylesheet" type=3D"text/=
css" href=3D"cid:<EMAIL>" /><l=
ink rel=3D"stylesheet" type=3D"text/css" href=3D"cid:css-a2ee9ad7-282f-41d7=
-<EMAIL>" />
   =20
    <meta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=
=3D1.0">
    <title>idea-pilot-insight</title>
    <meta name=3D"description" content=3D"Lovable Generated Project">
    <meta name=3D"author" content=3D"Lovable">

    <meta property=3D"og:title" content=3D"idea-pilot-insight">
    <meta property=3D"og:description" content=3D"Lovable Generated Project"=
>
    <meta property=3D"og:type" content=3D"website">
    <meta property=3D"og:image" content=3D"https://pub-bb2e103a32db4e198524=
a2e9ed8f35b4.r2.dev/f18a2898-383c-48d6-85d6-16183e262a97/id-preview-c48d804=
0--0dc5d1cc-3710-494a-823b-da576dbf07b9.lovable.app-1753442826729.png">

    <meta name=3D"twitter:card" content=3D"summary_large_image">
    <meta name=3D"twitter:site" content=3D"@lovable_dev">
    <meta name=3D"twitter:image" content=3D"https://pub-bb2e103a32db4e19852=
4a2e9ed8f35b4.r2.dev/f18a2898-383c-48d6-85d6-16183e262a97/id-preview-c48d80=
40--0dc5d1cc-3710-494a-823b-da576dbf07b9.lovable.app-1753442826729.png">
   =20
    <link rel=3D"stylesheet" crossorigin=3D"" href=3D"https://idea-pilot-in=
sight.lovable.app/assets/index-Bc_iacBs.css">
 =20

</head>

  <body>
    <div id=3D"root"><div role=3D"region" aria-label=3D"Notifications (F8)"=
 tabindex=3D"-1" style=3D"pointer-events: none;"><ol tabindex=3D"-1" class=
=3D"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bo=
ttom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div><div=
 class=3D"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=3D=
inset]]:bg-sidebar" style=3D"--sidebar-width: 16rem; --sidebar-width-icon: =
3rem;"><div class=3D"min-h-screen flex flex-col w-full bg-background"><head=
er class=3D"h-16 border-b bg-card/50 backdrop-blur-sm flex items-center jus=
tify-between px-6 shadow-sm"><div class=3D"flex items-center gap-4"><button=
 class=3D"inline-flex items-center justify-center gap-2 whitespace-nowrap r=
ounded-md text-sm font-medium ring-offset-background transition-all duratio=
n-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ri=
ng focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacit=
y-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-=
0 hover:bg-accent hover:text-accent-foreground h-7 w-7" data-sidebar=3D"tri=
gger"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" =
viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D=
"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide luci=
de-panel-left"><rect width=3D"18" height=3D"18" x=3D"3" y=3D"3" rx=3D"2"></=
rect><path d=3D"M9 3v18"></path></svg><span class=3D"sr-only">Toggle Sideba=
r</span></button><div class=3D"flex items-center gap-2"><svg xmlns=3D"http:=
//www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fil=
l=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"rou=
nd" stroke-linejoin=3D"round" class=3D"lucide lucide-brain h-8 w-8 text-pri=
mary"><path d=3D"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .=
556 6.588A4 4 0 1 0 12 18Z"></path><path d=3D"M12 5a3 3 0 1 1 5.997.125 4 4=
 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d=3D"=
M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d=3D"M17.599 6.5a3=
 3 0 0 0 .399-1.375"></path><path d=3D"M6.003 5.125A3 3 0 0 0 6.401 6.5"></=
path><path d=3D"M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d=3D"M19.93=
8 10.5a4 4 0 0 1 .585.396"></path><path d=3D"M6 18a4 4 0 0 1-1.967-.516"></=
path><path d=3D"M19.967 17.484A4 4 0 0 1 18 18"></path></svg><span class=3D=
"text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">AI Tr=
acker</span></div></div><div class=3D"flex-1 max-w-md mx-8"><div class=3D"r=
elative"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"2=
4" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=
=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide l=
ucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-=
foreground h-4 w-4"><circle cx=3D"11" cy=3D"11" r=3D"8"></circle><path d=3D=
"m21 21-4.3-4.3"></path></svg><input class=3D"flex h-10 w-full rounded-md b=
order border-input px-3 py-2 text-base ring-offset-background file:border-0=
 file:bg-transparent file:text-sm file:font-medium file:text-foreground pla=
ceholder:text-muted-foreground focus-visible:outline-none focus-visible:rin=
g-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not=
-allowed disabled:opacity-50 md:text-sm pl-10 bg-background/50" placeholder=
=3D"=E6=90=9C=E7=B4=A2 Idea =E6=88=96=E8=82=A1=E7=A5=A8..."></div></div><di=
v class=3D"flex items-center gap-4"><div class=3D"flex items-center gap-3 b=
g-gradient-card px-4 py-2 rounded-lg border shadow-card"><div class=3D"flex=
 items-center gap-2"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24"=
 height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" =
stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=
=3D"lucide lucide-coins h-4 w-4 text-warning"><circle cx=3D"8" cy=3D"8" r=
=3D"6"></circle><path d=3D"M18.09 10.37A6 6 0 1 1 10.34 18"></path><path d=
=3D"M7 6h1v4"></path><path d=3D"m16.71 13.88.7.71-2.82 2.82"></path></svg><=
span class=3D"font-mono text-lg font-bold">1,850</span><span class=3D"text-=
sm text-muted-foreground">=E7=A7=AF=E5=88=86</span></div><div class=3D"h-4 =
w-px bg-border"></div><div class=3D"flex items-center gap-1 text-sm text-mu=
ted-foreground"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" heig=
ht=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" strok=
e-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"l=
ucide lucide-zap h-3 w-3"><path d=3D"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5=
 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.=
5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"></path></svg><span>+200/=E6=97=
=A5</span></div></div><button class=3D"inline-flex items-center justify-cen=
ter whitespace-nowrap rounded-md text-sm font-medium ring-offset-background=
 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring fo=
cus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 =
[&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-=
gradient-primary text-primary-foreground hover:shadow-glow hover:scale-105 =
transition-all duration-300 h-10 px-4 py-2 gap-2"><svg xmlns=3D"http://www.=
w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"n=
one" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" st=
roke-linejoin=3D"round" class=3D"lucide lucide-plus h-4 w-4"><path d=3D"M5 =
12h14"></path><path d=3D"M12 5v14"></path></svg>=E6=96=B0 Idea</button></di=
v></header><div class=3D"flex flex-1"><div class=3D"group peer hidden md:bl=
ock text-sidebar-foreground" data-state=3D"expanded" data-collapsible=3D"" =
data-variant=3D"sidebar" data-side=3D"left"><div class=3D"duration-200 rela=
tive h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linea=
r group-data-[collapsible=3Doffcanvas]:w-0 group-data-[side=3Dright]:rotate=
-180 group-data-[collapsible=3Dicon]:w-[--sidebar-width-icon]"></div><div c=
lass=3D"duration-200 fixed inset-y-0 z-10 hidden h-svh transition-[left,rig=
ht,width] ease-linear md:flex left-0 group-data-[collapsible=3Doffcanvas]:l=
eft-[calc(var(--sidebar-width)*-1)] group-data-[collapsible=3Dicon]:w-[--si=
debar-width-icon] group-data-[side=3Dleft]:border-r group-data-[side=3Drigh=
t]:border-l w-56 ml-4"><div data-sidebar=3D"sidebar" class=3D"flex h-full w=
-full flex-col bg-sidebar group-data-[variant=3Dfloating]:rounded-lg group-=
data-[variant=3Dfloating]:border group-data-[variant=3Dfloating]:border-sid=
ebar-border group-data-[variant=3Dfloating]:shadow"><div data-sidebar=3D"co=
ntent" class=3D"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data=
-[collapsible=3Dicon]:overflow-hidden text-white bg-slate-700"><div data-si=
debar=3D"group" class=3D"relative flex w-full min-w-0 flex-col p-2"><div da=
ta-sidebar=3D"group-label" class=3D"duration-200 flex h-8 shrink-0 items-ce=
nter rounded-md px-2 text-xs font-medium outline-none ring-sidebar-ring tra=
nsition-[margin,opa] ease-linear focus-visible:ring-2 [&amp;&gt;svg]:size-4=
 [&amp;&gt;svg]:shrink-0 group-data-[collapsible=3Dicon]:-mt-8 group-data-[=
collapsible=3Dicon]:opacity-0 text-sidebar-foreground/70">=E6=88=91=E7=9A=
=84 Ideas</div><div data-sidebar=3D"group-content" class=3D"w-full text-sm"=
><ul data-sidebar=3D"menu" class=3D"flex w-full min-w-0 flex-col gap-1"><di=
v><li data-sidebar=3D"menu-item" class=3D"group/menu-item relative"><button=
 data-sidebar=3D"menu-button" data-size=3D"default" data-active=3D"false" c=
lass=3D"peer/menu-button flex w-full items-center gap-2 overflow-hidden rou=
nded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,heig=
ht,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sideb=
ar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group=
-has-[[data-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:pointer-ev=
ents-none aria-disabled:opacity-50 data-[active=3Dtrue]:bg-sidebar-accent d=
ata-[active=3Dtrue]:font-medium data-[active=3Dtrue]:text-sidebar-accent-fo=
reground data-[state=3Dopen]:hover:bg-sidebar-accent data-[state=3Dopen]:ho=
ver:text-sidebar-accent-foreground group-data-[collapsible=3Dicon]:!size-8 =
group-data-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:truncate [&=
amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:t=
ext-sidebar-accent-foreground h-8 text-sm justify-between"><div class=3D"fl=
ex items-center gap-2 min-w-0 flex-1"><svg xmlns=3D"http://www.w3.org/2000/=
svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=
=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejo=
in=3D"round" class=3D"lucide lucide-brain h-4 w-4 flex-shrink-0"><path d=3D=
"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 =
1 0 12 18Z"></path><path d=3D"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.7=
7 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d=3D"M15 13a4.5 4.5 0=
 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d=3D"M17.599 6.5a3 3 0 0 0 .399-1.=
375"></path><path d=3D"M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d=3D"=
M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d=3D"M19.938 10.5a4 4 0 0 1=
 .585.396"></path><path d=3D"M6 18a4 4 0 0 1-1.967-.516"></path><path d=3D"=
M19.967 17.484A4 4 0 0 1 18 18"></path></svg><span class=3D"truncate text-s=
m font-medium" title=3D"NVIDIA H200=E8=B6=85=E7=BA=A7=E8=8A=AF=E7=89=87=E9=
=87=8F=E4=BA=A7=EF=BC=8CAI=E8=AE=AD=E7=BB=83=E6=95=88=E7=8E=87=E9=9D=A9=E5=
=91=BD=E6=80=A7=E6=8F=90=E5=8D=87">NVIDIA H200=E8=B6=85=E7=BA=A7=E8=8A=AF=
=E7=89=87=E9=87=8F=E4=BA=A7=EF=BC=8CAI=E8=AE=AD=E7=BB=83=E6=95=88=E7=8E=87=
=E9=9D=A9=E5=91=BD=E6=80=A7=E6=8F=90=E5=8D=87</span></div><svg xmlns=3D"htt=
p://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" f=
ill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"r=
ound" stroke-linejoin=3D"round" class=3D"lucide lucide-chevron-right h-4 w-=
4"><path d=3D"m9 18 6-6-6-6"></path></svg></button></li></div><div><li data=
-sidebar=3D"menu-item" class=3D"group/menu-item relative"><button data-side=
bar=3D"menu-button" data-size=3D"default" data-active=3D"false" class=3D"pe=
er/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-=
2 text-left outline-none ring-sidebar-ring transition-[width,height,padding=
] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-=
foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[dat=
a-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none =
aria-disabled:opacity-50 data-[active=3Dtrue]:bg-sidebar-accent data-[activ=
e=3Dtrue]:font-medium data-[active=3Dtrue]:text-sidebar-accent-foreground d=
ata-[state=3Dopen]:hover:bg-sidebar-accent data-[state=3Dopen]:hover:text-s=
idebar-accent-foreground group-data-[collapsible=3Dicon]:!size-8 group-data=
-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;sv=
g]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sideba=
r-accent-foreground h-8 text-sm justify-between"><div class=3D"flex items-c=
enter gap-2 min-w-0 flex-1"><svg xmlns=3D"http://www.w3.org/2000/svg" width=
=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"lucide lucide-brain h-4 w-4 flex-shrink-0"><path d=3D"M12 5a3 3 =
0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"=
></path><path d=3D"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1=
-.556 6.588A4 4 0 1 1 12 18Z"></path><path d=3D"M15 13a4.5 4.5 0 0 1-3-4 4.=
5 4.5 0 0 1-3 4"></path><path d=3D"M17.599 6.5a3 3 0 0 0 .399-1.375"></path=
><path d=3D"M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d=3D"M3.477 10.8=
96a4 4 0 0 1 .585-.396"></path><path d=3D"M19.938 10.5a4 4 0 0 1 .585.396">=
</path><path d=3D"M6 18a4 4 0 0 1-1.967-.516"></path><path d=3D"M19.967 17.=
484A4 4 0 0 1 18 18"></path></svg><span class=3D"truncate text-sm font-medi=
um" title=3D"=E5=BE=AE=E8=BD=AFCopilot=E5=85=A8=E9=9D=A2=E9=9B=86=E6=88=90O=
ffice365=EF=BC=8C=E4=BC=81=E4=B8=9AAI=E5=8A=9E=E5=85=AC=E6=97=B6=E4=BB=A3=
=E5=88=B0=E6=9D=A5">=E5=BE=AE=E8=BD=AFCopilot=E5=85=A8=E9=9D=A2=E9=9B=86=E6=
=88=90Office365=EF=BC=8C=E4=BC=81=E4=B8=9AAI=E5=8A=9E=E5=85=AC=E6=97=B6=E4=
=BB=A3=E5=88=B0=E6=9D=A5</span></div><svg xmlns=3D"http://www.w3.org/2000/s=
vg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=
=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejo=
in=3D"round" class=3D"lucide lucide-chevron-right h-4 w-4"><path d=3D"m9 18=
 6-6-6-6"></path></svg></button></li></div><div><li data-sidebar=3D"menu-it=
em" class=3D"group/menu-item relative"><button data-sidebar=3D"menu-button"=
 data-size=3D"default" data-active=3D"false" class=3D"peer/menu-button flex=
 w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline=
-none ring-sidebar-ring transition-[width,height,padding] focus-visible:rin=
g-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled=
:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=3Dmenu-ac=
tion]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opaci=
ty-50 data-[active=3Dtrue]:bg-sidebar-accent data-[active=3Dtrue]:font-medi=
um data-[active=3Dtrue]:text-sidebar-accent-foreground data-[state=3Dopen]:=
hover:bg-sidebar-accent data-[state=3Dopen]:hover:text-sidebar-accent-foreg=
round group-data-[collapsible=3Dicon]:!size-8 group-data-[collapsible=3Dico=
n]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt=
;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground=
 h-8 text-sm justify-between"><div class=3D"flex items-center gap-2 min-w-0=
 flex-1"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"2=
4" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=
=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide l=
ucide-brain h-4 w-4 flex-shrink-0"><path d=3D"M12 5a3 3 0 1 0-5.997.125 4 4=
 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d=3D"=
M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1=
 1 12 18Z"></path><path d=3D"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></=
path><path d=3D"M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d=3D"M6.003 =
5.125A3 3 0 0 0 6.401 6.5"></path><path d=3D"M3.477 10.896a4 4 0 0 1 .585-.=
396"></path><path d=3D"M19.938 10.5a4 4 0 0 1 .585.396"></path><path d=3D"M=
6 18a4 4 0 0 1-1.967-.516"></path><path d=3D"M19.967 17.484A4 4 0 0 1 18 18=
"></path></svg><span class=3D"truncate text-sm font-medium" title=3D"=E7=89=
=B9=E6=96=AF=E6=8B=89FSD V13=E5=AE=9E=E7=8E=B0L4=E7=BA=A7=E8=87=AA=E5=8A=A8=
=E9=A9=BE=E9=A9=B6=EF=BC=8C=E5=95=86=E4=B8=9A=E5=8C=96=E5=BA=94=E7=94=A8=E5=
=9C=A8=E5=8D=B3">=E7=89=B9=E6=96=AF=E6=8B=89FSD V13=E5=AE=9E=E7=8E=B0L4=E7=
=BA=A7=E8=87=AA=E5=8A=A8=E9=A9=BE=E9=A9=B6=EF=BC=8C=E5=95=86=E4=B8=9A=E5=8C=
=96=E5=BA=94=E7=94=A8=E5=9C=A8=E5=8D=B3</span></div><svg xmlns=3D"http://ww=
w.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D=
"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" =
stroke-linejoin=3D"round" class=3D"lucide lucide-chevron-right h-4 w-4"><pa=
th d=3D"m9 18 6-6-6-6"></path></svg></button></li></div><div><li data-sideb=
ar=3D"menu-item" class=3D"group/menu-item relative"><button data-sidebar=3D=
"menu-button" data-size=3D"default" data-active=3D"false" class=3D"peer/men=
u-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text=
-left outline-none ring-sidebar-ring transition-[width,height,padding] focu=
s-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foregr=
ound disabled:pointer-events-none disabled:opacity-50 group-has-[[data-side=
bar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-d=
isabled:opacity-50 data-[active=3Dtrue]:bg-sidebar-accent data-[active=3Dtr=
ue]:font-medium data-[active=3Dtrue]:text-sidebar-accent-foreground data-[s=
tate=3Dopen]:hover:bg-sidebar-accent data-[state=3Dopen]:hover:text-sidebar=
-accent-foreground group-data-[collapsible=3Dicon]:!size-8 group-data-[coll=
apsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:siz=
e-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-acce=
nt-foreground h-8 text-sm justify-between"><div class=3D"flex items-center =
gap-2 min-w-0 flex-1"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24=
" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor"=
 stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" clas=
s=3D"lucide lucide-brain h-4 w-4 flex-shrink-0"><path d=3D"M12 5a3 3 0 1 0-=
5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></pat=
h><path d=3D"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 =
6.588A4 4 0 1 1 12 18Z"></path><path d=3D"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 =
0 0 1-3 4"></path><path d=3D"M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path=
 d=3D"M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d=3D"M3.477 10.896a4 4=
 0 0 1 .585-.396"></path><path d=3D"M19.938 10.5a4 4 0 0 1 .585.396"></path=
><path d=3D"M6 18a4 4 0 0 1-1.967-.516"></path><path d=3D"M19.967 17.484A4 =
4 0 0 1 18 18"></path></svg><span class=3D"truncate text-sm font-medium" ti=
tle=3D"=E8=8B=B9=E6=9E=9CM4 Ultra=E8=8A=AF=E7=89=87=E5=8F=91=E5=B8=83=EF=BC=
=8C=E7=AB=AF=E4=BE=A7AI=E8=AE=A1=E7=AE=97=E8=83=BD=E5=8A=9B=E8=B6=85=E8=B6=
=8A=E4=BA=91=E7=AB=AF">=E8=8B=B9=E6=9E=9CM4 Ultra=E8=8A=AF=E7=89=87=E5=8F=
=91=E5=B8=83=EF=BC=8C=E7=AB=AF=E4=BE=A7AI=E8=AE=A1=E7=AE=97=E8=83=BD=E5=8A=
=9B=E8=B6=85=E8=B6=8A=E4=BA=91=E7=AB=AF</span></div><svg xmlns=3D"http://ww=
w.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D=
"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" =
stroke-linejoin=3D"round" class=3D"lucide lucide-chevron-right h-4 w-4"><pa=
th d=3D"m9 18 6-6-6-6"></path></svg></button></li></div></ul></div></div><d=
iv data-sidebar=3D"group" class=3D"relative flex w-full min-w-0 flex-col p-=
2"><div data-sidebar=3D"group-label" class=3D"duration-200 flex h-8 shrink-=
0 items-center rounded-md px-2 text-xs font-medium outline-none ring-sideba=
r-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&amp;&gt;s=
vg]:size-4 [&amp;&gt;svg]:shrink-0 group-data-[collapsible=3Dicon]:-mt-8 gr=
oup-data-[collapsible=3Dicon]:opacity-0 text-sidebar-foreground/70">=E6=88=
=91=E7=9A=84=E8=82=A1=E7=A5=A8</div><div data-sidebar=3D"group-content" cla=
ss=3D"w-full text-sm"><ul data-sidebar=3D"menu" class=3D"flex w-full min-w-=
0 flex-col gap-1"><li data-sidebar=3D"menu-item" class=3D"group/menu-item r=
elative"><a data-sidebar=3D"menu-button" data-size=3D"default" data-active=
=3D"false" class=3D"peer/menu-button flex w-full items-center gap-2 overflo=
w-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transition=
-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent activ=
e:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opac=
ity-50 group-has-[[data-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disable=
d:pointer-events-none aria-disabled:opacity-50 data-[active=3Dtrue]:bg-side=
bar-accent data-[active=3Dtrue]:font-medium data-[active=3Dtrue]:text-sideb=
ar-accent-foreground data-[state=3Dopen]:hover:bg-sidebar-accent data-[stat=
e=3Dopen]:hover:text-sidebar-accent-foreground group-data-[collapsible=3Dic=
on]:!size-8 group-data-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]=
:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-ac=
cent hover:text-sidebar-accent-foreground h-8 text-sm ({isActive:l})=3D&gt;=
a(l)" href=3D"https://idea-pilot-insight.lovable.app/stock/AAPL"><svg xmlns=
=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 2=
4 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linec=
ap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-trending-up h=
-4 w-4"><polyline points=3D"22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyl=
ine points=3D"16 7 22 7 22 13"></polyline></svg><div class=3D"flex-1 flex j=
ustify-between items-center"><div><div class=3D"font-medium">AAPL</div><div=
 class=3D"text-xs text-sidebar-foreground/60">=E8=8B=B9=E6=9E=9C</div></div=
><span class=3D"text-xs text-success">+0.6%</span></div></a></li><li data-s=
idebar=3D"menu-item" class=3D"group/menu-item relative"><a data-sidebar=3D"=
menu-button" data-size=3D"default" data-active=3D"false" class=3D"peer/menu=
-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-=
left outline-none ring-sidebar-ring transition-[width,height,padding] focus=
-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foregro=
und disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sideb=
ar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-di=
sabled:opacity-50 data-[active=3Dtrue]:bg-sidebar-accent data-[active=3Dtru=
e]:font-medium data-[active=3Dtrue]:text-sidebar-accent-foreground data-[st=
ate=3Dopen]:hover:bg-sidebar-accent data-[state=3Dopen]:hover:text-sidebar-=
accent-foreground group-data-[collapsible=3Dicon]:!size-8 group-data-[colla=
psible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size=
-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accen=
t-foreground h-8 text-sm ({isActive:l})=3D&gt;a(l)" href=3D"https://idea-pi=
lot-insight.lovable.app/stock/AMD"><svg xmlns=3D"http://www.w3.org/2000/svg=
" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"=
currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=
=3D"round" class=3D"lucide lucide-trending-up h-4 w-4"><polyline points=3D"=
22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points=3D"16 7 22 7 22 1=
3"></polyline></svg><div class=3D"flex-1 flex justify-between items-center"=
><div><div class=3D"font-medium">AMD</div><div class=3D"text-xs text-sideba=
r-foreground/60">AMD</div></div><span class=3D"text-xs text-success">+1.8%<=
/span></div></a></li><li data-sidebar=3D"menu-item" class=3D"group/menu-ite=
m relative"><a data-sidebar=3D"menu-button" data-size=3D"default" data-acti=
ve=3D"false" class=3D"peer/menu-button flex w-full items-center gap-2 overf=
low-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transiti=
on-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent act=
ive:text-sidebar-accent-foreground disabled:pointer-events-none disabled:op=
acity-50 group-has-[[data-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disab=
led:pointer-events-none aria-disabled:opacity-50 data-[active=3Dtrue]:bg-si=
debar-accent data-[active=3Dtrue]:font-medium data-[active=3Dtrue]:text-sid=
ebar-accent-foreground data-[state=3Dopen]:hover:bg-sidebar-accent data-[st=
ate=3Dopen]:hover:text-sidebar-accent-foreground group-data-[collapsible=3D=
icon]:!size-8 group-data-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-chil=
d]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-=
accent hover:text-sidebar-accent-foreground h-8 text-sm ({isActive:l})=3D&g=
t;a(l)" href=3D"https://idea-pilot-insight.lovable.app/stock/AMZN"><svg xml=
ns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0=
 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-lin=
ecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-trending-up=
 h-4 w-4"><polyline points=3D"22 7 13.5 15.5 8.5 10.5 2 17"></polyline><pol=
yline points=3D"16 7 22 7 22 13"></polyline></svg><div class=3D"flex-1 flex=
 justify-between items-center"><div><div class=3D"font-medium">AMZN</div><d=
iv class=3D"text-xs text-sidebar-foreground/60">=E4=BA=9A=E9=A9=AC=E9=80=8A=
</div></div><span class=3D"text-xs text-success">+0.8%</span></div></a></li=
><li data-sidebar=3D"menu-item" class=3D"group/menu-item relative"><a data-=
sidebar=3D"menu-button" data-size=3D"default" data-active=3D"false" class=
=3D"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded=
-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,p=
adding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-a=
ccent-foreground disabled:pointer-events-none disabled:opacity-50 group-has=
-[[data-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:pointer-events=
-none aria-disabled:opacity-50 data-[active=3Dtrue]:bg-sidebar-accent data-=
[active=3Dtrue]:font-medium data-[active=3Dtrue]:text-sidebar-accent-foregr=
ound data-[state=3Dopen]:hover:bg-sidebar-accent data-[state=3Dopen]:hover:=
text-sidebar-accent-foreground group-data-[collapsible=3Dicon]:!size-8 grou=
p-data-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;=
&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-=
sidebar-accent-foreground h-8 text-sm ({isActive:l})=3D&gt;a(l)" href=3D"ht=
tps://idea-pilot-insight.lovable.app/stock/CRM"><svg xmlns=3D"http://www.w3=
.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"non=
e" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stro=
ke-linejoin=3D"round" class=3D"lucide lucide-trending-up h-4 w-4"><polyline=
 points=3D"22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points=3D"16 =
7 22 7 22 13"></polyline></svg><div class=3D"flex-1 flex justify-between it=
ems-center"><div><div class=3D"font-medium">CRM</div><div class=3D"text-xs =
text-sidebar-foreground/60">Salesforce</div></div><span class=3D"text-xs te=
xt-destructive">-0.5%</span></div></a></li><li data-sidebar=3D"menu-item" c=
lass=3D"group/menu-item relative"><a data-sidebar=3D"menu-button" data-size=
=3D"default" data-active=3D"false" class=3D"peer/menu-button flex w-full it=
ems-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none ring=
-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active=
:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-e=
vents-none disabled:opacity-50 group-has-[[data-sidebar=3Dmenu-action]]/men=
u-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data=
-[active=3Dtrue]:bg-sidebar-accent data-[active=3Dtrue]:font-medium data-[a=
ctive=3Dtrue]:text-sidebar-accent-foreground data-[state=3Dopen]:hover:bg-s=
idebar-accent data-[state=3Dopen]:hover:text-sidebar-accent-foreground grou=
p-data-[collapsible=3Dicon]:!size-8 group-data-[collapsible=3Dicon]:!p-2 [&=
amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shri=
nk-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground h-8 text-=
sm ({isActive:l})=3D&gt;a(l)" href=3D"https://idea-pilot-insight.lovable.ap=
p/stock/MSFT"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=
=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-=
width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"luc=
ide lucide-trending-up h-4 w-4"><polyline points=3D"22 7 13.5 15.5 8.5 10.5=
 2 17"></polyline><polyline points=3D"16 7 22 7 22 13"></polyline></svg><di=
v class=3D"flex-1 flex justify-between items-center"><div><div class=3D"fon=
t-medium">MSFT</div><div class=3D"text-xs text-sidebar-foreground/60">=E5=
=BE=AE=E8=BD=AF</div></div><span class=3D"text-xs text-success">+1.2%</span=
></div></a></li><li data-sidebar=3D"menu-item" class=3D"group/menu-item rel=
ative"><a data-sidebar=3D"menu-button" data-size=3D"default" data-active=3D=
"false" class=3D"peer/menu-button flex w-full items-center gap-2 overflow-h=
idden rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[w=
idth,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:t=
ext-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity=
-50 group-has-[[data-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:p=
ointer-events-none aria-disabled:opacity-50 data-[active=3Dtrue]:bg-sidebar=
-accent data-[active=3Dtrue]:font-medium data-[active=3Dtrue]:text-sidebar-=
accent-foreground data-[state=3Dopen]:hover:bg-sidebar-accent data-[state=
=3Dopen]:hover:text-sidebar-accent-foreground group-data-[collapsible=3Dico=
n]:!size-8 group-data-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:=
truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-acc=
ent hover:text-sidebar-accent-foreground h-8 text-sm ({isActive:l})=3D&gt;a=
(l)" href=3D"https://idea-pilot-insight.lovable.app/stock/NVDA"><svg xmlns=
=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 2=
4 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linec=
ap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-trending-up h=
-4 w-4"><polyline points=3D"22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyl=
ine points=3D"16 7 22 7 22 13"></polyline></svg><div class=3D"flex-1 flex j=
ustify-between items-center"><div><div class=3D"font-medium">NVDA</div><div=
 class=3D"text-xs text-sidebar-foreground/60">=E8=8B=B1=E4=BC=9F=E8=BE=BE</=
div></div><span class=3D"text-xs text-success">****%</span></div></a></li><=
li data-sidebar=3D"menu-item" class=3D"group/menu-item relative"><a data-si=
debar=3D"menu-button" data-size=3D"default" data-active=3D"false" class=3D"=
peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md =
p-2 text-left outline-none ring-sidebar-ring transition-[width,height,paddi=
ng] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accen=
t-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[d=
ata-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:pointer-events-non=
e aria-disabled:opacity-50 data-[active=3Dtrue]:bg-sidebar-accent data-[act=
ive=3Dtrue]:font-medium data-[active=3Dtrue]:text-sidebar-accent-foreground=
 data-[state=3Dopen]:hover:bg-sidebar-accent data-[state=3Dopen]:hover:text=
-sidebar-accent-foreground group-data-[collapsible=3Dicon]:!size-8 group-da=
ta-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;=
svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-side=
bar-accent-foreground h-8 text-sm ({isActive:l})=3D&gt;a(l)" href=3D"https:=
//idea-pilot-insight.lovable.app/stock/ORCL"><svg xmlns=3D"http://www.w3.or=
g/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" =
stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-=
linejoin=3D"round" class=3D"lucide lucide-trending-up h-4 w-4"><polyline po=
ints=3D"22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points=3D"16 7 2=
2 7 22 13"></polyline></svg><div class=3D"flex-1 flex justify-between items=
-center"><div><div class=3D"font-medium">ORCL</div><div class=3D"text-xs te=
xt-sidebar-foreground/60">=E7=94=B2=E9=AA=A8=E6=96=87</div></div><span clas=
s=3D"text-xs text-success">+0.3%</span></div></a></li><li data-sidebar=3D"m=
enu-item" class=3D"group/menu-item relative"><a data-sidebar=3D"menu-button=
" data-size=3D"default" data-active=3D"false" class=3D"peer/menu-button fle=
x w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outlin=
e-none ring-sidebar-ring transition-[width,height,padding] focus-visible:ri=
ng-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disable=
d:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=3Dmenu-a=
ction]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opac=
ity-50 data-[active=3Dtrue]:bg-sidebar-accent data-[active=3Dtrue]:font-med=
ium data-[active=3Dtrue]:text-sidebar-accent-foreground data-[state=3Dopen]=
:hover:bg-sidebar-accent data-[state=3Dopen]:hover:text-sidebar-accent-fore=
ground group-data-[collapsible=3Dicon]:!size-8 group-data-[collapsible=3Dic=
on]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&g=
t;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foregroun=
d h-8 text-sm ({isActive:l})=3D&gt;a(l)" href=3D"https://idea-pilot-insight=
.lovable.app/stock/QCOM"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D=
"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentCol=
or" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" c=
lass=3D"lucide lucide-trending-up h-4 w-4"><polyline points=3D"22 7 13.5 15=
.5 8.5 10.5 2 17"></polyline><polyline points=3D"16 7 22 7 22 13"></polylin=
e></svg><div class=3D"flex-1 flex justify-between items-center"><div><div c=
lass=3D"font-medium">QCOM</div><div class=3D"text-xs text-sidebar-foregroun=
d/60">=E9=AB=98=E9=80=9A</div></div><span class=3D"text-xs text-success">+2=
.1%</span></div></a></li><li data-sidebar=3D"menu-item" class=3D"group/menu=
-item relative"><a data-sidebar=3D"menu-button" data-size=3D"default" data-=
active=3D"false" class=3D"peer/menu-button flex w-full items-center gap-2 o=
verflow-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring tran=
sition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent=
 active:text-sidebar-accent-foreground disabled:pointer-events-none disable=
d:opacity-50 group-has-[[data-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-d=
isabled:pointer-events-none aria-disabled:opacity-50 data-[active=3Dtrue]:b=
g-sidebar-accent data-[active=3Dtrue]:font-medium data-[active=3Dtrue]:text=
-sidebar-accent-foreground data-[state=3Dopen]:hover:bg-sidebar-accent data=
-[state=3Dopen]:hover:text-sidebar-accent-foreground group-data-[collapsibl=
e=3Dicon]:!size-8 group-data-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-=
child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-side=
bar-accent hover:text-sidebar-accent-foreground h-8 text-sm ({isActive:l})=
=3D&gt;a(l)" href=3D"https://idea-pilot-insight.lovable.app/stock/TSLA"><sv=
g xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=
=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" str=
oke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-tren=
ding-up h-4 w-4"><polyline points=3D"22 7 13.5 15.5 8.5 10.5 2 17"></polyli=
ne><polyline points=3D"16 7 22 7 22 13"></polyline></svg><div class=3D"flex=
-1 flex justify-between items-center"><div><div class=3D"font-medium">TSLA<=
/div><div class=3D"text-xs text-sidebar-foreground/60">=E7=89=B9=E6=96=AF=
=E6=8B=89</div></div><span class=3D"text-xs text-destructive">-1.1%</span><=
/div></a></li><li data-sidebar=3D"menu-item" class=3D"group/menu-item relat=
ive"><a data-sidebar=3D"menu-button" data-size=3D"default" data-active=3D"f=
alse" class=3D"peer/menu-button flex w-full items-center gap-2 overflow-hid=
den rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[wid=
th,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:tex=
t-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-5=
0 group-has-[[data-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:poi=
nter-events-none aria-disabled:opacity-50 data-[active=3Dtrue]:bg-sidebar-a=
ccent data-[active=3Dtrue]:font-medium data-[active=3Dtrue]:text-sidebar-ac=
cent-foreground data-[state=3Dopen]:hover:bg-sidebar-accent data-[state=3Do=
pen]:hover:text-sidebar-accent-foreground group-data-[collapsible=3Dicon]:!=
size-8 group-data-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:trun=
cate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent =
hover:text-sidebar-accent-foreground h-8 text-sm ({isActive:l})=3D&gt;a(l)"=
 href=3D"https://idea-pilot-insight.lovable.app/stock/TSM"><svg xmlns=3D"ht=
tp://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" =
fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"=
round" stroke-linejoin=3D"round" class=3D"lucide lucide-trending-up h-4 w-4=
"><polyline points=3D"22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline po=
ints=3D"16 7 22 7 22 13"></polyline></svg><div class=3D"flex-1 flex justify=
-between items-center"><div><div class=3D"font-medium">TSM</div><div class=
=3D"text-xs text-sidebar-foreground/60">=E5=8F=B0=E7=A7=AF=E7=94=B5</div></=
div><span class=3D"text-xs text-success">+1.5%</span></div></a></li></ul></=
div></div><div data-sidebar=3D"group" class=3D"relative flex w-full min-w-0=
 flex-col p-2"><div data-sidebar=3D"group-label" class=3D"duration-200 flex=
 h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-none=
 ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2=
 [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 group-data-[collapsible=3Dic=
on]:-mt-8 group-data-[collapsible=3Dicon]:opacity-0 text-sidebar-foreground=
/70">=E6=A0=87=E7=AD=BE</div><div data-sidebar=3D"group-content" class=3D"w=
-full text-sm"><ul data-sidebar=3D"menu" class=3D"flex w-full min-w-0 flex-=
col gap-1"><li data-sidebar=3D"menu-item" class=3D"group/menu-item relative=
"><a data-sidebar=3D"menu-button" data-size=3D"default" data-active=3D"fals=
e" class=3D"peer/menu-button flex w-full items-center gap-2 overflow-hidden=
 rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,=
height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-s=
idebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 g=
roup-has-[[data-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:pointe=
r-events-none aria-disabled:opacity-50 data-[active=3Dtrue]:bg-sidebar-acce=
nt data-[active=3Dtrue]:font-medium data-[active=3Dtrue]:text-sidebar-accen=
t-foreground data-[state=3Dopen]:hover:bg-sidebar-accent data-[state=3Dopen=
]:hover:text-sidebar-accent-foreground group-data-[collapsible=3Dicon]:!siz=
e-8 group-data-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:truncat=
e [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hov=
er:text-sidebar-accent-foreground h-8 text-sm ({isActive:i})=3D&gt;a(i)" hr=
ef=3D"https://idea-pilot-insight.lovable.app/favorites"><svg xmlns=3D"http:=
//www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fil=
l=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"rou=
nd" stroke-linejoin=3D"round" class=3D"lucide lucide-star h-4 w-4"><path d=
=3D"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.1=
6l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878=
l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.39=
6 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9=
.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z"></pat=
h></svg><span>=E6=94=B6=E8=97=8F</span></a></li><li data-sidebar=3D"menu-it=
em" class=3D"group/menu-item relative"><a data-sidebar=3D"menu-button" data=
-size=3D"default" data-active=3D"false" class=3D"peer/menu-button flex w-fu=
ll items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none=
 ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 a=
ctive:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:poin=
ter-events-none disabled:opacity-50 group-has-[[data-sidebar=3Dmenu-action]=
]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50=
 data-[active=3Dtrue]:bg-sidebar-accent data-[active=3Dtrue]:font-medium da=
ta-[active=3Dtrue]:text-sidebar-accent-foreground data-[state=3Dopen]:hover=
:bg-sidebar-accent data-[state=3Dopen]:hover:text-sidebar-accent-foreground=
 group-data-[collapsible=3Dicon]:!size-8 group-data-[collapsible=3Dicon]:!p=
-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]=
:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground h-8 =
text-sm ({isActive:i})=3D&gt;a(i)" href=3D"https://idea-pilot-insight.lovab=
le.app/archive"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" heig=
ht=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" strok=
e-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"l=
ucide lucide-archive h-4 w-4"><rect width=3D"20" height=3D"5" x=3D"2" y=3D"=
3" rx=3D"1"></rect><path d=3D"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"></p=
ath><path d=3D"M10 12h4"></path></svg><span>=E5=BD=92=E6=A1=A3</span></a></=
li><li data-sidebar=3D"menu-item" class=3D"group/menu-item relative"><a dat=
a-sidebar=3D"menu-button" data-size=3D"default" data-active=3D"false" class=
=3D"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded=
-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,p=
adding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-a=
ccent-foreground disabled:pointer-events-none disabled:opacity-50 group-has=
-[[data-sidebar=3Dmenu-action]]/menu-item:pr-8 aria-disabled:pointer-events=
-none aria-disabled:opacity-50 data-[active=3Dtrue]:bg-sidebar-accent data-=
[active=3Dtrue]:font-medium data-[active=3Dtrue]:text-sidebar-accent-foregr=
ound data-[state=3Dopen]:hover:bg-sidebar-accent data-[state=3Dopen]:hover:=
text-sidebar-accent-foreground group-data-[collapsible=3Dicon]:!size-8 grou=
p-data-[collapsible=3Dicon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;=
&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-=
sidebar-accent-foreground h-8 text-sm ({isActive:i})=3D&gt;a(i)" href=3D"ht=
tps://idea-pilot-insight.lovable.app/shared"><svg xmlns=3D"http://www.w3.or=
g/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" =
stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-=
linejoin=3D"round" class=3D"lucide lucide-share2 h-4 w-4"><circle cx=3D"18"=
 cy=3D"5" r=3D"3"></circle><circle cx=3D"6" cy=3D"12" r=3D"3"></circle><cir=
cle cx=3D"18" cy=3D"19" r=3D"3"></circle><line x1=3D"8.59" x2=3D"15.42" y1=
=3D"13.51" y2=3D"17.49"></line><line x1=3D"15.41" x2=3D"8.59" y1=3D"6.51" y=
2=3D"10.49"></line></svg><span>=E5=88=86=E4=BA=AB</span></a></li></ul></div=
></div></div></div></div></div><main class=3D"flex-1 p-6"><div class=3D"spa=
ce-y-8"><div class=3D"space-y-6"><div class=3D"flex items-center justify-be=
tween"><h2 class=3D"text-2xl font-bold">=E6=88=91=E7=9A=84 Ideas</h2><div c=
lass=3D"text-sm text-muted-foreground">=E5=85=B1 6 =E4=B8=AA=E6=83=B3=E6=B3=
=95=E8=BF=BD=E8=B8=AA=E4=B8=AD</div></div><div class=3D"grid gap-6 lg:grid-=
cols-2"><div class=3D"rounded-lg text-card-foreground shadow-sm cursor-poin=
ter bg-gradient-card hover:shadow-elevated transition-all duration-300 hove=
r:scale-[1.02] group border"><div class=3D"flex h-full"><div class=3D"flex-=
1 p-6 flex flex-col gap-4"><div class=3D"flex items-start gap-3"><svg xmlns=
=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 2=
4 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linec=
ap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-brain h-5 w-5=
 text-primary flex-shrink-0 mt-1"><path d=3D"M12 5a3 3 0 1 0-5.997.125 4 4 =
0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d=3D"M=
12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 =
1 12 18Z"></path><path d=3D"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></p=
ath><path d=3D"M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d=3D"M6.003 5=
.125A3 3 0 0 0 6.401 6.5"></path><path d=3D"M3.477 10.896a4 4 0 0 1 .585-.3=
96"></path><path d=3D"M19.938 10.5a4 4 0 0 1 .585.396"></path><path d=3D"M6=
 18a4 4 0 0 1-1.967-.516"></path><path d=3D"M19.967 17.484A4 4 0 0 1 18 18"=
></path></svg><div class=3D"flex-1 min-w-0"><h3 class=3D"font-bold text-bas=
e leading-tight line-clamp-2 group-hover:text-primary transition-colors mb-=
2">NVIDIA H200=E8=B6=85=E7=BA=A7=E8=8A=AF=E7=89=87=E9=87=8F=E4=BA=A7=EF=BC=
=8CAI=E8=AE=AD=E7=BB=83=E6=95=88=E7=8E=87=E9=9D=A9=E5=91=BD=E6=80=A7=E6=8F=
=90=E5=8D=87</h3><div class=3D"flex items-center justify-between gap-2"><di=
v class=3D"flex items-center gap-2"><div class=3D"inline-flex items-center =
rounded-full border font-semibold transition-colors focus:outline-none focu=
s:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground text-xs px-2 p=
y-1">AI=E8=8A=AF=E7=89=87</div></div><div class=3D"flex items-center gap-1.=
5"><div class=3D"relative"><svg xmlns=3D"http://www.w3.org/2000/svg" width=
=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"lucide lucide-chart-column h-3.5 w-3.5 text-primary animate-puls=
e"><path d=3D"M3 3v16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 17V9"></path>=
<path d=3D"M13 17V5"></path><path d=3D"M8 17v-3"></path></svg><div class=3D=
"absolute -top-1 -right-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pul=
se"></div></div><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" heig=
ht=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" strok=
e-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"l=
ucide lucide-mail h-3.5 w-3.5 text-muted-foreground hover:text-primary tran=
sition-colors cursor-pointer"><rect width=3D"20" height=3D"16" x=3D"2" y=3D=
"4" rx=3D"2"></rect><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"><=
/path></svg><div class=3D"flex items-center gap-1 cursor-pointer hover:text=
-primary transition-colors"><svg xmlns=3D"http://www.w3.org/2000/svg" width=
=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"lucide lucide-message-circle h-3.5 w-3.5 text-muted-foreground">=
<path d=3D"M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span class=3D"text=
-xs text-muted-foreground">50</span></div></div></div></div></div><div clas=
s=3D"p-3 bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200/5=
0 rounded-none"><div class=3D"flex items-start gap-2"><svg xmlns=3D"http://=
www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=
=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"roun=
d" stroke-linejoin=3D"round" class=3D"lucide lucide-sparkles h-4 w-4 text-p=
rimary mt-0.5 flex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6=
.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5=
 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964=
L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>=
<path d=3D"M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2">=
</path><path d=3D"M5 18H3"></path></svg><div class=3D"flex-1"><ul class=3D"=
space-y-2 text-sm text-foreground"><li class=3D"flex items-start justify-be=
tween gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 b=
g-primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp=
-1">=E3=80=90=E5=88=A9=E5=A5=BD=E3=80=91=E8=8A=AF=E7=89=87=E6=80=A7=E8=83=
=BD=E5=A4=A7=E5=B9=85=E6=8F=90=E5=8D=87=EF=BC=8C=E4=B8=8B=E6=B8=B8=E5=BA=94=
=E7=94=A8=E9=9C=80=E6=B1=82=E6=BF=80=E5=A2=9E=EF=BC=8C=E4=BA=A7=E4=B8=9A=E9=
=93=BE=E9=BE=99=E5=A4=B4=E5=8F=97=E7=9B=8A=E6=98=8E=E6=98=BE</span></div><s=
pan class=3D"text-xs text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=
=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-between gap-2=
"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary r=
ounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=
=90=E8=B6=8B=E5=8A=BF=E3=80=91AI=E7=AE=97=E5=8A=9B=E9=9C=80=E6=B1=82=E6=8C=
=81=E7=BB=AD=E7=88=86=E5=8F=91=EF=BC=8C=E9=AB=98=E7=AB=AF=E8=8A=AF=E7=89=87=
=E4=BE=9B=E4=B8=8D=E5=BA=94=E6=B1=82=EF=BC=8C=E8=AE=AE=E4=BB=B7=E8=83=BD=E5=
=8A=9B=E5=A2=9E=E5=BC=BA</span></div><span class=3D"text-xs text-muted-fore=
ground flex-shrink-0">1=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"=
flex items-start justify-between gap-2"><div class=3D"flex items-start gap-=
2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></spa=
n><span class=3D"line-clamp-1">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E5=9C=
=B0=E7=BC=98=E6=94=BF=E6=B2=BB=E5=BD=B1=E5=93=8D=E4=BE=9B=E5=BA=94=E9=93=BE=
=E7=A8=B3=E5=AE=9A=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8=E5=9B=BD=E9=99=85=E8=
=B4=B8=E6=98=93=E6=94=BF=E7=AD=96=E5=8F=98=E5=8C=96</span></div><span class=
=3D"text-xs text-muted-foreground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=
=8D</span></li></ul></div></div></div><div class=3D"space-y-2"><div class=
=3D"flex items-start gap-2 px-[15px]"><svg xmlns=3D"http://www.w3.org/2000/=
svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=
=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejo=
in=3D"round" class=3D"lucide lucide-message-square h-4 w-4 text-muted-foreg=
round mt-0.5 flex-shrink-0"><path d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 =
0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg><div class=3D"flex-1"><ul class=3D"=
space-y-2 text-sm text-foreground"><li class=3D"flex items-start justify-be=
tween gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 b=
g-muted-foreground rounded-full mt-2 flex-shrink-0"></span><span class=3D"l=
ine-clamp-1 flex-1">=E5=BE=AE=E8=BD=AFAzure=E7=8E=87=E5=85=88=E9=83=A8=E7=
=BD=B2H200=E9=9B=86=E7=BE=A4=EF=BC=8C=E4=B8=8EOpenAI=E6=B7=B1=E5=BA=A6=E5=
=90=88=E4=BD=9C=E8=AE=AD=E7=BB=83GPT-5</span></div><span class=3D"text-xs t=
ext-muted-foreground flex-shrink-0">1=E5=B0=8F=E6=97=B6=E5=89=8D</span></li=
><li class=3D"flex items-start justify-between gap-2"><div class=3D"flex it=
ems-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-full mt=
-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">AWS=E5=AE=A3=
=E5=B8=83=E6=98=8E=E5=B9=B4Q1=E5=BC=80=E5=A7=8B=E6=8F=90=E4=BE=9BH200=E5=AE=
=9E=E4=BE=8B=E6=9C=8D=E5=8A=A1=EF=BC=8C=E9=A2=84=E8=AE=A2=E9=87=8F=E5=B7=B2=
=E8=B6=8580%</span></div><span class=3D"text-xs text-muted-foreground flex-=
shrink-0">3=E5=B0=8F=E6=97=B6=E5=89=8D</span></li></ul></div></div></div></=
div><div class=3D"w-32 border-l bg-muted/20 p-3 flex flex-col"><div class=
=3D"text-xs text-muted-foreground mb-2 font-medium">Following</div><div cla=
ss=3D"space-y-1.5 flex-1"><button class=3D"group relative w-full p-2 bg-car=
d border border-border rounded-lg hover:border-primary/30 hover:bg-accent/5=
0 transition-all duration-200 text-left"><div class=3D"absolute -top-1 -rig=
ht-1 w-2 h-2 bg-destructive rounded-full"></div><div class=3D"flex justify-=
between items-center"><div class=3D"font-sans text-xs font-medium text-fore=
ground group-hover:text-primary transition-colors">NVDA</div><div class=3D"=
text-xs font-medium text-success">****%</div></div></button><button class=
=3D"group relative w-full p-2 bg-card border border-border rounded-lg hover=
:border-primary/30 hover:bg-accent/50 transition-all duration-200 text-left=
"><div class=3D"flex justify-between items-center"><div class=3D"font-sans =
text-xs font-medium text-foreground group-hover:text-primary transition-col=
ors">TSM</div><div class=3D"text-xs font-medium text-destructive">-1.2%</di=
v></div></button><button class=3D"group relative w-full p-2 bg-card border =
border-border rounded-lg hover:border-primary/30 hover:bg-accent/50 transit=
ion-all duration-200 text-left"><div class=3D"absolute -top-1 -right-1 w-2 =
h-2 bg-destructive rounded-full"></div><div class=3D"flex justify-between i=
tems-center"><div class=3D"font-sans text-xs font-medium text-foreground gr=
oup-hover:text-primary transition-colors">AMZN</div><div class=3D"text-xs f=
ont-medium text-success">+0.8%</div></div></button><button class=3D"group r=
elative w-full p-2 bg-card border border-border rounded-lg hover:border-pri=
mary/30 hover:bg-accent/50 transition-all duration-200 text-left"><div clas=
s=3D"flex justify-between items-center"><div class=3D"font-sans text-xs fon=
t-medium text-foreground group-hover:text-primary transition-colors">GOOGL<=
/div><div class=3D"text-xs font-medium text-success">+4.1%</div></div></but=
ton><button class=3D"group relative w-full p-2 bg-card border border-border=
 rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all durat=
ion-200 text-left"><div class=3D"flex justify-between items-center"><div cl=
ass=3D"font-sans text-xs font-medium text-foreground group-hover:text-prima=
ry transition-colors">MSFT</div><div class=3D"text-xs font-medium text-dest=
ructive">-0.5%</div></div></button></div><button class=3D"mt-2 w-full py-1.=
5 text-xs text-muted-foreground hover:text-primary transition-colors border=
-t border-border/50 pt-2">more</button></div></div></div><div class=3D"roun=
ded-lg text-card-foreground shadow-sm cursor-pointer bg-gradient-card hover=
:shadow-elevated transition-all duration-300 hover:scale-[1.02] group borde=
r"><div class=3D"flex h-full"><div class=3D"flex-1 p-6 flex flex-col gap-4"=
><div class=3D"flex items-start gap-3"><svg xmlns=3D"http://www.w3.org/2000=
/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=
=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejo=
in=3D"round" class=3D"lucide lucide-brain h-5 w-5 text-primary flex-shrink-=
0 mt-1"><path d=3D"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0=
 .556 6.588A4 4 0 1 0 12 18Z"></path><path d=3D"M12 5a3 3 0 1 1 5.997.125 4=
 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d=
=3D"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d=3D"M17.599 6=
.5a3 3 0 0 0 .399-1.375"></path><path d=3D"M6.003 5.125A3 3 0 0 0 6.401 6.5=
"></path><path d=3D"M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d=3D"M1=
9.938 10.5a4 4 0 0 1 .585.396"></path><path d=3D"M6 18a4 4 0 0 1-1.967-.516=
"></path><path d=3D"M19.967 17.484A4 4 0 0 1 18 18"></path></svg><div class=
=3D"flex-1 min-w-0"><h3 class=3D"font-bold text-base leading-tight line-cla=
mp-2 group-hover:text-primary transition-colors mb-2">=E5=BE=AE=E8=BD=AFCop=
ilot=E5=85=A8=E9=9D=A2=E9=9B=86=E6=88=90Office365=EF=BC=8C=E4=BC=81=E4=B8=
=9AAI=E5=8A=9E=E5=85=AC=E6=97=B6=E4=BB=A3=E5=88=B0=E6=9D=A5</h3><div class=
=3D"flex items-center justify-between gap-2"><div class=3D"flex items-cente=
r gap-2"><div class=3D"inline-flex items-center rounded-full border font-se=
mibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring fo=
cus:ring-offset-2 text-foreground text-xs px-2 py-1">=E4=BC=81=E4=B8=9A=E8=
=BD=AF=E4=BB=B6</div></div><div class=3D"flex items-center gap-1.5"><div cl=
ass=3D"relative"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" hei=
ght=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stro=
ke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"=
lucide lucide-chart-column h-3.5 w-3.5 text-primary animate-pulse"><path d=
=3D"M3 3v16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 17V9"></path><path d=3D=
"M13 17V5"></path><path d=3D"M8 17v-3"></path></svg><div class=3D"absolute =
-top-1 -right-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>=
</div><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" =
viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D=
"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide luci=
de-mail h-3.5 w-3.5 text-muted-foreground hover:text-primary transition-col=
ors cursor-pointer"><rect width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"=
2"></rect><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></sv=
g><div class=3D"flex items-center gap-1 cursor-pointer hover:text-primary t=
ransition-colors"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" he=
ight=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" str=
oke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D=
"lucide lucide-message-circle h-3.5 w-3.5 text-muted-foreground"><path d=3D=
"M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span class=3D"text-xs text-m=
uted-foreground">44</span></div></div></div></div></div><div class=3D"p-3 b=
g-gradient-to-r from-gray-50 to-gray-100 border border-gray-200/50 rounded-=
none"><div class=3D"flex items-start gap-2"><svg xmlns=3D"http://www.w3.org=
/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" s=
troke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-l=
inejoin=3D"round" class=3D"lucide lucide-sparkles h-4 w-4 text-primary mt-0=
.5 flex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a=
.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963=
 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.06=
3a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"=
M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><pat=
h d=3D"M5 18H3"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 t=
ext-sm text-foreground"><li class=3D"flex items-start justify-between gap-2=
"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary r=
ounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=
=90=E5=88=A9=E5=A5=BD=E3=80=91=E4=BC=81=E4=B8=9A=E6=95=B0=E5=AD=97=E5=8C=96=
=E8=BD=AC=E5=9E=8B=E5=8A=A0=E9=80=9F=EF=BC=8CAI=E5=BA=94=E7=94=A8=E6=B8=97=
=E9=80=8F=E7=8E=87=E5=BF=AB=E9=80=9F=E6=8F=90=E5=8D=87=EF=BC=8C=E5=95=86=E4=
=B8=9A=E5=8C=96=E8=BF=9B=E7=A8=8B=E6=98=8E=E7=A1=AE</span></div><span class=
=3D"text-xs text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=
=8D</span></li><li class=3D"flex items-start justify-between gap-2"><div cl=
ass=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-fu=
ll mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E8=B6=
=8B=E5=8A=BF=E3=80=91AI Agent=E6=88=90=E4=B8=BA=E6=96=B0=E5=A2=9E=E9=95=BF=
=E7=82=B9=EF=BC=8CB=E7=AB=AF=E5=B8=82=E5=9C=BA=E7=88=86=E5=8F=91=EF=BC=8C=
=E8=AE=A2=E9=98=85=E6=A8=A1=E5=BC=8F=E9=AA=8C=E8=AF=81=E6=88=90=E5=8A=9F</s=
pan></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=
=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-b=
etween gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 =
bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clam=
p-1">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E7=9B=91=E7=AE=A1=E6=94=BF=E7=AD=
=96=E8=B6=8B=E4=B8=A5=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8AI=E5=AE=89=E5=85=
=A8=E5=92=8C=E6=95=B0=E6=8D=AE=E9=9A=90=E7=A7=81=E7=9B=B8=E5=85=B3=E6=B3=95=
=E8=A7=84=E5=BD=B1=E5=93=8D</span></div><span class=3D"text-xs text-muted-f=
oreground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span></li></ul></di=
v></div></div><div class=3D"space-y-2"><div class=3D"flex items-start gap-2=
 px-[15px]"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=
=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-=
width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"luc=
ide lucide-message-square h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-=
0"><path d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z=
"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-fo=
reground"><li class=3D"flex items-start justify-between gap-2"><div class=
=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground roun=
ded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">Cop=
ilot Pro=E4=BC=81=E4=B8=9A=E7=89=88=E7=94=A8=E6=88=B7=E5=A2=9E=E9=95=BF=E7=
=8E=87=E8=BE=BE=E5=88=B0150%=EF=BC=8C=E6=9C=88=E6=B4=BB=E8=B7=83=E7=94=A8=
=E6=88=B7=E7=AA=81=E7=A0=B41200=E4=B8=87</span></div><span class=3D"text-xs=
 text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D</span></=
li><li class=3D"flex items-start justify-between gap-2"><div class=3D"flex =
items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-full =
mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">=E8=B4=A2=E5=
=AF=8C500=E5=BC=BA=E4=B8=AD=E5=B7=B2=E6=9C=8978%=E4=BC=81=E4=B8=9A=E9=83=A8=
=E7=BD=B2Copilot=EF=BC=8C=E7=94=9F=E4=BA=A7=E5=8A=9B=E6=8F=90=E5=8D=8745%</=
span></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">4=E5=
=B0=8F=E6=97=B6=E5=89=8D</span></li></ul></div></div></div></div><div class=
=3D"w-32 border-l bg-muted/20 p-3 flex flex-col"><div class=3D"text-xs text=
-muted-foreground mb-2 font-medium">Following</div><div class=3D"space-y-1.=
5 flex-1"><button class=3D"group relative w-full p-2 bg-card border border-=
border rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all=
 duration-200 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-=
destructive rounded-full"></div><div class=3D"flex justify-between items-ce=
nter"><div class=3D"font-sans text-xs font-medium text-foreground group-hov=
er:text-primary transition-colors">MSFT</div><div class=3D"text-xs font-med=
ium text-success">****%</div></div></button><button class=3D"group relative=
 w-full p-2 bg-card border border-border rounded-lg hover:border-primary/30=
 hover:bg-accent/50 transition-all duration-200 text-left"><div class=3D"fl=
ex justify-between items-center"><div class=3D"font-sans text-xs font-mediu=
m text-foreground group-hover:text-primary transition-colors">CRM</div><div=
 class=3D"text-xs font-medium text-destructive">-1.2%</div></div></button><=
button class=3D"group relative w-full p-2 bg-card border border-border roun=
ded-lg hover:border-primary/30 hover:bg-accent/50 transition-all duration-2=
00 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive=
 rounded-full"></div><div class=3D"flex justify-between items-center"><div =
class=3D"font-sans text-xs font-medium text-foreground group-hover:text-pri=
mary transition-colors">ORCL</div><div class=3D"text-xs font-medium text-su=
ccess">+0.8%</div></div></button><button class=3D"group relative w-full p-2=
 bg-card border border-border rounded-lg hover:border-primary/30 hover:bg-a=
ccent/50 transition-all duration-200 text-left"><div class=3D"flex justify-=
between items-center"><div class=3D"font-sans text-xs font-medium text-fore=
ground group-hover:text-primary transition-colors">SNOW</div><div class=3D"=
text-xs font-medium text-success">+4.1%</div></div></button><button class=
=3D"group relative w-full p-2 bg-card border border-border rounded-lg hover=
:border-primary/30 hover:bg-accent/50 transition-all duration-200 text-left=
"><div class=3D"flex justify-between items-center"><div class=3D"font-sans =
text-xs font-medium text-foreground group-hover:text-primary transition-col=
ors">WDAY</div><div class=3D"text-xs font-medium text-destructive">-0.5%</d=
iv></div></button></div><button class=3D"mt-2 w-full py-1.5 text-xs text-mu=
ted-foreground hover:text-primary transition-colors border-t border-border/=
50 pt-2">more</button></div></div></div><div class=3D"rounded-lg text-card-=
foreground shadow-sm cursor-pointer bg-gradient-card hover:shadow-elevated =
transition-all duration-300 hover:scale-[1.02] group border"><div class=3D"=
flex h-full"><div class=3D"flex-1 p-6 flex flex-col gap-4"><div class=3D"fl=
ex items-start gap-3"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24=
" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor"=
 stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" clas=
s=3D"lucide lucide-brain h-5 w-5 text-primary flex-shrink-0 mt-1"><path d=
=3D"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4=
 0 1 0 12 18Z"></path><path d=3D"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 =
5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d=3D"M15 13a4.5 4.=
5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path d=3D"M17.599 6.5a3 3 0 0 0 .399=
-1.375"></path><path d=3D"M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d=
=3D"M3.477 10.896a4 4 0 0 1 .585-.396"></path><path d=3D"M19.938 10.5a4 4 0=
 0 1 .585.396"></path><path d=3D"M6 18a4 4 0 0 1-1.967-.516"></path><path d=
=3D"M19.967 17.484A4 4 0 0 1 18 18"></path></svg><div class=3D"flex-1 min-w=
-0"><h3 class=3D"font-bold text-base leading-tight line-clamp-2 group-hover=
:text-primary transition-colors mb-2">=E7=89=B9=E6=96=AF=E6=8B=89FSD V13=E5=
=AE=9E=E7=8E=B0L4=E7=BA=A7=E8=87=AA=E5=8A=A8=E9=A9=BE=E9=A9=B6=EF=BC=8C=E5=
=95=86=E4=B8=9A=E5=8C=96=E5=BA=94=E7=94=A8=E5=9C=A8=E5=8D=B3</h3><div class=
=3D"flex items-center justify-between gap-2"><div class=3D"flex items-cente=
r gap-2"><div class=3D"inline-flex items-center rounded-full border font-se=
mibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring fo=
cus:ring-offset-2 text-foreground text-xs px-2 py-1">=E6=99=BA=E8=83=BD=E6=
=B1=BD=E8=BD=A6</div></div><div class=3D"flex items-center gap-1.5"><div cl=
ass=3D"relative"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" hei=
ght=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stro=
ke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"=
lucide lucide-chart-column h-3.5 w-3.5 text-primary animate-pulse"><path d=
=3D"M3 3v16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 17V9"></path><path d=3D=
"M13 17V5"></path><path d=3D"M8 17v-3"></path></svg><div class=3D"absolute =
-top-1 -right-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>=
</div><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" =
viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D=
"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide luci=
de-mail h-3.5 w-3.5 text-muted-foreground hover:text-primary transition-col=
ors cursor-pointer"><rect width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"=
2"></rect><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></sv=
g><div class=3D"flex items-center gap-1 cursor-pointer hover:text-primary t=
ransition-colors"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" he=
ight=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" str=
oke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D=
"lucide lucide-message-circle h-3.5 w-3.5 text-muted-foreground"><path d=3D=
"M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span class=3D"text-xs text-m=
uted-foreground">22</span></div></div></div></div></div><div class=3D"p-3 b=
g-gradient-to-r from-gray-50 to-gray-100 border border-gray-200/50 rounded-=
none"><div class=3D"flex items-start gap-2"><svg xmlns=3D"http://www.w3.org=
/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" s=
troke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-l=
inejoin=3D"round" class=3D"lucide lucide-sparkles h-4 w-4 text-primary mt-0=
.5 flex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a=
.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963=
 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.06=
3a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"=
M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><pat=
h d=3D"M5 18H3"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 t=
ext-sm text-foreground"><li class=3D"flex items-start justify-between gap-2=
"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary r=
ounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=
=90=E5=88=A9=E5=A5=BD=E3=80=91=E6=8A=80=E6=9C=AF=E7=AA=81=E7=A0=B4=E5=B8=A6=
=E6=9D=A5=E5=95=86=E4=B8=9A=E5=8C=96=E6=9C=BA=E9=81=87=EF=BC=8C=E5=87=BA=E8=
=A1=8C=E6=9C=8D=E5=8A=A1=E5=B8=82=E5=9C=BA=E7=A9=BA=E9=97=B4=E5=B7=A8=E5=A4=
=A7</span></div><span class=3D"text-xs text-muted-foreground flex-shrink-0"=
>2=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start just=
ify-between gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1=
 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line=
-clamp-1">=E3=80=90=E8=B6=8B=E5=8A=BF=E3=80=91L4=E7=BA=A7=E8=87=AA=E5=8A=A8=
=E9=A9=BE=E9=A9=B6=E8=A7=84=E6=A8=A1=E5=BA=94=E7=94=A8=E5=9C=A8=E5=8D=B3=EF=
=BC=8C=E4=BA=A7=E4=B8=9A=E9=93=BE=E4=BB=B7=E5=80=BC=E9=87=8D=E6=9E=84=E5=8A=
=A0=E9=80=9F</span></div><span class=3D"text-xs text-muted-foreground flex-=
shrink-0">1=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-s=
tart justify-between gap-2"><div class=3D"flex items-start gap-2"><span cla=
ss=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span clas=
s=3D"line-clamp-1">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E5=AE=89=E5=85=A8=
=E7=9B=91=E7=AE=A1=E8=A6=81=E6=B1=82=E6=8F=90=E9=AB=98=EF=BC=8C=E9=9C=80=E5=
=85=B3=E6=B3=A8=E6=94=BF=E7=AD=96=E5=AE=A1=E6=89=B9=E5=92=8C=E8=B4=A3=E4=BB=
=BB=E8=AE=A4=E5=AE=9A=E9=97=AE=E9=A2=98</span></div><span class=3D"text-xs =
text-muted-foreground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span></=
li></ul></div></div></div><div class=3D"space-y-2"><div class=3D"flex items=
-start gap-2 px-[15px]"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"=
24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColo=
r" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" cl=
ass=3D"lucide lucide-message-square h-4 w-4 text-muted-foreground mt-0.5 fl=
ex-shrink-0"><path d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 =
0 0 1 2 2z"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-=
sm text-foreground"><li class=3D"flex items-start justify-between gap-2"><d=
iv class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foregro=
und rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 fle=
x-1">=E9=A9=AC=E6=96=AF=E5=85=8B=E5=AE=A3=E5=B8=83Robotaxi=E6=9C=8D=E5=8A=
=A1=E5=B0=86=E5=9C=A8=E5=BE=B7=E5=B7=9E=E5=A5=A5=E6=96=AF=E6=B1=80=E7=8E=87=
=E5=85=88=E4=B8=8A=E7=BA=BF=EF=BC=8C=E6=8A=95=E5=85=A51000=E8=BE=86=E8=BD=
=A6=E9=98=9F</span></div><span class=3D"text-xs text-muted-foreground flex-=
shrink-0">12=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-=
start justify-between gap-2"><div class=3D"flex items-start gap-2"><span cl=
ass=3D"w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></span>=
<span class=3D"line-clamp-1 flex-1">FSD V13=E6=B5=8B=E8=AF=95=E6=95=B0=E6=
=8D=AE=E6=98=BE=E7=A4=BA=E5=AE=89=E5=85=A8=E6=80=A7=E6=8F=90=E5=8D=87500%=
=EF=BC=8C=E4=BA=8B=E6=95=85=E7=8E=87=E9=99=8D=E8=87=B3=E4=BA=BA=E7=B1=BB=E9=
=A9=BE=E9=A9=B6=E5=91=981/10</span></div><span class=3D"text-xs text-muted-=
foreground flex-shrink-0">18=E5=B0=8F=E6=97=B6=E5=89=8D</span></li></ul></d=
iv></div></div></div><div class=3D"w-32 border-l bg-muted/20 p-3 flex flex-=
col"><div class=3D"text-xs text-muted-foreground mb-2 font-medium">Followin=
g</div><div class=3D"space-y-1.5 flex-1"><button class=3D"group relative w-=
full p-2 bg-card border border-border rounded-lg hover:border-primary/30 ho=
ver:bg-accent/50 transition-all duration-200 text-left"><div class=3D"absol=
ute -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div><div class=
=3D"flex justify-between items-center"><div class=3D"font-sans text-xs font=
-medium text-foreground group-hover:text-primary transition-colors">TSLA</d=
iv><div class=3D"text-xs font-medium text-success">****%</div></div></butto=
n><button class=3D"group relative w-full p-2 bg-card border border-border r=
ounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all duratio=
n-200 text-left"><div class=3D"flex justify-between items-center"><div clas=
s=3D"font-sans text-xs font-medium text-foreground group-hover:text-primary=
 transition-colors">NVDA</div><div class=3D"text-xs font-medium text-destru=
ctive">-1.2%</div></div></button><button class=3D"group relative w-full p-2=
 bg-card border border-border rounded-lg hover:border-primary/30 hover:bg-a=
ccent/50 transition-all duration-200 text-left"><div class=3D"absolute -top=
-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div><div class=3D"flex j=
ustify-between items-center"><div class=3D"font-sans text-xs font-medium te=
xt-foreground group-hover:text-primary transition-colors">AMD</div><div cla=
ss=3D"text-xs font-medium text-success">+0.8%</div></div></button><button c=
lass=3D"group relative w-full p-2 bg-card border border-border rounded-lg h=
over:border-primary/30 hover:bg-accent/50 transition-all duration-200 text-=
left"><div class=3D"flex justify-between items-center"><div class=3D"font-s=
ans text-xs font-medium text-foreground group-hover:text-primary transition=
-colors">INTC</div><div class=3D"text-xs font-medium text-success">+4.1%</d=
iv></div></button><button class=3D"group relative w-full p-2 bg-card border=
 border-border rounded-lg hover:border-primary/30 hover:bg-accent/50 transi=
tion-all duration-200 text-left"><div class=3D"flex justify-between items-c=
enter"><div class=3D"font-sans text-xs font-medium text-foreground group-ho=
ver:text-primary transition-colors">QCOM</div><div class=3D"text-xs font-me=
dium text-destructive">-0.5%</div></div></button></div><button class=3D"mt-=
2 w-full py-1.5 text-xs text-muted-foreground hover:text-primary transition=
-colors border-t border-border/50 pt-2">more</button></div></div></div><div=
 class=3D"rounded-lg text-card-foreground shadow-sm cursor-pointer bg-gradi=
ent-card hover:shadow-elevated transition-all duration-300 hover:scale-[1.0=
2] group border"><div class=3D"flex h-full"><div class=3D"flex-1 p-6 flex f=
lex-col gap-4"><div class=3D"flex items-start gap-3"><svg xmlns=3D"http://w=
ww.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=
=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"roun=
d" stroke-linejoin=3D"round" class=3D"lucide lucide-brain h-5 w-5 text-prim=
ary flex-shrink-0 mt-1"><path d=3D"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.52=
6 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"></path><path d=3D"M12 5a3 3 0=
 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z">=
</path><path d=3D"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"></path><path =
d=3D"M17.599 6.5a3 3 0 0 0 .399-1.375"></path><path d=3D"M6.003 5.125A3 3 0=
 0 0 6.401 6.5"></path><path d=3D"M3.477 10.896a4 4 0 0 1 .585-.396"></path=
><path d=3D"M19.938 10.5a4 4 0 0 1 .585.396"></path><path d=3D"M6 18a4 4 0 =
0 1-1.967-.516"></path><path d=3D"M19.967 17.484A4 4 0 0 1 18 18"></path></=
svg><div class=3D"flex-1 min-w-0"><h3 class=3D"font-bold text-base leading-=
tight line-clamp-2 group-hover:text-primary transition-colors mb-2">=E8=8B=
=B9=E6=9E=9CM4 Ultra=E8=8A=AF=E7=89=87=E5=8F=91=E5=B8=83=EF=BC=8C=E7=AB=AF=
=E4=BE=A7AI=E8=AE=A1=E7=AE=97=E8=83=BD=E5=8A=9B=E8=B6=85=E8=B6=8A=E4=BA=91=
=E7=AB=AF</h3><div class=3D"flex items-center justify-between gap-2"><div c=
lass=3D"flex items-center gap-2"><div class=3D"inline-flex items-center rou=
nded-full border font-semibold transition-colors focus:outline-none focus:r=
ing-2 focus:ring-ring focus:ring-offset-2 text-foreground text-xs px-2 py-1=
">=E8=8A=AF=E7=89=87=E6=8A=80=E6=9C=AF</div></div><div class=3D"flex items-=
center gap-1.5"><div class=3D"relative"><svg xmlns=3D"http://www.w3.org/200=
0/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" strok=
e=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linej=
oin=3D"round" class=3D"lucide lucide-chart-column h-3.5 w-3.5 text-primary =
animate-pulse"><path d=3D"M3 3v16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 1=
7V9"></path><path d=3D"M13 17V5"></path><path d=3D"M8 17v-3"></path></svg><=
div class=3D"absolute -top-1 -right-1 w-1.5 h-1.5 bg-green-500 rounded-full=
 animate-pulse"></div></div><svg xmlns=3D"http://www.w3.org/2000/svg" width=
=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"lucide lucide-mail h-3.5 w-3.5 text-muted-foreground hover:text-=
primary transition-colors cursor-pointer"><rect width=3D"20" height=3D"16" =
x=3D"2" y=3D"4" rx=3D"2"></rect><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2=
.06 0L2 7"></path></svg><div class=3D"flex items-center gap-1 cursor-pointe=
r hover:text-primary transition-colors"><svg xmlns=3D"http://www.w3.org/200=
0/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" strok=
e=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linej=
oin=3D"round" class=3D"lucide lucide-message-circle h-3.5 w-3.5 text-muted-=
foreground"><path d=3D"M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span c=
lass=3D"text-xs text-muted-foreground">31</span></div></div></div></div></d=
iv><div class=3D"p-3 bg-gradient-to-r from-gray-50 to-gray-100 border borde=
r-gray-200/50 rounded-none"><div class=3D"flex items-start gap-2"><svg xmln=
s=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 =
24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-line=
cap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-sparkles h-4=
 w-4 text-primary mt-0.5 flex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.=
5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582=
-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0=
 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 =
0z"></path><path d=3D"M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=
=3D"M4 17v2"></path><path d=3D"M5 18H3"></path></svg><div class=3D"flex-1">=
<ul class=3D"space-y-2 text-sm text-foreground"><li class=3D"flex items-sta=
rt justify-between gap-2"><div class=3D"flex items-start gap-2"><span class=
=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=
=3D"line-clamp-1">=E3=80=90=E5=88=A9=E5=A5=BD=E3=80=91=E8=8A=AF=E7=89=87=E6=
=80=A7=E8=83=BD=E5=A4=A7=E5=B9=85=E6=8F=90=E5=8D=87=EF=BC=8C=E4=B8=8B=E6=B8=
=B8=E5=BA=94=E7=94=A8=E9=9C=80=E6=B1=82=E6=BF=80=E5=A2=9E=EF=BC=8C=E4=BA=A7=
=E4=B8=9A=E9=93=BE=E9=BE=99=E5=A4=B4=E5=8F=97=E7=9B=8A=E6=98=8E=E6=98=BE</s=
pan></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">2=E5=
=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-b=
etween gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 =
bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clam=
p-1">=E3=80=90=E8=B6=8B=E5=8A=BF=E3=80=91AI=E7=AE=97=E5=8A=9B=E9=9C=80=E6=
=B1=82=E6=8C=81=E7=BB=AD=E7=88=86=E5=8F=91=EF=BC=8C=E9=AB=98=E7=AB=AF=E8=8A=
=AF=E7=89=87=E4=BE=9B=E4=B8=8D=E5=BA=94=E6=B1=82=EF=BC=8C=E8=AE=AE=E4=BB=B7=
=E8=83=BD=E5=8A=9B=E5=A2=9E=E5=BC=BA</span></div><span class=3D"text-xs tex=
t-muted-foreground flex-shrink-0">1=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><=
li class=3D"flex items-start justify-between gap-2"><div class=3D"flex item=
s-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shr=
ink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E9=A3=8E=E9=99=A9=E3=
=80=91=E5=9C=B0=E7=BC=98=E6=94=BF=E6=B2=BB=E5=BD=B1=E5=93=8D=E4=BE=9B=E5=BA=
=94=E9=93=BE=E7=A8=B3=E5=AE=9A=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8=E5=9B=BD=
=E9=99=85=E8=B4=B8=E6=98=93=E6=94=BF=E7=AD=96=E5=8F=98=E5=8C=96</span></div=
><span class=3D"text-xs text-muted-foreground flex-shrink-0">30=E5=88=86=E9=
=92=9F=E5=89=8D</span></li></ul></div></div></div><div class=3D"space-y-2">=
<div class=3D"flex items-start gap-2 px-[15px]"><svg xmlns=3D"http://www.w3=
.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"non=
e" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stro=
ke-linejoin=3D"round" class=3D"lucide lucide-message-square h-4 w-4 text-mu=
ted-foreground mt-0.5 flex-shrink-0"><path d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4=
V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg><div class=3D"flex-1"><ul =
class=3D"space-y-2 text-sm text-foreground"><li class=3D"flex items-start j=
ustify-between gap-2"><div class=3D"flex items-start gap-2"><span class=3D"=
w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></span><span c=
lass=3D"line-clamp-1 flex-1">=E8=8B=B9=E6=9E=9CM4 Ultra=E6=80=A7=E8=83=BD=
=E6=B5=8B=E8=AF=95=EF=BC=9AAI=E8=AE=A1=E7=AE=97=E8=83=BD=E6=95=88=E6=AF=94=
=E8=B6=85=E8=B6=8ANVIDIA H100=E8=BE=BE30%</span></div><span class=3D"text-x=
s text-muted-foreground flex-shrink-0">3=E5=B0=8F=E6=97=B6=E5=89=8D</span><=
/li><li class=3D"flex items-start justify-between gap-2"><div class=3D"flex=
 items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-full=
 mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">Adobe=E5=AE=
=A3=E5=B8=83Creative Suite=E5=85=A8=E9=9D=A2=E4=BC=98=E5=8C=96M4 Ultra=EF=
=BC=8C=E6=B8=B2=E6=9F=93=E9=80=9F=E5=BA=A6=E6=8F=90=E5=8D=873=E5=80=8D</spa=
n></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">6=E5=B0=
=8F=E6=97=B6=E5=89=8D</span></li></ul></div></div></div></div><div class=3D=
"w-32 border-l bg-muted/20 p-3 flex flex-col"><div class=3D"text-xs text-mu=
ted-foreground mb-2 font-medium">Following</div><div class=3D"space-y-1.5 f=
lex-1"><button class=3D"group relative w-full p-2 bg-card border border-bor=
der rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all du=
ration-200 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-des=
tructive rounded-full"></div><div class=3D"flex justify-between items-cente=
r"><div class=3D"font-sans text-xs font-medium text-foreground group-hover:=
text-primary transition-colors">AAPL</div><div class=3D"text-xs font-medium=
 text-success">****%</div></div></button><button class=3D"group relative w-=
full p-2 bg-card border border-border rounded-lg hover:border-primary/30 ho=
ver:bg-accent/50 transition-all duration-200 text-left"><div class=3D"flex =
justify-between items-center"><div class=3D"font-sans text-xs font-medium t=
ext-foreground group-hover:text-primary transition-colors">TSM</div><div cl=
ass=3D"text-xs font-medium text-destructive">-1.2%</div></div></button><but=
ton class=3D"group relative w-full p-2 bg-card border border-border rounded=
-lg hover:border-primary/30 hover:bg-accent/50 transition-all duration-200 =
text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive ro=
unded-full"></div><div class=3D"flex justify-between items-center"><div cla=
ss=3D"font-sans text-xs font-medium text-foreground group-hover:text-primar=
y transition-colors">QCOM</div><div class=3D"text-xs font-medium text-succe=
ss">+0.8%</div></div></button><button class=3D"group relative w-full p-2 bg=
-card border border-border rounded-lg hover:border-primary/30 hover:bg-acce=
nt/50 transition-all duration-200 text-left"><div class=3D"flex justify-bet=
ween items-center"><div class=3D"font-sans text-xs font-medium text-foregro=
und group-hover:text-primary transition-colors">AVGO</div><div class=3D"tex=
t-xs font-medium text-success">+4.1%</div></div></button><button class=3D"g=
roup relative w-full p-2 bg-card border border-border rounded-lg hover:bord=
er-primary/30 hover:bg-accent/50 transition-all duration-200 text-left"><di=
v class=3D"flex justify-between items-center"><div class=3D"font-sans text-=
xs font-medium text-foreground group-hover:text-primary transition-colors">=
MU</div><div class=3D"text-xs font-medium text-destructive">-0.5%</div></di=
v></button></div><button class=3D"mt-2 w-full py-1.5 text-xs text-muted-for=
eground hover:text-primary transition-colors border-t border-border/50 pt-2=
">more</button></div></div></div><div class=3D"rounded-lg text-card-foregro=
und shadow-sm cursor-pointer bg-gradient-card hover:shadow-elevated transit=
ion-all duration-300 hover:scale-[1.02] group border"><div class=3D"flex h-=
full"><div class=3D"flex-1 p-6 flex flex-col gap-4"><div class=3D"flex item=
s-start gap-3"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" heigh=
t=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke=
-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lu=
cide lucide-brain h-5 w-5 text-primary flex-shrink-0 mt-1"><path d=3D"M12 5=
a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12=
 18Z"></path><path d=3D"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 =
0 0 1-.556 6.588A4 4 0 1 1 12 18Z"></path><path d=3D"M15 13a4.5 4.5 0 0 1-3=
-4 4.5 4.5 0 0 1-3 4"></path><path d=3D"M17.599 6.5a3 3 0 0 0 .399-1.375"><=
/path><path d=3D"M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d=3D"M3.477=
 10.896a4 4 0 0 1 .585-.396"></path><path d=3D"M19.938 10.5a4 4 0 0 1 .585.=
396"></path><path d=3D"M6 18a4 4 0 0 1-1.967-.516"></path><path d=3D"M19.96=
7 17.484A4 4 0 0 1 18 18"></path></svg><div class=3D"flex-1 min-w-0"><h3 cl=
ass=3D"font-bold text-base leading-tight line-clamp-2 group-hover:text-prim=
ary transition-colors mb-2">=E4=BA=9A=E9=A9=AC=E9=80=8ABedrock=E5=B9=B3=E5=
=8F=B0=E6=8E=A8=E5=87=BA=E4=BC=81=E4=B8=9A=E7=BA=A7AI Agent=EF=BC=8C=E6=89=
=93=E9=80=A0AI=E5=B7=A5=E4=BD=9C=E6=B5=81=E7=94=9F=E6=80=81</h3><div class=
=3D"flex items-center justify-between gap-2"><div class=3D"flex items-cente=
r gap-2"><div class=3D"inline-flex items-center rounded-full border font-se=
mibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring fo=
cus:ring-offset-2 text-foreground text-xs px-2 py-1">=E4=BA=91=E6=9C=8D=E5=
=8A=A1</div></div><div class=3D"flex items-center gap-1.5"><div class=3D"re=
lative"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24=
" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=
=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide l=
ucide-chart-column h-3.5 w-3.5 text-primary animate-pulse"><path d=3D"M3 3v=
16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 17V9"></path><path d=3D"M13 17V5=
"></path><path d=3D"M8 17v-3"></path></svg><div class=3D"absolute -top-1 -r=
ight-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div></div><sv=
g xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=
=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" str=
oke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-mail=
 h-3.5 w-3.5 text-muted-foreground hover:text-primary transition-colors cur=
sor-pointer"><rect width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"2"></re=
ct><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><div =
class=3D"flex items-center gap-1 cursor-pointer hover:text-primary transiti=
on-colors"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D=
"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-wid=
th=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide=
 lucide-message-circle h-3.5 w-3.5 text-muted-foreground"><path d=3D"M7.9 2=
0A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span class=3D"text-xs text-muted-fo=
reground">24</span></div></div></div></div></div><div class=3D"p-3 bg-gradi=
ent-to-r from-gray-50 to-gray-100 border border-gray-200/50 rounded-none"><=
div class=3D"flex items-start gap-2"><svg xmlns=3D"http://www.w3.org/2000/s=
vg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=
=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejo=
in=3D"round" class=3D"lucide lucide-sparkles h-4 w-4 text-primary mt-0.5 fl=
ex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 =
0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14=
.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2=
 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3=
v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=
=3D"M5 18H3"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text=
-sm text-foreground"><li class=3D"flex items-start justify-between gap-2"><=
div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary roun=
ded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=
=E5=88=A9=E5=A5=BD=E3=80=91=E4=BC=81=E4=B8=9A=E6=95=B0=E5=AD=97=E5=8C=96=E8=
=BD=AC=E5=9E=8B=E5=8A=A0=E9=80=9F=EF=BC=8CAI=E5=BA=94=E7=94=A8=E6=B8=97=E9=
=80=8F=E7=8E=87=E5=BF=AB=E9=80=9F=E6=8F=90=E5=8D=87=EF=BC=8C=E5=95=86=E4=B8=
=9A=E5=8C=96=E8=BF=9B=E7=A8=8B=E6=98=8E=E7=A1=AE</span></div><span class=3D=
"text-xs text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D<=
/span></li><li class=3D"flex items-start justify-between gap-2"><div class=
=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full =
mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E8=B6=8B=
=E5=8A=BF=E3=80=91AI Agent=E6=88=90=E4=B8=BA=E6=96=B0=E5=A2=9E=E9=95=BF=E7=
=82=B9=EF=BC=8CB=E7=AB=AF=E5=B8=82=E5=9C=BA=E7=88=86=E5=8F=91=EF=BC=8C=E8=
=AE=A2=E9=98=85=E6=A8=A1=E5=BC=8F=E9=AA=8C=E8=AF=81=E6=88=90=E5=8A=9F</span=
></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=B0=
=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-betw=
een gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-=
primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1=
">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E7=9B=91=E7=AE=A1=E6=94=BF=E7=AD=96=
=E8=B6=8B=E4=B8=A5=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8AI=E5=AE=89=E5=85=A8=
=E5=92=8C=E6=95=B0=E6=8D=AE=E9=9A=90=E7=A7=81=E7=9B=B8=E5=85=B3=E6=B3=95=E8=
=A7=84=E5=BD=B1=E5=93=8D</span></div><span class=3D"text-xs text-muted-fore=
ground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span></li></ul></div><=
/div></div><div class=3D"space-y-2"><div class=3D"flex items-start gap-2 px=
-[15px]"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"2=
4" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=
=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide l=
ucide-message-square h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0"><p=
ath d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></p=
ath></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-foregro=
und"><li class=3D"flex items-start justify-between gap-2"><div class=3D"fle=
x items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-ful=
l mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">Bedrock AI=
 Agent beta=E7=89=88=E6=9C=AC=E5=8F=91=E5=B8=83=EF=BC=8C=E5=B7=B2=E6=9C=895=
00=E5=AE=B6=E4=BC=81=E4=B8=9A=E7=94=B3=E8=AF=B7=E8=AF=95=E7=94=A8</span></d=
iv><span class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=B0=8F=
=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-between=
 gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-mut=
ed-foreground rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-c=
lamp-1 flex-1">Amazon=E4=B8=8ESalesforce=E6=B7=B1=E5=BA=A6=E5=90=88=E4=BD=
=9C=EF=BC=8CAI Agent=E9=9B=86=E6=88=90CRM=E7=B3=BB=E7=BB=9F</span></div><sp=
an class=3D"text-xs text-muted-foreground flex-shrink-0">4=E5=B0=8F=E6=97=
=B6=E5=89=8D</span></li></ul></div></div></div></div><div class=3D"w-32 bor=
der-l bg-muted/20 p-3 flex flex-col"><div class=3D"text-xs text-muted-foreg=
round mb-2 font-medium">Following</div><div class=3D"space-y-1.5 flex-1"><b=
utton class=3D"group relative w-full p-2 bg-card border border-border round=
ed-lg hover:border-primary/30 hover:bg-accent/50 transition-all duration-20=
0 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive =
rounded-full"></div><div class=3D"flex justify-between items-center"><div c=
lass=3D"font-sans text-xs font-medium text-foreground group-hover:text-prim=
ary transition-colors">AMZN</div><div class=3D"text-xs font-medium text-suc=
cess">****%</div></div></button><button class=3D"group relative w-full p-2 =
bg-card border border-border rounded-lg hover:border-primary/30 hover:bg-ac=
cent/50 transition-all duration-200 text-left"><div class=3D"flex justify-b=
etween items-center"><div class=3D"font-sans text-xs font-medium text-foreg=
round group-hover:text-primary transition-colors">MSFT</div><div class=3D"t=
ext-xs font-medium text-destructive">-1.2%</div></div></button><button clas=
s=3D"group relative w-full p-2 bg-card border border-border rounded-lg hove=
r:border-primary/30 hover:bg-accent/50 transition-all duration-200 text-lef=
t"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-fu=
ll"></div><div class=3D"flex justify-between items-center"><div class=3D"fo=
nt-sans text-xs font-medium text-foreground group-hover:text-primary transi=
tion-colors">GOOGL</div><div class=3D"text-xs font-medium text-success">+0.=
8%</div></div></button><button class=3D"group relative w-full p-2 bg-card b=
order border-border rounded-lg hover:border-primary/30 hover:bg-accent/50 t=
ransition-all duration-200 text-left"><div class=3D"flex justify-between it=
ems-center"><div class=3D"font-sans text-xs font-medium text-foreground gro=
up-hover:text-primary transition-colors">CRM</div><div class=3D"text-xs fon=
t-medium text-success">+4.1%</div></div></button><button class=3D"group rel=
ative w-full p-2 bg-card border border-border rounded-lg hover:border-prima=
ry/30 hover:bg-accent/50 transition-all duration-200 text-left"><div class=
=3D"flex justify-between items-center"><div class=3D"font-sans text-xs font=
-medium text-foreground group-hover:text-primary transition-colors">SNOW</d=
iv><div class=3D"text-xs font-medium text-destructive">-0.5%</div></div></b=
utton></div><button class=3D"mt-2 w-full py-1.5 text-xs text-muted-foregrou=
nd hover:text-primary transition-colors border-t border-border/50 pt-2">mor=
e</button></div></div></div><div class=3D"rounded-lg text-card-foreground s=
hadow-sm cursor-pointer bg-gradient-card hover:shadow-elevated transition-a=
ll duration-300 hover:scale-[1.02] group border"><div class=3D"flex h-full"=
><div class=3D"flex-1 p-6 flex flex-col gap-4"><div class=3D"flex items-sta=
rt gap-3"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"=
24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-widt=
h=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide =
lucide-brain h-5 w-5 text-primary flex-shrink-0 mt-1"><path d=3D"M12 5a3 3 =
0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"=
></path><path d=3D"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1=
-.556 6.588A4 4 0 1 1 12 18Z"></path><path d=3D"M15 13a4.5 4.5 0 0 1-3-4 4.=
5 4.5 0 0 1-3 4"></path><path d=3D"M17.599 6.5a3 3 0 0 0 .399-1.375"></path=
><path d=3D"M6.003 5.125A3 3 0 0 0 6.401 6.5"></path><path d=3D"M3.477 10.8=
96a4 4 0 0 1 .585-.396"></path><path d=3D"M19.938 10.5a4 4 0 0 1 .585.396">=
</path><path d=3D"M6 18a4 4 0 0 1-1.967-.516"></path><path d=3D"M19.967 17.=
484A4 4 0 0 1 18 18"></path></svg><div class=3D"flex-1 min-w-0"><h3 class=
=3D"font-bold text-base leading-tight line-clamp-2 group-hover:text-primary=
 transition-colors mb-2">Anthropic Claude 3.5=E5=8F=91=E5=B8=83=EF=BC=8CAI=
=E5=AE=89=E5=85=A8=E6=80=A7=E4=B8=8E=E8=83=BD=E5=8A=9B=E5=B9=B6=E9=87=8D=E7=
=AA=81=E7=A0=B4</h3><div class=3D"flex items-center justify-between gap-2">=
<div class=3D"flex items-center gap-2"><div class=3D"inline-flex items-cent=
er rounded-full border font-semibold transition-colors focus:outline-none f=
ocus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground text-xs px-=
2 py-1">AI=E5=AE=89=E5=85=A8</div></div><div class=3D"flex items-center gap=
-1.5"><div class=3D"relative"><svg xmlns=3D"http://www.w3.org/2000/svg" wid=
th=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"curre=
ntColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"rou=
nd" class=3D"lucide lucide-chart-column h-3.5 w-3.5 text-primary animate-pu=
lse"><path d=3D"M3 3v16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 17V9"></pat=
h><path d=3D"M13 17V5"></path><path d=3D"M8 17v-3"></path></svg><div class=
=3D"absolute -top-1 -right-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-=
pulse"></div></div><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" h=
eight=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" st=
roke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=
=3D"lucide lucide-mail h-3.5 w-3.5 text-muted-foreground hover:text-primary=
 transition-colors cursor-pointer"><rect width=3D"20" height=3D"16" x=3D"2"=
 y=3D"4" rx=3D"2"></rect><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2=
 7"></path></svg><div class=3D"flex items-center gap-1 cursor-pointer hover=
:text-primary transition-colors"><svg xmlns=3D"http://www.w3.org/2000/svg" =
width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"cu=
rrentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"=
round" class=3D"lucide lucide-message-circle h-3.5 w-3.5 text-muted-foregro=
und"><path d=3D"M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span class=3D=
"text-xs text-muted-foreground">36</span></div></div></div></div></div><div=
 class=3D"p-3 bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-=
200/50 rounded-none"><div class=3D"flex items-start gap-2"><svg xmlns=3D"ht=
tp://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" =
fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"=
round" stroke-linejoin=3D"round" class=3D"lucide lucide-sparkles h-4 w-4 te=
xt-primary mt-0.5 flex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.06=
3l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a=
.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 =
.964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></p=
ath><path d=3D"M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17=
v2"></path><path d=3D"M5 18H3"></path></svg><div class=3D"flex-1"><ul class=
=3D"space-y-2 text-sm text-foreground"><li class=3D"flex items-start justif=
y-between gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h=
-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-c=
lamp-1">=E3=80=90=E5=88=A9=E5=A5=BD=E3=80=91=E4=BC=81=E4=B8=9A=E6=95=B0=E5=
=AD=97=E5=8C=96=E8=BD=AC=E5=9E=8B=E5=8A=A0=E9=80=9F=EF=BC=8CAI=E5=BA=94=E7=
=94=A8=E6=B8=97=E9=80=8F=E7=8E=87=E5=BF=AB=E9=80=9F=E6=8F=90=E5=8D=87=EF=BC=
=8C=E5=95=86=E4=B8=9A=E5=8C=96=E8=BF=9B=E7=A8=8B=E6=98=8E=E7=A1=AE</span></=
div><span class=3D"text-xs text-muted-foreground flex-shrink-0">2=E5=B0=8F=
=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-between=
 gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-pri=
mary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=
=E3=80=90=E8=B6=8B=E5=8A=BF=E3=80=91AI Agent=E6=88=90=E4=B8=BA=E6=96=B0=E5=
=A2=9E=E9=95=BF=E7=82=B9=EF=BC=8CB=E7=AB=AF=E5=B8=82=E5=9C=BA=E7=88=86=E5=
=8F=91=EF=BC=8C=E8=AE=A2=E9=98=85=E6=A8=A1=E5=BC=8F=E9=AA=8C=E8=AF=81=E6=88=
=90=E5=8A=9F</span></div><span class=3D"text-xs text-muted-foreground flex-=
shrink-0">1=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-s=
tart justify-between gap-2"><div class=3D"flex items-start gap-2"><span cla=
ss=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span clas=
s=3D"line-clamp-1">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E7=9B=91=E7=AE=A1=
=E6=94=BF=E7=AD=96=E8=B6=8B=E4=B8=A5=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8AI=
=E5=AE=89=E5=85=A8=E5=92=8C=E6=95=B0=E6=8D=AE=E9=9A=90=E7=A7=81=E7=9B=B8=E5=
=85=B3=E6=B3=95=E8=A7=84=E5=BD=B1=E5=93=8D</span></div><span class=3D"text-=
xs text-muted-foreground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span=
></li></ul></div></div></div><div class=3D"space-y-2"><div class=3D"flex it=
ems-start gap-2 px-[15px]"><svg xmlns=3D"http://www.w3.org/2000/svg" width=
=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"lucide lucide-message-square h-4 w-4 text-muted-foreground mt-0.=
5 flex-shrink-0"><path d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a=
2 2 0 0 1 2 2z"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 t=
ext-sm text-foreground"><li class=3D"flex items-start justify-between gap-2=
"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-muted-for=
eground rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1=
 flex-1">Claude 3.5=E9=80=9A=E8=BF=87=E6=AC=A7=E7=9B=9FAI=E6=B3=95=E6=A1=88=
=E8=AE=A4=E8=AF=81=EF=BC=8C=E6=88=90=E4=B8=BA=E9=A6=96=E4=B8=AA=E5=90=88=E8=
=A7=84=E7=9A=84=E5=A4=A7=E6=A8=A1=E5=9E=8B</span></div><span class=3D"text-=
xs text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D</span>=
</li><li class=3D"flex items-start justify-between gap-2"><div class=3D"fle=
x items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-ful=
l mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">=E5=A4=9A=
=E5=AE=B6=E9=87=91=E8=9E=8D=E6=9C=BA=E6=9E=84=E9=80=89=E6=8B=A9Claude 3.5=
=E5=A4=84=E7=90=86=E6=95=8F=E6=84=9F=E6=95=B0=E6=8D=AE=EF=BC=8C=E5=AE=89=E5=
=85=A8=E6=80=A7=E8=8E=B7=E8=AE=A4=E5=8F=AF</span></div><span class=3D"text-=
xs text-muted-foreground flex-shrink-0">5=E5=B0=8F=E6=97=B6=E5=89=8D</span>=
</li></ul></div></div></div></div><div class=3D"w-32 border-l bg-muted/20 p=
-3 flex flex-col"><div class=3D"text-xs text-muted-foreground mb-2 font-med=
ium">Following</div><div class=3D"space-y-1.5 flex-1"><button class=3D"grou=
p relative w-full p-2 bg-card border border-border rounded-lg hover:border-=
primary/30 hover:bg-accent/50 transition-all duration-200 text-left"><div c=
lass=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div=
><div class=3D"flex justify-between items-center"><div class=3D"font-sans t=
ext-xs font-medium text-foreground group-hover:text-primary transition-colo=
rs">GOOGL</div><div class=3D"text-xs font-medium text-success">****%</div><=
/div></button><button class=3D"group relative w-full p-2 bg-card border bor=
der-border rounded-lg hover:border-primary/30 hover:bg-accent/50 transition=
-all duration-200 text-left"><div class=3D"flex justify-between items-cente=
r"><div class=3D"font-sans text-xs font-medium text-foreground group-hover:=
text-primary transition-colors">MSFT</div><div class=3D"text-xs font-medium=
 text-destructive">-1.2%</div></div></button><button class=3D"group relativ=
e w-full p-2 bg-card border border-border rounded-lg hover:border-primary/3=
0 hover:bg-accent/50 transition-all duration-200 text-left"><div class=3D"a=
bsolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div><div cla=
ss=3D"flex justify-between items-center"><div class=3D"font-sans text-xs fo=
nt-medium text-foreground group-hover:text-primary transition-colors">NVDA<=
/div><div class=3D"text-xs font-medium text-success">+0.8%</div></div></but=
ton><button class=3D"group relative w-full p-2 bg-card border border-border=
 rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all durat=
ion-200 text-left"><div class=3D"flex justify-between items-center"><div cl=
ass=3D"font-sans text-xs font-medium text-foreground group-hover:text-prima=
ry transition-colors">AMZN</div><div class=3D"text-xs font-medium text-succ=
ess">+4.1%</div></div></button><button class=3D"group relative w-full p-2 b=
g-card border border-border rounded-lg hover:border-primary/30 hover:bg-acc=
ent/50 transition-all duration-200 text-left"><div class=3D"flex justify-be=
tween items-center"><div class=3D"font-sans text-xs font-medium text-foregr=
ound group-hover:text-primary transition-colors">META</div><div class=3D"te=
xt-xs font-medium text-destructive">-0.5%</div></div></button></div><button=
 class=3D"mt-2 w-full py-1.5 text-xs text-muted-foreground hover:text-prima=
ry transition-colors border-t border-border/50 pt-2">more</button></div></d=
iv></div></div></div><div class=3D"space-y-6"><div class=3D"flex items-cent=
er justify-between"><h2 class=3D"text-2xl font-bold">=E5=B8=82=E5=9C=BA=E6=
=8E=A8=E8=8D=90</h2><div class=3D"text-sm text-muted-foreground">=E5=8F=91=
=E7=8E=B0=E6=9B=B4=E5=A4=9A=E6=8A=95=E8=B5=84=E6=9C=BA=E4=BC=9A =C2=B7 =E5=
=85=B1 10 =E4=B8=AA</div></div><div class=3D"flex items-center gap-6 pb-4 b=
order-b border-slate-200"><div class=3D"flex items-center gap-2"><span clas=
s=3D"text-sm font-medium text-slate-700 min-w-[48px]">=E6=A0=87=E7=AD=BE=E7=
=AD=9B=E9=80=89</span><button class=3D"inline-flex items-center justify-cen=
ter gap-2 whitespace-nowrap font-medium ring-offset-background transition-a=
ll duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visib=
le:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disab=
led:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_sv=
g]:shrink-0 border bg-background hover:bg-accent hover:text-accent-foregrou=
nd shadow-sm hover:shadow-md rounded-md h-8 px-3 text-xs text-slate-600 bor=
der-slate-300" type=3D"button" id=3D"radix-:r3p:" aria-haspopup=3D"menu" ar=
ia-expanded=3D"false" data-state=3D"closed">=E5=85=A8=E9=83=A8 <svg xmlns=
=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 2=
4 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linec=
ap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-chevron-down =
ml-1 h-3 w-3"><path d=3D"m6 9 6 6 6-6"></path></svg></button></div></div><d=
iv class=3D"grid gap-6 lg:grid-cols-2"><div class=3D"rounded-lg text-card-f=
oreground shadow-sm cursor-pointer bg-gradient-card hover:shadow-elevated t=
ransition-all duration-300 hover:scale-[1.02] group border"><div class=3D"f=
lex h-full"><div class=3D"flex-1 p-6 flex flex-col gap-4"><div class=3D"fle=
x items-start gap-3"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24"=
 height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" =
stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=
=3D"lucide lucide-sparkles h-5 w-5 text-primary flex-shrink-0 mt-1"><path d=
=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.9=
36A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 1=
5.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-=
1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path><path d=
=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18H3"></path>=
</svg><div class=3D"flex-1 min-w-0"><h3 class=3D"font-bold text-base leadin=
g-tight line-clamp-2 group-hover:text-primary transition-colors mb-2">Grok-=
4=E5=8D=B3=E5=B0=86=E5=8F=91=E5=B8=83=EF=BC=8CAI=E5=9F=BA=E7=A1=80=E6=A8=A1=
=E5=9E=8B=E8=83=BD=E5=8A=9B=E8=BF=9B=E4=B8=80=E6=AD=A5=E6=8F=90=E5=8D=87</h=
3><div class=3D"flex items-center justify-between gap-2"><div class=3D"flex=
 items-center gap-2"><div class=3D"inline-flex items-center rounded-full bo=
rder font-semibold transition-colors focus:outline-none focus:ring-2 focus:=
ring-ring focus:ring-offset-2 text-foreground text-xs px-2 py-1">AI=E6=A8=
=A1=E5=9E=8B</div><div class=3D"inline-flex items-center rounded-full borde=
r font-semibold transition-colors focus:outline-none focus:ring-2 focus:rin=
g-ring focus:ring-offset-2 text-xs px-2 py-1 text-primary border-primary/30=
">=E6=8E=A8=E8=8D=90</div></div><div class=3D"flex items-center gap-1.5"><d=
iv class=3D"relative"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24=
" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor"=
 stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" clas=
s=3D"lucide lucide-chart-column h-3.5 w-3.5 text-primary animate-pulse"><pa=
th d=3D"M3 3v16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 17V9"></path><path =
d=3D"M13 17V5"></path><path d=3D"M8 17v-3"></path></svg><div class=3D"absol=
ute -top-1 -right-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></=
div></div><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"=
24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-widt=
h=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide =
lucide-mail h-3.5 w-3.5 text-muted-foreground hover:text-primary transition=
-colors cursor-pointer"><rect width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=
=3D"2"></rect><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>=
</svg><div class=3D"flex items-center gap-1 cursor-pointer hover:text-prima=
ry transition-colors"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24=
" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor"=
 stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" clas=
s=3D"lucide lucide-message-circle h-3.5 w-3.5 text-muted-foreground"><path =
d=3D"M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span class=3D"text-xs te=
xt-muted-foreground">6</span></div></div></div></div></div><div class=3D"p-=
3 bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200/50 round=
ed-none"><div class=3D"flex items-start gap-2"><svg xmlns=3D"http://www.w3.=
org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none=
" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" strok=
e-linejoin=3D"round" class=3D"lucide lucide-sparkles h-4 w-4 text-primary m=
t-0.5 flex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.5=
82a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .=
963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14=
.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=
=3D"M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path>=
<path d=3D"M5 18H3"></path></svg><div class=3D"flex-1"><ul class=3D"space-y=
-2 text-sm text-foreground"><li class=3D"flex items-start justify-between g=
ap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-prima=
ry rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=
=80=90=E5=88=A9=E5=A5=BD=E3=80=91=E4=BC=81=E4=B8=9A=E6=95=B0=E5=AD=97=E5=8C=
=96=E8=BD=AC=E5=9E=8B=E5=8A=A0=E9=80=9F=EF=BC=8CAI=E5=BA=94=E7=94=A8=E6=B8=
=97=E9=80=8F=E7=8E=87=E5=BF=AB=E9=80=9F=E6=8F=90=E5=8D=87=EF=BC=8C=E5=95=86=
=E4=B8=9A=E5=8C=96=E8=BF=9B=E7=A8=8B=E6=98=8E=E7=A1=AE</span></div><span cl=
ass=3D"text-xs text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=
=89=8D</span></li><li class=3D"flex items-start justify-between gap-2"><div=
 class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded=
-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E8=
=B6=8B=E5=8A=BF=E3=80=91AI Agent=E6=88=90=E4=B8=BA=E6=96=B0=E5=A2=9E=E9=95=
=BF=E7=82=B9=EF=BC=8CB=E7=AB=AF=E5=B8=82=E5=9C=BA=E7=88=86=E5=8F=91=EF=BC=
=8C=E8=AE=A2=E9=98=85=E6=A8=A1=E5=BC=8F=E9=AA=8C=E8=AF=81=E6=88=90=E5=8A=9F=
</span></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">1=
=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justif=
y-between gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h=
-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-c=
lamp-1">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E7=9B=91=E7=AE=A1=E6=94=BF=E7=
=AD=96=E8=B6=8B=E4=B8=A5=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8AI=E5=AE=89=E5=
=85=A8=E5=92=8C=E6=95=B0=E6=8D=AE=E9=9A=90=E7=A7=81=E7=9B=B8=E5=85=B3=E6=B3=
=95=E8=A7=84=E5=BD=B1=E5=93=8D</span></div><span class=3D"text-xs text-mute=
d-foreground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span></li></ul><=
/div></div></div><div class=3D"space-y-2"><div class=3D"flex items-start ga=
p-2 px-[15px]"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" heigh=
t=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke=
-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lu=
cide lucide-message-square h-4 w-4 text-muted-foreground mt-0.5 flex-shrink=
-0"><path d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2=
z"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-f=
oreground"><li class=3D"flex items-start justify-between gap-2"><div class=
=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground roun=
ded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">Gro=
k-4=E6=B5=8B=E8=AF=95=E7=89=88=E6=9C=AC=E6=80=A7=E8=83=BD=E6=8C=87=E6=A0=87=
=E6=9B=9D=E5=85=89=EF=BC=8C=E6=8E=A8=E7=90=86=E8=83=BD=E5=8A=9B=E8=B6=85=E8=
=B6=8AGPT-4o=E8=BE=BE25%</span></div><span class=3D"text-xs text-muted-fore=
ground flex-shrink-0">15=E5=88=86=E9=92=9F=E5=89=8D</span></li><li class=3D=
"flex items-start justify-between gap-2"><div class=3D"flex items-start gap=
-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrin=
k-0"></span><span class=3D"line-clamp-1 flex-1">xAI=E8=8E=B7=E5=BE=9760=E4=
=BA=BF=E7=BE=8E=E5=85=83=E6=96=B0=E4=B8=80=E8=BD=AE=E8=9E=8D=E8=B5=84=EF=BC=
=8C=E4=BC=B0=E5=80=BC=E7=AA=81=E7=A0=B4500=E4=BA=BF=E7=BE=8E=E5=85=83</span=
></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=B0=
=8F=E6=97=B6=E5=89=8D</span></li></ul></div></div></div></div><div class=3D=
"w-32 border-l bg-muted/20 p-3 flex flex-col"><div class=3D"text-xs text-mu=
ted-foreground mb-2 font-medium">Following</div><div class=3D"space-y-1.5 f=
lex-1"><button class=3D"group relative w-full p-2 bg-card border border-bor=
der rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all du=
ration-200 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-des=
tructive rounded-full"></div><div class=3D"flex justify-between items-cente=
r"><div class=3D"font-sans text-xs font-medium text-foreground group-hover:=
text-primary transition-colors">NVDA</div><div class=3D"text-xs font-medium=
 text-success">****%</div></div></button><button class=3D"group relative w-=
full p-2 bg-card border border-border rounded-lg hover:border-primary/30 ho=
ver:bg-accent/50 transition-all duration-200 text-left"><div class=3D"flex =
justify-between items-center"><div class=3D"font-sans text-xs font-medium t=
ext-foreground group-hover:text-primary transition-colors">MSFT</div><div c=
lass=3D"text-xs font-medium text-destructive">-1.2%</div></div></button><bu=
tton class=3D"group relative w-full p-2 bg-card border border-border rounde=
d-lg hover:border-primary/30 hover:bg-accent/50 transition-all duration-200=
 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive r=
ounded-full"></div><div class=3D"flex justify-between items-center"><div cl=
ass=3D"font-sans text-xs font-medium text-foreground group-hover:text-prima=
ry transition-colors">GOOGL</div><div class=3D"text-xs font-medium text-suc=
cess">+0.8%</div></div></button><button class=3D"group relative w-full p-2 =
bg-card border border-border rounded-lg hover:border-primary/30 hover:bg-ac=
cent/50 transition-all duration-200 text-left"><div class=3D"flex justify-b=
etween items-center"><div class=3D"font-sans text-xs font-medium text-foreg=
round group-hover:text-primary transition-colors">META</div><div class=3D"t=
ext-xs font-medium text-success">+4.1%</div></div></button><button class=3D=
"group relative w-full p-2 bg-card border border-border rounded-lg hover:bo=
rder-primary/30 hover:bg-accent/50 transition-all duration-200 text-left"><=
div class=3D"flex justify-between items-center"><div class=3D"font-sans tex=
t-xs font-medium text-foreground group-hover:text-primary transition-colors=
">TSLA</div><div class=3D"text-xs font-medium text-destructive">-0.5%</div>=
</div></button></div><button class=3D"mt-2 w-full py-1.5 text-xs text-muted=
-foreground hover:text-primary transition-colors border-t border-border/50 =
pt-2">more</button></div></div></div><div class=3D"rounded-lg text-card-for=
eground shadow-sm cursor-pointer bg-gradient-card hover:shadow-elevated tra=
nsition-all duration-300 hover:scale-[1.02] group border"><div class=3D"fle=
x h-full"><div class=3D"flex-1 p-6 flex flex-col gap-4"><div class=3D"flex =
items-start gap-3"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" h=
eight=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" st=
roke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=
=3D"lucide lucide-sparkles h-5 w-5 text-primary flex-shrink-0 mt-1"><path d=
=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.9=
36A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 1=
5.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-=
1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path><path d=
=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18H3"></path>=
</svg><div class=3D"flex-1 min-w-0"><h3 class=3D"font-bold text-base leadin=
g-tight line-clamp-2 group-hover:text-primary transition-colors mb-2">OpenA=
I=E5=8F=91=E5=B8=83Sora=E8=A7=86=E9=A2=91=E7=94=9F=E6=88=90=E6=A8=A1=E5=9E=
=8B=E5=95=86=E4=B8=9A=E7=89=88=E6=9C=AC</h3><div class=3D"flex items-center=
 justify-between gap-2"><div class=3D"flex items-center gap-2"><div class=
=3D"inline-flex items-center rounded-full border font-semibold transition-c=
olors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 t=
ext-foreground text-xs px-2 py-1">AI=E8=A7=86=E9=A2=91</div><div class=3D"i=
nline-flex items-center rounded-full border font-semibold transition-colors=
 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-x=
s px-2 py-1 text-primary border-primary/30">=E6=8E=A8=E8=8D=90</div></div><=
div class=3D"flex items-center gap-1.5"><div class=3D"relative"><svg xmlns=
=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 2=
4 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linec=
ap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-chart-column =
h-3.5 w-3.5 text-primary animate-pulse"><path d=3D"M3 3v16a2 2 0 0 0 2 2h16=
"></path><path d=3D"M18 17V9"></path><path d=3D"M13 17V5"></path><path d=3D=
"M8 17v-3"></path></svg><div class=3D"absolute -top-1 -right-1 w-1.5 h-1.5 =
bg-green-500 rounded-full animate-pulse"></div></div><svg xmlns=3D"http://w=
ww.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=
=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"roun=
d" stroke-linejoin=3D"round" class=3D"lucide lucide-mail h-3.5 w-3.5 text-m=
uted-foreground hover:text-primary transition-colors cursor-pointer"><rect =
width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"2"></rect><path d=3D"m22 7=
-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><div class=3D"flex items=
-center gap-1 cursor-pointer hover:text-primary transition-colors"><svg xml=
ns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0=
 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-lin=
ecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-message-cir=
cle h-3.5 w-3.5 text-muted-foreground"><path d=3D"M7.9 20A9 9 0 1 0 4 16.1L=
2 22Z"></path></svg><span class=3D"text-xs text-muted-foreground">26</span>=
</div></div></div></div></div><div class=3D"p-3 bg-gradient-to-r from-gray-=
50 to-gray-100 border border-gray-200/50 rounded-none"><div class=3D"flex i=
tems-start gap-2"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" he=
ight=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" str=
oke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D=
"lucide lucide-sparkles h-4 w-4 text-primary mt-0.5 flex-shrink-0"><path d=
=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.9=
36A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 1=
5.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-=
1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path><path d=
=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18H3"></path>=
</svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-foreground"=
><li class=3D"flex items-start justify-between gap-2"><div class=3D"flex it=
ems-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-s=
hrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E5=88=A9=E5=A5=BD=E3=
=80=91=E4=BC=81=E4=B8=9A=E6=95=B0=E5=AD=97=E5=8C=96=E8=BD=AC=E5=9E=8B=E5=8A=
=A0=E9=80=9F=EF=BC=8CAI=E5=BA=94=E7=94=A8=E6=B8=97=E9=80=8F=E7=8E=87=E5=BF=
=AB=E9=80=9F=E6=8F=90=E5=8D=87=EF=BC=8C=E5=95=86=E4=B8=9A=E5=8C=96=E8=BF=9B=
=E7=A8=8B=E6=98=8E=E7=A1=AE</span></div><span class=3D"text-xs text-muted-f=
oreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=
=3D"flex items-start justify-between gap-2"><div class=3D"flex items-start =
gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"><=
/span><span class=3D"line-clamp-1">=E3=80=90=E8=B6=8B=E5=8A=BF=E3=80=91AI A=
gent=E6=88=90=E4=B8=BA=E6=96=B0=E5=A2=9E=E9=95=BF=E7=82=B9=EF=BC=8CB=E7=AB=
=AF=E5=B8=82=E5=9C=BA=E7=88=86=E5=8F=91=EF=BC=8C=E8=AE=A2=E9=98=85=E6=A8=A1=
=E5=BC=8F=E9=AA=8C=E8=AF=81=E6=88=90=E5=8A=9F</span></div><span class=3D"te=
xt-xs text-muted-foreground flex-shrink-0">1=E5=B0=8F=E6=97=B6=E5=89=8D</sp=
an></li><li class=3D"flex items-start justify-between gap-2"><div class=3D"=
flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2=
 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E9=A3=8E=E9=
=99=A9=E3=80=91=E7=9B=91=E7=AE=A1=E6=94=BF=E7=AD=96=E8=B6=8B=E4=B8=A5=EF=BC=
=8C=E9=9C=80=E5=85=B3=E6=B3=A8AI=E5=AE=89=E5=85=A8=E5=92=8C=E6=95=B0=E6=8D=
=AE=E9=9A=90=E7=A7=81=E7=9B=B8=E5=85=B3=E6=B3=95=E8=A7=84=E5=BD=B1=E5=93=8D=
</span></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">30=
=E5=88=86=E9=92=9F=E5=89=8D</span></li></ul></div></div></div><div class=3D=
"space-y-2"><div class=3D"flex items-start gap-2 px-[15px]"><svg xmlns=3D"h=
ttp://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24"=
 fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D=
"round" stroke-linejoin=3D"round" class=3D"lucide lucide-message-square h-4=
 w-4 text-muted-foreground mt-0.5 flex-shrink-0"><path d=3D"M21 15a2 2 0 0 =
1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg><div class=3D"=
flex-1"><ul class=3D"space-y-2 text-sm text-foreground"><li class=3D"flex i=
tems-start justify-between gap-2"><div class=3D"flex items-start gap-2"><sp=
an class=3D"w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></=
span><span class=3D"line-clamp-1 flex-1">Sora Pro=E7=89=88=E6=9C=AC=E5=AE=
=9A=E4=BB=B7=E5=85=AC=E5=B8=83=EF=BC=8C=E6=9C=88=E8=B4=B9$200=E7=9E=84=E5=
=87=86=E4=B8=93=E4=B8=9A=E5=B8=82=E5=9C=BA</span></div><span class=3D"text-=
xs text-muted-foreground flex-shrink-0">45=E5=88=86=E9=92=9F=E5=89=8D</span=
></li><li class=3D"flex items-start justify-between gap-2"><div class=3D"fl=
ex items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-fu=
ll mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">Adobe=E5=
=AE=A3=E5=B8=83=E4=B8=8EOpenAI=E5=90=88=E4=BD=9C=EF=BC=8CSora=E5=B0=86=E9=
=9B=86=E6=88=90=E5=88=B0Premiere Pro</span></div><span class=3D"text-xs tex=
t-muted-foreground flex-shrink-0">3=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><=
/ul></div></div></div></div><div class=3D"w-32 border-l bg-muted/20 p-3 fle=
x flex-col"><div class=3D"text-xs text-muted-foreground mb-2 font-medium">F=
ollowing</div><div class=3D"space-y-1.5 flex-1"><button class=3D"group rela=
tive w-full p-2 bg-card border border-border rounded-lg hover:border-primar=
y/30 hover:bg-accent/50 transition-all duration-200 text-left"><div class=
=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div><di=
v class=3D"flex justify-between items-center"><div class=3D"font-sans text-=
xs font-medium text-foreground group-hover:text-primary transition-colors">=
MSFT</div><div class=3D"text-xs font-medium text-success">****%</div></div>=
</button><button class=3D"group relative w-full p-2 bg-card border border-b=
order rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all =
duration-200 text-left"><div class=3D"flex justify-between items-center"><d=
iv class=3D"font-sans text-xs font-medium text-foreground group-hover:text-=
primary transition-colors">ADBE</div><div class=3D"text-xs font-medium text=
-destructive">-1.2%</div></div></button><button class=3D"group relative w-f=
ull p-2 bg-card border border-border rounded-lg hover:border-primary/30 hov=
er:bg-accent/50 transition-all duration-200 text-left"><div class=3D"absolu=
te -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div><div class=3D=
"flex justify-between items-center"><div class=3D"font-sans text-xs font-me=
dium text-foreground group-hover:text-primary transition-colors">NVDA</div>=
<div class=3D"text-xs font-medium text-success">+0.8%</div></div></button><=
button class=3D"group relative w-full p-2 bg-card border border-border roun=
ded-lg hover:border-primary/30 hover:bg-accent/50 transition-all duration-2=
00 text-left"><div class=3D"flex justify-between items-center"><div class=
=3D"font-sans text-xs font-medium text-foreground group-hover:text-primary =
transition-colors">NFLX</div><div class=3D"text-xs font-medium text-success=
">+4.1%</div></div></button><button class=3D"group relative w-full p-2 bg-c=
ard border border-border rounded-lg hover:border-primary/30 hover:bg-accent=
/50 transition-all duration-200 text-left"><div class=3D"flex justify-betwe=
en items-center"><div class=3D"font-sans text-xs font-medium text-foregroun=
d group-hover:text-primary transition-colors">DIS</div><div class=3D"text-x=
s font-medium text-destructive">-0.5%</div></div></button></div><button cla=
ss=3D"mt-2 w-full py-1.5 text-xs text-muted-foreground hover:text-primary t=
ransition-colors border-t border-border/50 pt-2">more</button></div></div><=
/div><div class=3D"rounded-lg text-card-foreground shadow-sm cursor-pointer=
 bg-gradient-card hover:shadow-elevated transition-all duration-300 hover:s=
cale-[1.02] group border"><div class=3D"flex h-full"><div class=3D"flex-1 p=
-6 flex flex-col gap-4"><div class=3D"flex items-start gap-3"><svg xmlns=3D=
"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 2=
4" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=
=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-sparkles h-5 w-=
5 text-primary flex-shrink-0 mt-1"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.=
063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.13=
5a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 =
0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"><=
/path><path d=3D"M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 =
17v2"></path><path d=3D"M5 18H3"></path></svg><div class=3D"flex-1 min-w-0"=
><h3 class=3D"font-bold text-base leading-tight line-clamp-2 group-hover:te=
xt-primary transition-colors mb-2">=E8=8B=B9=E6=9E=9CM4=E8=8A=AF=E7=89=87AI=
=E6=80=A7=E8=83=BD=E5=A4=A7=E5=B9=85=E6=8F=90=E5=8D=87=EF=BC=8C=E7=AB=AF=E4=
=BE=A7=E8=AE=A1=E7=AE=97=E7=88=86=E5=8F=91</h3><div class=3D"flex items-cen=
ter justify-between gap-2"><div class=3D"flex items-center gap-2"><div clas=
s=3D"inline-flex items-center rounded-full border font-semibold transition-=
colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 =
text-foreground text-xs px-2 py-1">=E7=AB=AF=E4=BE=A7=E8=8A=AF=E7=89=87</di=
v><div class=3D"inline-flex items-center rounded-full border font-semibold =
transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:rin=
g-offset-2 text-xs px-2 py-1 text-primary border-primary/30">=E6=8E=A8=E8=
=8D=90</div></div><div class=3D"flex items-center gap-1.5"><div class=3D"re=
lative"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24=
" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=
=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide l=
ucide-chart-column h-3.5 w-3.5 text-primary animate-pulse"><path d=3D"M3 3v=
16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 17V9"></path><path d=3D"M13 17V5=
"></path><path d=3D"M8 17v-3"></path></svg><div class=3D"absolute -top-1 -r=
ight-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div></div><sv=
g xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=
=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" str=
oke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-mail=
 h-3.5 w-3.5 text-muted-foreground hover:text-primary transition-colors cur=
sor-pointer"><rect width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"2"></re=
ct><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><div =
class=3D"flex items-center gap-1 cursor-pointer hover:text-primary transiti=
on-colors"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D=
"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-wid=
th=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide=
 lucide-message-circle h-3.5 w-3.5 text-muted-foreground"><path d=3D"M7.9 2=
0A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span class=3D"text-xs text-muted-fo=
reground">32</span></div></div></div></div></div><div class=3D"p-3 bg-gradi=
ent-to-r from-gray-50 to-gray-100 border border-gray-200/50 rounded-none"><=
div class=3D"flex items-start gap-2"><svg xmlns=3D"http://www.w3.org/2000/s=
vg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=
=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejo=
in=3D"round" class=3D"lucide lucide-sparkles h-4 w-4 text-primary mt-0.5 fl=
ex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 =
0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14=
.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2=
 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3=
v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=
=3D"M5 18H3"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text=
-sm text-foreground"><li class=3D"flex items-start justify-between gap-2"><=
div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary roun=
ded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=
=E5=88=A9=E5=A5=BD=E3=80=91=E8=8A=AF=E7=89=87=E6=80=A7=E8=83=BD=E5=A4=A7=E5=
=B9=85=E6=8F=90=E5=8D=87=EF=BC=8C=E4=B8=8B=E6=B8=B8=E5=BA=94=E7=94=A8=E9=9C=
=80=E6=B1=82=E6=BF=80=E5=A2=9E=EF=BC=8C=E4=BA=A7=E4=B8=9A=E9=93=BE=E9=BE=99=
=E5=A4=B4=E5=8F=97=E7=9B=8A=E6=98=8E=E6=98=BE</span></div><span class=3D"te=
xt-xs text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D</sp=
an></li><li class=3D"flex items-start justify-between gap-2"><div class=3D"=
flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2=
 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E8=B6=8B=E5=
=8A=BF=E3=80=91AI=E7=AE=97=E5=8A=9B=E9=9C=80=E6=B1=82=E6=8C=81=E7=BB=AD=E7=
=88=86=E5=8F=91=EF=BC=8C=E9=AB=98=E7=AB=AF=E8=8A=AF=E7=89=87=E4=BE=9B=E4=B8=
=8D=E5=BA=94=E6=B1=82=EF=BC=8C=E8=AE=AE=E4=BB=B7=E8=83=BD=E5=8A=9B=E5=A2=9E=
=E5=BC=BA</span></div><span class=3D"text-xs text-muted-foreground flex-shr=
ink-0">1=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-star=
t justify-between gap-2"><div class=3D"flex items-start gap-2"><span class=
=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=
=3D"line-clamp-1">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E5=9C=B0=E7=BC=98=E6=
=94=BF=E6=B2=BB=E5=BD=B1=E5=93=8D=E4=BE=9B=E5=BA=94=E9=93=BE=E7=A8=B3=E5=AE=
=9A=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8=E5=9B=BD=E9=99=85=E8=B4=B8=E6=98=93=
=E6=94=BF=E7=AD=96=E5=8F=98=E5=8C=96</span></div><span class=3D"text-xs tex=
t-muted-foreground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span></li>=
</ul></div></div></div><div class=3D"space-y-2"><div class=3D"flex items-st=
art gap-2 px-[15px]"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24"=
 height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" =
stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=
=3D"lucide lucide-message-square h-4 w-4 text-muted-foreground mt-0.5 flex-=
shrink-0"><path d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0=
 1 2 2z"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm =
text-foreground"><li class=3D"flex items-start justify-between gap-2"><div =
class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground=
 rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1=
">M4=E8=8A=AF=E7=89=87AI benchmarks=E5=85=AC=E5=B8=83=EF=BC=8CNeural Engine=
=E6=80=A7=E8=83=BD=E6=8F=90=E5=8D=87300%</span></div><span class=3D"text-xs=
 text-muted-foreground flex-shrink-0">1=E5=B0=8F=E6=97=B6=E5=89=8D</span></=
li><li class=3D"flex items-start justify-between gap-2"><div class=3D"flex =
items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-full =
mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">iPhone 16 Pr=
o=E7=B3=BB=E5=88=97M4=E8=8A=AF=E7=89=87=E8=AE=A2=E5=8D=95=E9=87=8F=E8=B6=85=
=E9=A2=84=E6=9C=9F=EF=BC=8C=E4=BE=9B=E5=BA=94=E9=93=BE=E7=B4=A7=E5=BC=A0</s=
pan></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">4=E5=
=B0=8F=E6=97=B6=E5=89=8D</span></li></ul></div></div></div></div><div class=
=3D"w-32 border-l bg-muted/20 p-3 flex flex-col"><div class=3D"text-xs text=
-muted-foreground mb-2 font-medium">Following</div><div class=3D"space-y-1.=
5 flex-1"><button class=3D"group relative w-full p-2 bg-card border border-=
border rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all=
 duration-200 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-=
destructive rounded-full"></div><div class=3D"flex justify-between items-ce=
nter"><div class=3D"font-sans text-xs font-medium text-foreground group-hov=
er:text-primary transition-colors">AAPL</div><div class=3D"text-xs font-med=
ium text-success">****%</div></div></button><button class=3D"group relative=
 w-full p-2 bg-card border border-border rounded-lg hover:border-primary/30=
 hover:bg-accent/50 transition-all duration-200 text-left"><div class=3D"fl=
ex justify-between items-center"><div class=3D"font-sans text-xs font-mediu=
m text-foreground group-hover:text-primary transition-colors">TSM</div><div=
 class=3D"text-xs font-medium text-destructive">-1.2%</div></div></button><=
button class=3D"group relative w-full p-2 bg-card border border-border roun=
ded-lg hover:border-primary/30 hover:bg-accent/50 transition-all duration-2=
00 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive=
 rounded-full"></div><div class=3D"flex justify-between items-center"><div =
class=3D"font-sans text-xs font-medium text-foreground group-hover:text-pri=
mary transition-colors">NVDA</div><div class=3D"text-xs font-medium text-su=
ccess">+0.8%</div></div></button><button class=3D"group relative w-full p-2=
 bg-card border border-border rounded-lg hover:border-primary/30 hover:bg-a=
ccent/50 transition-all duration-200 text-left"><div class=3D"flex justify-=
between items-center"><div class=3D"font-sans text-xs font-medium text-fore=
ground group-hover:text-primary transition-colors">ARM</div><div class=3D"t=
ext-xs font-medium text-success">+4.1%</div></div></button><button class=3D=
"group relative w-full p-2 bg-card border border-border rounded-lg hover:bo=
rder-primary/30 hover:bg-accent/50 transition-all duration-200 text-left"><=
div class=3D"flex justify-between items-center"><div class=3D"font-sans tex=
t-xs font-medium text-foreground group-hover:text-primary transition-colors=
">QCOM</div><div class=3D"text-xs font-medium text-destructive">-0.5%</div>=
</div></button></div><button class=3D"mt-2 w-full py-1.5 text-xs text-muted=
-foreground hover:text-primary transition-colors border-t border-border/50 =
pt-2">more</button></div></div></div><div class=3D"rounded-lg text-card-for=
eground shadow-sm cursor-pointer bg-gradient-card hover:shadow-elevated tra=
nsition-all duration-300 hover:scale-[1.02] group border"><div class=3D"fle=
x h-full"><div class=3D"flex-1 p-6 flex flex-col gap-4"><div class=3D"flex =
items-start gap-3"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" h=
eight=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" st=
roke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=
=3D"lucide lucide-sparkles h-5 w-5 text-primary flex-shrink-0 mt-1"><path d=
=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.9=
36A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 1=
5.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-=
1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path><path d=
=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18H3"></path>=
</svg><div class=3D"flex-1 min-w-0"><h3 class=3D"font-bold text-base leadin=
g-tight line-clamp-2 group-hover:text-primary transition-colors mb-2">FDA=
=E6=89=B9=E5=87=86=E9=A6=96=E6=AC=BEAI=E8=BE=85=E5=8A=A9=E8=AF=8A=E6=96=AD=
=E8=AE=BE=E5=A4=87=E5=A4=A7=E8=A7=84=E6=A8=A1=E5=BA=94=E7=94=A8</h3><div cl=
ass=3D"flex items-center justify-between gap-2"><div class=3D"flex items-ce=
nter gap-2"><div class=3D"inline-flex items-center rounded-full border font=
-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring=
 focus:ring-offset-2 text-foreground text-xs px-2 py-1">=E5=8C=BB=E7=96=97A=
I</div><div class=3D"inline-flex items-center rounded-full border font-semi=
bold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focu=
s:ring-offset-2 text-xs px-2 py-1 text-primary border-primary/30">=E6=8E=A8=
=E8=8D=90</div></div><div class=3D"flex items-center gap-1.5"><div class=3D=
"relative"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D=
"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-wid=
th=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide=
 lucide-chart-column h-3.5 w-3.5 text-primary animate-pulse"><path d=3D"M3 =
3v16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 17V9"></path><path d=3D"M13 17=
V5"></path><path d=3D"M8 17v-3"></path></svg><div class=3D"absolute -top-1 =
-right-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div></div><=
svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=
=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" str=
oke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-mail=
 h-3.5 w-3.5 text-muted-foreground hover:text-primary transition-colors cur=
sor-pointer"><rect width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"2"></re=
ct><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><div =
class=3D"flex items-center gap-1 cursor-pointer hover:text-primary transiti=
on-colors"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D=
"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-wid=
th=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide=
 lucide-message-circle h-3.5 w-3.5 text-muted-foreground"><path d=3D"M7.9 2=
0A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span class=3D"text-xs text-muted-fo=
reground">30</span></div></div></div></div></div><div class=3D"p-3 bg-gradi=
ent-to-r from-gray-50 to-gray-100 border border-gray-200/50 rounded-none"><=
div class=3D"flex items-start gap-2"><svg xmlns=3D"http://www.w3.org/2000/s=
vg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=
=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejo=
in=3D"round" class=3D"lucide lucide-sparkles h-4 w-4 text-primary mt-0.5 fl=
ex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 =
0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14=
.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2=
 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3=
v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=
=3D"M5 18H3"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text=
-sm text-foreground"><li class=3D"flex items-start justify-between gap-2"><=
div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary roun=
ded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=
=E5=88=A9=E5=A5=BD=E3=80=91=E4=BC=81=E4=B8=9A=E6=95=B0=E5=AD=97=E5=8C=96=E8=
=BD=AC=E5=9E=8B=E5=8A=A0=E9=80=9F=EF=BC=8CAI=E5=BA=94=E7=94=A8=E6=B8=97=E9=
=80=8F=E7=8E=87=E5=BF=AB=E9=80=9F=E6=8F=90=E5=8D=87=EF=BC=8C=E5=95=86=E4=B8=
=9A=E5=8C=96=E8=BF=9B=E7=A8=8B=E6=98=8E=E7=A1=AE</span></div><span class=3D=
"text-xs text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D<=
/span></li><li class=3D"flex items-start justify-between gap-2"><div class=
=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full =
mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E8=B6=8B=
=E5=8A=BF=E3=80=91AI Agent=E6=88=90=E4=B8=BA=E6=96=B0=E5=A2=9E=E9=95=BF=E7=
=82=B9=EF=BC=8CB=E7=AB=AF=E5=B8=82=E5=9C=BA=E7=88=86=E5=8F=91=EF=BC=8C=E8=
=AE=A2=E9=98=85=E6=A8=A1=E5=BC=8F=E9=AA=8C=E8=AF=81=E6=88=90=E5=8A=9F</span=
></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=B0=
=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-betw=
een gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-=
primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1=
">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E7=9B=91=E7=AE=A1=E6=94=BF=E7=AD=96=
=E8=B6=8B=E4=B8=A5=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8AI=E5=AE=89=E5=85=A8=
=E5=92=8C=E6=95=B0=E6=8D=AE=E9=9A=90=E7=A7=81=E7=9B=B8=E5=85=B3=E6=B3=95=E8=
=A7=84=E5=BD=B1=E5=93=8D</span></div><span class=3D"text-xs text-muted-fore=
ground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span></li></ul></div><=
/div></div><div class=3D"space-y-2"><div class=3D"flex items-start gap-2 px=
-[15px]"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"2=
4" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=
=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide l=
ucide-message-square h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0"><p=
ath d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></p=
ath></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-foregro=
und"><li class=3D"flex items-start justify-between gap-2"><div class=3D"fle=
x items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-ful=
l mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">FDA=E7=BB=
=BF=E8=89=B2=E9=80=9A=E9=81=93=E6=89=B9=E5=87=86=E9=A6=96=E6=AC=BEAI=E8=82=
=BA=E7=99=8C=E7=AD=9B=E6=9F=A5=E8=AE=BE=E5=A4=87=EF=BC=8C=E9=A2=84=E8=AE=A1=
=E5=B9=B4=E5=86=85=E4=B8=8A=E5=B8=82</span></div><span class=3D"text-xs tex=
t-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><=
li class=3D"flex items-start justify-between gap-2"><div class=3D"flex item=
s-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-full mt-2=
 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">=E5=BC=BA=E7=94=
=9F=E5=85=AC=E5=8F=B8=E5=AE=A3=E5=B8=83AI=E8=AF=8A=E6=96=AD=E8=AE=BE=E5=A4=
=87=E8=AE=A2=E5=8D=95=E7=AA=81=E7=A0=B410=E4=B8=87=E5=8F=B0</span></div><sp=
an class=3D"text-xs text-muted-foreground flex-shrink-0">5=E5=B0=8F=E6=97=
=B6=E5=89=8D</span></li></ul></div></div></div></div><div class=3D"w-32 bor=
der-l bg-muted/20 p-3 flex flex-col"><div class=3D"text-xs text-muted-foreg=
round mb-2 font-medium">Following</div><div class=3D"space-y-1.5 flex-1"><b=
utton class=3D"group relative w-full p-2 bg-card border border-border round=
ed-lg hover:border-primary/30 hover:bg-accent/50 transition-all duration-20=
0 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive =
rounded-full"></div><div class=3D"flex justify-between items-center"><div c=
lass=3D"font-sans text-xs font-medium text-foreground group-hover:text-prim=
ary transition-colors">JNJ</div><div class=3D"text-xs font-medium text-succ=
ess">****%</div></div></button><button class=3D"group relative w-full p-2 b=
g-card border border-border rounded-lg hover:border-primary/30 hover:bg-acc=
ent/50 transition-all duration-200 text-left"><div class=3D"flex justify-be=
tween items-center"><div class=3D"font-sans text-xs font-medium text-foregr=
ound group-hover:text-primary transition-colors">PFE</div><div class=3D"tex=
t-xs font-medium text-destructive">-1.2%</div></div></button><button class=
=3D"group relative w-full p-2 bg-card border border-border rounded-lg hover=
:border-primary/30 hover:bg-accent/50 transition-all duration-200 text-left=
"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-ful=
l"></div><div class=3D"flex justify-between items-center"><div class=3D"fon=
t-sans text-xs font-medium text-foreground group-hover:text-primary transit=
ion-colors">NVDA</div><div class=3D"text-xs font-medium text-success">+0.8%=
</div></div></button><button class=3D"group relative w-full p-2 bg-card bor=
der border-border rounded-lg hover:border-primary/30 hover:bg-accent/50 tra=
nsition-all duration-200 text-left"><div class=3D"flex justify-between item=
s-center"><div class=3D"font-sans text-xs font-medium text-foreground group=
-hover:text-primary transition-colors">UNH</div><div class=3D"text-xs font-=
medium text-success">+4.1%</div></div></button><button class=3D"group relat=
ive w-full p-2 bg-card border border-border rounded-lg hover:border-primary=
/30 hover:bg-accent/50 transition-all duration-200 text-left"><div class=3D=
"flex justify-between items-center"><div class=3D"font-sans text-xs font-me=
dium text-foreground group-hover:text-primary transition-colors">TMO</div><=
div class=3D"text-xs font-medium text-destructive">-0.5%</div></div></butto=
n></div><button class=3D"mt-2 w-full py-1.5 text-xs text-muted-foreground h=
over:text-primary transition-colors border-t border-border/50 pt-2">more</b=
utton></div></div></div><div class=3D"rounded-lg text-card-foreground shado=
w-sm cursor-pointer bg-gradient-card hover:shadow-elevated transition-all d=
uration-300 hover:scale-[1.02] group border"><div class=3D"flex h-full"><di=
v class=3D"flex-1 p-6 flex flex-col gap-4"><div class=3D"flex items-start g=
ap-3"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" =
viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D=
"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide luci=
de-sparkles h-5 w-5 text-primary flex-shrink-0 mt-1"><path d=3D"M9.937 15.5=
A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.=
937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135=
 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.=
5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path><path d=3D"M22 5h-4"></p=
ath><path d=3D"M4 17v2"></path><path d=3D"M5 18H3"></path></svg><div class=
=3D"flex-1 min-w-0"><h3 class=3D"font-bold text-base leading-tight line-cla=
mp-2 group-hover:text-primary transition-colors mb-2">=E7=89=B9=E6=96=AF=E6=
=8B=89FSD V13=E7=89=88=E6=9C=AC=E5=AE=9E=E7=8E=B0=E5=AE=8C=E5=85=A8=E8=87=
=AA=E5=8A=A8=E9=A9=BE=E9=A9=B6</h3><div class=3D"flex items-center justify-=
between gap-2"><div class=3D"flex items-center gap-2"><div class=3D"inline-=
flex items-center rounded-full border font-semibold transition-colors focus=
:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foregro=
und text-xs px-2 py-1">=E8=87=AA=E5=8A=A8=E9=A9=BE=E9=A9=B6</div><div class=
=3D"inline-flex items-center rounded-full border font-semibold transition-c=
olors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 t=
ext-xs px-2 py-1 text-primary border-primary/30">=E6=8E=A8=E8=8D=90</div></=
div><div class=3D"flex items-center gap-1.5"><div class=3D"relative"><svg x=
mlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0=
 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-l=
inecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-chart-col=
umn h-3.5 w-3.5 text-primary animate-pulse"><path d=3D"M3 3v16a2 2 0 0 0 2 =
2h16"></path><path d=3D"M18 17V9"></path><path d=3D"M13 17V5"></path><path =
d=3D"M8 17v-3"></path></svg><div class=3D"absolute -top-1 -right-1 w-1.5 h-=
1.5 bg-green-500 rounded-full animate-pulse"></div></div><svg xmlns=3D"http=
://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fi=
ll=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"ro=
und" stroke-linejoin=3D"round" class=3D"lucide lucide-mail h-3.5 w-3.5 text=
-muted-foreground hover:text-primary transition-colors cursor-pointer"><rec=
t width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"2"></rect><path d=3D"m22=
 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><div class=3D"flex ite=
ms-center gap-1 cursor-pointer hover:text-primary transition-colors"><svg x=
mlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0=
 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-l=
inecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-message-c=
ircle h-3.5 w-3.5 text-muted-foreground"><path d=3D"M7.9 20A9 9 0 1 0 4 16.=
1L2 22Z"></path></svg><span class=3D"text-xs text-muted-foreground">6</span=
></div></div></div></div></div><div class=3D"p-3 bg-gradient-to-r from-gray=
-50 to-gray-100 border border-gray-200/50 rounded-none"><div class=3D"flex =
items-start gap-2"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" h=
eight=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" st=
roke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=
=3D"lucide lucide-sparkles h-4 w-4 text-primary mt-0.5 flex-shrink-0"><path=
 d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9=
.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0=
 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437=
l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path><path d=
=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18H3"></path>=
</svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-foreground"=
><li class=3D"flex items-start justify-between gap-2"><div class=3D"flex it=
ems-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-s=
hrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E5=88=A9=E5=A5=BD=E3=
=80=91=E6=8A=80=E6=9C=AF=E7=AA=81=E7=A0=B4=E5=B8=A6=E6=9D=A5=E5=95=86=E4=B8=
=9A=E5=8C=96=E6=9C=BA=E9=81=87=EF=BC=8C=E5=87=BA=E8=A1=8C=E6=9C=8D=E5=8A=A1=
=E5=B8=82=E5=9C=BA=E7=A9=BA=E9=97=B4=E5=B7=A8=E5=A4=A7</span></div><span cl=
ass=3D"text-xs text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=
=89=8D</span></li><li class=3D"flex items-start justify-between gap-2"><div=
 class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded=
-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E8=
=B6=8B=E5=8A=BF=E3=80=91L4=E7=BA=A7=E8=87=AA=E5=8A=A8=E9=A9=BE=E9=A9=B6=E8=
=A7=84=E6=A8=A1=E5=BA=94=E7=94=A8=E5=9C=A8=E5=8D=B3=EF=BC=8C=E4=BA=A7=E4=B8=
=9A=E9=93=BE=E4=BB=B7=E5=80=BC=E9=87=8D=E6=9E=84=E5=8A=A0=E9=80=9F</span></=
div><span class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=B0=8F=
=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-between=
 gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-pri=
mary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=
=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E5=AE=89=E5=85=A8=E7=9B=91=E7=AE=A1=E8=
=A6=81=E6=B1=82=E6=8F=90=E9=AB=98=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8=E6=94=
=BF=E7=AD=96=E5=AE=A1=E6=89=B9=E5=92=8C=E8=B4=A3=E4=BB=BB=E8=AE=A4=E5=AE=9A=
=E9=97=AE=E9=A2=98</span></div><span class=3D"text-xs text-muted-foreground=
 flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span></li></ul></div></div><=
/div><div class=3D"space-y-2"><div class=3D"flex items-start gap-2 px-[15px=
]"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" vie=
wBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2"=
 stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-=
message-square h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0"><path d=
=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path><=
/svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-foreground">=
<li class=3D"flex items-start justify-between gap-2"><div class=3D"flex ite=
ms-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-full mt-=
2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">=E9=A9=AC=E6=96=
=AF=E5=85=8B=E5=AE=A3=E5=B8=83FSD=E8=AE=A2=E9=98=85=E7=94=A8=E6=88=B7=E7=AA=
=81=E7=A0=B4100=E4=B8=87=EF=BC=8C=E6=9C=88=E6=94=B6=E5=85=A5=E8=BE=BE30=E4=
=BA=BF=E7=BE=8E=E5=85=83</span></div><span class=3D"text-xs text-muted-fore=
ground flex-shrink-0">3=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"=
flex items-start justify-between gap-2"><div class=3D"flex items-start gap-=
2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink=
-0"></span><span class=3D"line-clamp-1 flex-1">FSD V13=E8=8E=B7=E5=BE=97=E5=
=8A=A0=E5=B7=9EDMV=E5=AE=8C=E5=85=A8=E8=87=AA=E5=8A=A8=E9=A9=BE=E9=A9=B6=E6=
=B5=8B=E8=AF=95=E8=AE=B8=E5=8F=AF</span></div><span class=3D"text-xs text-m=
uted-foreground flex-shrink-0">6=E5=B0=8F=E6=97=B6=E5=89=8D</span></li></ul=
></div></div></div></div><div class=3D"w-32 border-l bg-muted/20 p-3 flex f=
lex-col"><div class=3D"text-xs text-muted-foreground mb-2 font-medium">Foll=
owing</div><div class=3D"space-y-1.5 flex-1"><button class=3D"group relativ=
e w-full p-2 bg-card border border-border rounded-lg hover:border-primary/3=
0 hover:bg-accent/50 transition-all duration-200 text-left"><div class=3D"a=
bsolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div><div cla=
ss=3D"flex justify-between items-center"><div class=3D"font-sans text-xs fo=
nt-medium text-foreground group-hover:text-primary transition-colors">TSLA<=
/div><div class=3D"text-xs font-medium text-success">****%</div></div></but=
ton><button class=3D"group relative w-full p-2 bg-card border border-border=
 rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all durat=
ion-200 text-left"><div class=3D"flex justify-between items-center"><div cl=
ass=3D"font-sans text-xs font-medium text-foreground group-hover:text-prima=
ry transition-colors">NVDA</div><div class=3D"text-xs font-medium text-dest=
ructive">-1.2%</div></div></button><button class=3D"group relative w-full p=
-2 bg-card border border-border rounded-lg hover:border-primary/30 hover:bg=
-accent/50 transition-all duration-200 text-left"><div class=3D"absolute -t=
op-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div><div class=3D"flex=
 justify-between items-center"><div class=3D"font-sans text-xs font-medium =
text-foreground group-hover:text-primary transition-colors">AMD</div><div c=
lass=3D"text-xs font-medium text-success">+0.8%</div></div></button><button=
 class=3D"group relative w-full p-2 bg-card border border-border rounded-lg=
 hover:border-primary/30 hover:bg-accent/50 transition-all duration-200 tex=
t-left"><div class=3D"flex justify-between items-center"><div class=3D"font=
-sans text-xs font-medium text-foreground group-hover:text-primary transiti=
on-colors">MOBILEYE</div><div class=3D"text-xs font-medium text-success">+4=
.1%</div></div></button><button class=3D"group relative w-full p-2 bg-card =
border border-border rounded-lg hover:border-primary/30 hover:bg-accent/50 =
transition-all duration-200 text-left"><div class=3D"flex justify-between i=
tems-center"><div class=3D"font-sans text-xs font-medium text-foreground gr=
oup-hover:text-primary transition-colors">GM</div><div class=3D"text-xs fon=
t-medium text-destructive">-0.5%</div></div></button></div><button class=3D=
"mt-2 w-full py-1.5 text-xs text-muted-foreground hover:text-primary transi=
tion-colors border-t border-border/50 pt-2">more</button></div></div></div>=
<div class=3D"rounded-lg text-card-foreground shadow-sm cursor-pointer bg-g=
radient-card hover:shadow-elevated transition-all duration-300 hover:scale-=
[1.02] group border"><div class=3D"flex h-full"><div class=3D"flex-1 p-6 fl=
ex flex-col gap-4"><div class=3D"flex items-start gap-3"><svg xmlns=3D"http=
://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fi=
ll=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"ro=
und" stroke-linejoin=3D"round" class=3D"lucide lucide-sparkles h-5 w-5 text=
-primary flex-shrink-0 mt-1"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6=
.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5=
 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964=
L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>=
<path d=3D"M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2">=
</path><path d=3D"M5 18H3"></path></svg><div class=3D"flex-1 min-w-0"><h3 c=
lass=3D"font-bold text-base leading-tight line-clamp-2 group-hover:text-pri=
mary transition-colors mb-2">=E9=87=8F=E5=AD=90=E8=AE=A1=E7=AE=97IBM=E7=AA=
=81=E7=A0=B41000=E9=87=8F=E5=AD=90=E6=AF=94=E7=89=B9=E9=87=8C=E7=A8=8B=E7=
=A2=91</h3><div class=3D"flex items-center justify-between gap-2"><div clas=
s=3D"flex items-center gap-2"><div class=3D"inline-flex items-center rounde=
d-full border font-semibold transition-colors focus:outline-none focus:ring=
-2 focus:ring-ring focus:ring-offset-2 text-foreground text-xs px-2 py-1">=
=E9=87=8F=E5=AD=90=E8=AE=A1=E7=AE=97</div><div class=3D"inline-flex items-c=
enter rounded-full border font-semibold transition-colors focus:outline-non=
e focus:ring-2 focus:ring-ring focus:ring-offset-2 text-xs px-2 py-1 text-p=
rimary border-primary/30">=E6=8E=A8=E8=8D=90</div></div><div class=3D"flex =
items-center gap-1.5"><div class=3D"relative"><svg xmlns=3D"http://www.w3.o=
rg/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none"=
 stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke=
-linejoin=3D"round" class=3D"lucide lucide-chart-column h-3.5 w-3.5 text-pr=
imary animate-pulse"><path d=3D"M3 3v16a2 2 0 0 0 2 2h16"></path><path d=3D=
"M18 17V9"></path><path d=3D"M13 17V5"></path><path d=3D"M8 17v-3"></path><=
/svg><div class=3D"absolute -top-1 -right-1 w-1.5 h-1.5 bg-green-500 rounde=
d-full animate-pulse"></div></div><svg xmlns=3D"http://www.w3.org/2000/svg"=
 width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"c=
urrentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D=
"round" class=3D"lucide lucide-mail h-3.5 w-3.5 text-muted-foreground hover=
:text-primary transition-colors cursor-pointer"><rect width=3D"20" height=
=3D"16" x=3D"2" y=3D"4" rx=3D"2"></rect><path d=3D"m22 7-8.97 5.7a1.94 1.94=
 0 0 1-2.06 0L2 7"></path></svg><div class=3D"flex items-center gap-1 curso=
r-pointer hover:text-primary transition-colors"><svg xmlns=3D"http://www.w3=
.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"non=
e" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stro=
ke-linejoin=3D"round" class=3D"lucide lucide-message-circle h-3.5 w-3.5 tex=
t-muted-foreground"><path d=3D"M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg=
><span class=3D"text-xs text-muted-foreground">13</span></div></div></div><=
/div></div><div class=3D"p-3 bg-gradient-to-r from-gray-50 to-gray-100 bord=
er border-gray-200/50 rounded-none"><div class=3D"flex items-start gap-2"><=
svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=
=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" str=
oke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-spar=
kles h-4 w-4 text-primary mt-0.5 flex-shrink-0"><path d=3D"M9.937 15.5A2 2 =
0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8=
.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.58=
1a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0=
 1-.963 0z"></path><path d=3D"M20 3v4"></path><path d=3D"M22 5h-4"></path><=
path d=3D"M4 17v2"></path><path d=3D"M5 18H3"></path></svg><div class=3D"fl=
ex-1"><ul class=3D"space-y-2 text-sm text-foreground"><li class=3D"flex ite=
ms-start justify-between gap-2"><div class=3D"flex items-start gap-2"><span=
 class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span =
class=3D"line-clamp-1">=E3=80=90=E5=88=A9=E5=A5=BD=E3=80=91=E6=8A=80=E6=9C=
=AF=E7=AA=81=E7=A0=B4=E9=AA=8C=E8=AF=81=E5=95=86=E4=B8=9A=E4=BB=B7=E5=80=BC=
=EF=BC=8C=E6=94=BF=E5=BA=9C=E5=92=8C=E4=BC=81=E4=B8=9A=E6=8A=95=E8=B5=84=E5=
=8A=A0=E9=80=9F</span></div><span class=3D"text-xs text-muted-foreground fl=
ex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex item=
s-start justify-between gap-2"><div class=3D"flex items-start gap-2"><span =
class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span c=
lass=3D"line-clamp-1">=E3=80=90=E8=B6=8B=E5=8A=BF=E3=80=91=E7=89=B9=E5=AE=
=9A=E5=9C=BA=E6=99=AF=E5=BA=94=E7=94=A8=E8=90=BD=E5=9C=B0=EF=BC=8C=E9=87=91=
=E8=9E=8D=E3=80=81=E5=88=B6=E8=8D=AF=E3=80=81=E6=9D=90=E6=96=99=E7=AD=89=E9=
=A2=86=E5=9F=9F=E9=9C=80=E6=B1=82=E6=98=8E=E7=A1=AE</span></div><span class=
=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=B0=8F=E6=97=B6=E5=89=
=8D</span></li><li class=3D"flex items-start justify-between gap-2"><div cl=
ass=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-fu=
ll mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E9=A3=
=8E=E9=99=A9=E3=80=91=E6=8A=80=E6=9C=AF=E4=BB=8D=E5=A4=84=E6=97=A9=E6=9C=9F=
=E9=98=B6=E6=AE=B5=EF=BC=8C=E5=95=86=E4=B8=9A=E5=8C=96=E6=97=B6=E9=97=B4=E8=
=A1=A8=E5=AD=98=E5=9C=A8=E4=B8=8D=E7=A1=AE=E5=AE=9A=E6=80=A7</span></div><s=
pan class=3D"text-xs text-muted-foreground flex-shrink-0">30=E5=88=86=E9=92=
=9F=E5=89=8D</span></li></ul></div></div></div><div class=3D"space-y-2"><di=
v class=3D"flex items-start gap-2 px-[15px]"><svg xmlns=3D"http://www.w3.or=
g/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" =
stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-=
linejoin=3D"round" class=3D"lucide lucide-message-square h-4 w-4 text-muted=
-foreground mt-0.5 flex-shrink-0"><path d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a=
2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg><div class=3D"flex-1"><ul cla=
ss=3D"space-y-2 text-sm text-foreground"><li class=3D"flex items-start just=
ify-between gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1=
 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></span><span clas=
s=3D"line-clamp-1 flex-1">IBM Condor=E9=87=8F=E5=AD=90=E5=A4=84=E7=90=86=E5=
=99=A8=E6=AD=A3=E5=BC=8F=E5=8F=91=E5=B8=83=EF=BC=8C1121=E9=87=8F=E5=AD=90=
=E6=AF=94=E7=89=B9=E5=88=9B=E7=BA=AA=E5=BD=95</span></div><span class=3D"te=
xt-xs text-muted-foreground flex-shrink-0">4=E5=B0=8F=E6=97=B6=E5=89=8D</sp=
an></li><li class=3D"flex items-start justify-between gap-2"><div class=3D"=
flex items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-=
full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">=E6=91=
=A9=E6=A0=B9=E5=A4=A7=E9=80=9A=E4=B8=8EIBM=E7=AD=BE=E7=BD=B2=E9=87=8F=E5=AD=
=90=E8=AE=A1=E7=AE=97=E9=87=91=E8=9E=8D=E5=BB=BA=E6=A8=A1=E6=9C=8D=E5=8A=A1=
=E5=8D=8F=E8=AE=AE</span></div><span class=3D"text-xs text-muted-foreground=
 flex-shrink-0">7=E5=B0=8F=E6=97=B6=E5=89=8D</span></li></ul></div></div></=
div></div><div class=3D"w-32 border-l bg-muted/20 p-3 flex flex-col"><div c=
lass=3D"text-xs text-muted-foreground mb-2 font-medium">Following</div><div=
 class=3D"space-y-1.5 flex-1"><button class=3D"group relative w-full p-2 bg=
-card border border-border rounded-lg hover:border-primary/30 hover:bg-acce=
nt/50 transition-all duration-200 text-left"><div class=3D"absolute -top-1 =
-right-1 w-2 h-2 bg-destructive rounded-full"></div><div class=3D"flex just=
ify-between items-center"><div class=3D"font-sans text-xs font-medium text-=
foreground group-hover:text-primary transition-colors">IBM</div><div class=
=3D"text-xs font-medium text-success">****%</div></div></button><button cla=
ss=3D"group relative w-full p-2 bg-card border border-border rounded-lg hov=
er:border-primary/30 hover:bg-accent/50 transition-all duration-200 text-le=
ft"><div class=3D"flex justify-between items-center"><div class=3D"font-san=
s text-xs font-medium text-foreground group-hover:text-primary transition-c=
olors">GOOGL</div><div class=3D"text-xs font-medium text-destructive">-1.2%=
</div></div></button><button class=3D"group relative w-full p-2 bg-card bor=
der border-border rounded-lg hover:border-primary/30 hover:bg-accent/50 tra=
nsition-all duration-200 text-left"><div class=3D"absolute -top-1 -right-1 =
w-2 h-2 bg-destructive rounded-full"></div><div class=3D"flex justify-betwe=
en items-center"><div class=3D"font-sans text-xs font-medium text-foregroun=
d group-hover:text-primary transition-colors">MSFT</div><div class=3D"text-=
xs font-medium text-success">+0.8%</div></div></button><button class=3D"gro=
up relative w-full p-2 bg-card border border-border rounded-lg hover:border=
-primary/30 hover:bg-accent/50 transition-all duration-200 text-left"><div =
class=3D"flex justify-between items-center"><div class=3D"font-sans text-xs=
 font-medium text-foreground group-hover:text-primary transition-colors">IN=
TC</div><div class=3D"text-xs font-medium text-success">+4.1%</div></div></=
button><button class=3D"group relative w-full p-2 bg-card border border-bor=
der rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all du=
ration-200 text-left"><div class=3D"flex justify-between items-center"><div=
 class=3D"font-sans text-xs font-medium text-foreground group-hover:text-pr=
imary transition-colors">AMZN</div><div class=3D"text-xs font-medium text-d=
estructive">-0.5%</div></div></button></div><button class=3D"mt-2 w-full py=
-1.5 text-xs text-muted-foreground hover:text-primary transition-colors bor=
der-t border-border/50 pt-2">more</button></div></div></div><div class=3D"r=
ounded-lg text-card-foreground shadow-sm cursor-pointer bg-gradient-card ho=
ver:shadow-elevated transition-all duration-300 hover:scale-[1.02] group bo=
rder"><div class=3D"flex h-full"><div class=3D"flex-1 p-6 flex flex-col gap=
-4"><div class=3D"flex items-start gap-3"><svg xmlns=3D"http://www.w3.org/2=
000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" str=
oke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-lin=
ejoin=3D"round" class=3D"lucide lucide-sparkles h-5 w-5 text-primary flex-s=
hrink-0 mt-1"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5=
 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L1=
4.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 =
2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 =
3v4"></path><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=
=3D"M5 18H3"></path></svg><div class=3D"flex-1 min-w-0"><h3 class=3D"font-b=
old text-base leading-tight line-clamp-2 group-hover:text-primary transitio=
n-colors mb-2">=E5=BE=AE=E8=BD=AFCopilot=E4=BC=81=E4=B8=9A=E7=89=88=E7=94=
=A8=E6=88=B7=E6=95=B0=E7=AA=81=E7=A0=B4=E5=8D=83=E4=B8=87=E7=BA=A7=E5=88=AB=
</h3><div class=3D"flex items-center justify-between gap-2"><div class=3D"f=
lex items-center gap-2"><div class=3D"inline-flex items-center rounded-full=
 border font-semibold transition-colors focus:outline-none focus:ring-2 foc=
us:ring-ring focus:ring-offset-2 text-foreground text-xs px-2 py-1">=E4=BC=
=81=E4=B8=9A=E8=BD=AF=E4=BB=B6</div><div class=3D"inline-flex items-center =
rounded-full border font-semibold transition-colors focus:outline-none focu=
s:ring-2 focus:ring-ring focus:ring-offset-2 text-xs px-2 py-1 text-primary=
 border-primary/30">=E6=8E=A8=E8=8D=90</div></div><div class=3D"flex items-=
center gap-1.5"><div class=3D"relative"><svg xmlns=3D"http://www.w3.org/200=
0/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" strok=
e=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linej=
oin=3D"round" class=3D"lucide lucide-chart-column h-3.5 w-3.5 text-primary =
animate-pulse"><path d=3D"M3 3v16a2 2 0 0 0 2 2h16"></path><path d=3D"M18 1=
7V9"></path><path d=3D"M13 17V5"></path><path d=3D"M8 17v-3"></path></svg><=
div class=3D"absolute -top-1 -right-1 w-1.5 h-1.5 bg-green-500 rounded-full=
 animate-pulse"></div></div><svg xmlns=3D"http://www.w3.org/2000/svg" width=
=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"lucide lucide-mail h-3.5 w-3.5 text-muted-foreground hover:text-=
primary transition-colors cursor-pointer"><rect width=3D"20" height=3D"16" =
x=3D"2" y=3D"4" rx=3D"2"></rect><path d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2=
.06 0L2 7"></path></svg><div class=3D"flex items-center gap-1 cursor-pointe=
r hover:text-primary transition-colors"><svg xmlns=3D"http://www.w3.org/200=
0/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" strok=
e=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linej=
oin=3D"round" class=3D"lucide lucide-message-circle h-3.5 w-3.5 text-muted-=
foreground"><path d=3D"M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg><span c=
lass=3D"text-xs text-muted-foreground">4</span></div></div></div></div></di=
v><div class=3D"p-3 bg-gradient-to-r from-gray-50 to-gray-100 border border=
-gray-200/50 rounded-none"><div class=3D"flex items-start gap-2"><svg xmlns=
=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 2=
4 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linec=
ap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-sparkles h-4 =
w-4 text-primary mt-0.5 flex-shrink-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5=
 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-=
6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 =
0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0=
z"></path><path d=3D"M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=3D=
"M4 17v2"></path><path d=3D"M5 18H3"></path></svg><div class=3D"flex-1"><ul=
 class=3D"space-y-2 text-sm text-foreground"><li class=3D"flex items-start =
justify-between gap-2"><div class=3D"flex items-start gap-2"><span class=3D=
"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"=
line-clamp-1">=E3=80=90=E5=88=A9=E5=A5=BD=E3=80=91=E4=BC=81=E4=B8=9A=E6=95=
=B0=E5=AD=97=E5=8C=96=E8=BD=AC=E5=9E=8B=E5=8A=A0=E9=80=9F=EF=BC=8CAI=E5=BA=
=94=E7=94=A8=E6=B8=97=E9=80=8F=E7=8E=87=E5=BF=AB=E9=80=9F=E6=8F=90=E5=8D=87=
=EF=BC=8C=E5=95=86=E4=B8=9A=E5=8C=96=E8=BF=9B=E7=A8=8B=E6=98=8E=E7=A1=AE</s=
pan></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">2=E5=
=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-b=
etween gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 =
bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clam=
p-1">=E3=80=90=E8=B6=8B=E5=8A=BF=E3=80=91AI Agent=E6=88=90=E4=B8=BA=E6=96=
=B0=E5=A2=9E=E9=95=BF=E7=82=B9=EF=BC=8CB=E7=AB=AF=E5=B8=82=E5=9C=BA=E7=88=
=86=E5=8F=91=EF=BC=8C=E8=AE=A2=E9=98=85=E6=A8=A1=E5=BC=8F=E9=AA=8C=E8=AF=81=
=E6=88=90=E5=8A=9F</span></div><span class=3D"text-xs text-muted-foreground=
 flex-shrink-0">1=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex i=
tems-start justify-between gap-2"><div class=3D"flex items-start gap-2"><sp=
an class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></span><spa=
n class=3D"line-clamp-1">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E7=9B=91=E7=
=AE=A1=E6=94=BF=E7=AD=96=E8=B6=8B=E4=B8=A5=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=
=A8AI=E5=AE=89=E5=85=A8=E5=92=8C=E6=95=B0=E6=8D=AE=E9=9A=90=E7=A7=81=E7=9B=
=B8=E5=85=B3=E6=B3=95=E8=A7=84=E5=BD=B1=E5=93=8D</span></div><span class=3D=
"text-xs text-muted-foreground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D=
</span></li></ul></div></div></div><div class=3D"space-y-2"><div class=3D"f=
lex items-start gap-2 px-[15px]"><svg xmlns=3D"http://www.w3.org/2000/svg" =
width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"cu=
rrentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"=
round" class=3D"lucide lucide-message-square h-4 w-4 text-muted-foreground =
mt-0.5 flex-shrink-0"><path d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-=
2h14a2 2 0 0 1 2 2z"></path></svg><div class=3D"flex-1"><ul class=3D"space-=
y-2 text-sm text-foreground"><li class=3D"flex items-start justify-between =
gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-mute=
d-foreground rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-cl=
amp-1 flex-1">Copilot=E4=BC=81=E4=B8=9A=E7=89=88=E5=AE=9A=E4=BB=B7=E8=B0=83=
=E6=95=B4=EF=BC=8C=E4=B8=AD=E5=B0=8F=E4=BC=81=E4=B8=9A=E7=89=88=E6=9C=88=E8=
=B4=B9=E9=99=8D=E8=87=B3=E6=AF=8F=E7=94=A8=E6=88=B7$15</span></div><span cl=
ass=3D"text-xs text-muted-foreground flex-shrink-0">5=E5=B0=8F=E6=97=B6=E5=
=89=8D</span></li><li class=3D"flex items-start justify-between gap-2"><div=
 class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foregroun=
d rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-=
1">=E5=85=A8=E7=90=83=E8=B4=A2=E5=AF=8C1000=E5=BC=BA=E4=BC=81=E4=B8=9A=E4=
=B8=AD=E5=B7=B2=E6=9C=8985%=E9=83=A8=E7=BD=B2Copilot=E6=9C=8D=E5=8A=A1</spa=
n></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">8=E5=B0=
=8F=E6=97=B6=E5=89=8D</span></li></ul></div></div></div></div><div class=3D=
"w-32 border-l bg-muted/20 p-3 flex flex-col"><div class=3D"text-xs text-mu=
ted-foreground mb-2 font-medium">Following</div><div class=3D"space-y-1.5 f=
lex-1"><button class=3D"group relative w-full p-2 bg-card border border-bor=
der rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all du=
ration-200 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-des=
tructive rounded-full"></div><div class=3D"flex justify-between items-cente=
r"><div class=3D"font-sans text-xs font-medium text-foreground group-hover:=
text-primary transition-colors">MSFT</div><div class=3D"text-xs font-medium=
 text-success">****%</div></div></button><button class=3D"group relative w-=
full p-2 bg-card border border-border rounded-lg hover:border-primary/30 ho=
ver:bg-accent/50 transition-all duration-200 text-left"><div class=3D"flex =
justify-between items-center"><div class=3D"font-sans text-xs font-medium t=
ext-foreground group-hover:text-primary transition-colors">CRM</div><div cl=
ass=3D"text-xs font-medium text-destructive">-1.2%</div></div></button><but=
ton class=3D"group relative w-full p-2 bg-card border border-border rounded=
-lg hover:border-primary/30 hover:bg-accent/50 transition-all duration-200 =
text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive ro=
unded-full"></div><div class=3D"flex justify-between items-center"><div cla=
ss=3D"font-sans text-xs font-medium text-foreground group-hover:text-primar=
y transition-colors">ORCL</div><div class=3D"text-xs font-medium text-succe=
ss">+0.8%</div></div></button><button class=3D"group relative w-full p-2 bg=
-card border border-border rounded-lg hover:border-primary/30 hover:bg-acce=
nt/50 transition-all duration-200 text-left"><div class=3D"flex justify-bet=
ween items-center"><div class=3D"font-sans text-xs font-medium text-foregro=
und group-hover:text-primary transition-colors">ADBE</div><div class=3D"tex=
t-xs font-medium text-success">+4.1%</div></div></button><button class=3D"g=
roup relative w-full p-2 bg-card border border-border rounded-lg hover:bord=
er-primary/30 hover:bg-accent/50 transition-all duration-200 text-left"><di=
v class=3D"flex justify-between items-center"><div class=3D"font-sans text-=
xs font-medium text-foreground group-hover:text-primary transition-colors">=
SNOW</div><div class=3D"text-xs font-medium text-destructive">-0.5%</div></=
div></button></div><button class=3D"mt-2 w-full py-1.5 text-xs text-muted-f=
oreground hover:text-primary transition-colors border-t border-border/50 pt=
-2">more</button></div></div></div><div class=3D"rounded-lg text-card-foreg=
round shadow-sm cursor-pointer bg-gradient-card hover:shadow-elevated trans=
ition-all duration-300 hover:scale-[1.02] group border"><div class=3D"flex =
h-full"><div class=3D"flex-1 p-6 flex flex-col gap-4"><div class=3D"flex it=
ems-start gap-3"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" hei=
ght=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stro=
ke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"=
lucide lucide-sparkles h-5 w-5 text-primary flex-shrink-0 mt-1"><path d=3D"=
M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2=
 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 =
9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.58=
2 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path><path d=3D"M2=
2 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18H3"></path></svg>=
<div class=3D"flex-1 min-w-0"><h3 class=3D"font-bold text-base leading-tigh=
t line-clamp-2 group-hover:text-primary transition-colors mb-2">=E8=8B=B1=
=E4=BC=9F=E8=BE=BE=E5=8F=91=E5=B8=83H200=E8=8A=AF=E7=89=87=EF=BC=8CAI=E8=AE=
=AD=E7=BB=83=E6=95=88=E7=8E=87=E6=8F=90=E5=8D=8750%</h3><div class=3D"flex =
items-center justify-between gap-2"><div class=3D"flex items-center gap-2">=
<div class=3D"inline-flex items-center rounded-full border font-semibold tr=
ansition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-=
offset-2 text-foreground text-xs px-2 py-1">AI=E8=8A=AF=E7=89=87</div><div =
class=3D"inline-flex items-center rounded-full border font-semibold transit=
ion-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offse=
t-2 text-xs px-2 py-1 text-primary border-primary/30">=E6=8E=A8=E8=8D=90</d=
iv></div><div class=3D"flex items-center gap-1.5"><div class=3D"relative"><=
svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=
=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" str=
oke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-char=
t-column h-3.5 w-3.5 text-primary animate-pulse"><path d=3D"M3 3v16a2 2 0 0=
 0 2 2h16"></path><path d=3D"M18 17V9"></path><path d=3D"M13 17V5"></path><=
path d=3D"M8 17v-3"></path></svg><div class=3D"absolute -top-1 -right-1 w-1=
.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div></div><svg xmlns=3D=
"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 2=
4" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=
=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-mail h-3.5 w-3.=
5 text-muted-foreground hover:text-primary transition-colors cursor-pointer=
"><rect width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"2"></rect><path d=
=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><div class=3D"f=
lex items-center gap-1 cursor-pointer hover:text-primary transition-colors"=
><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewB=
ox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" s=
troke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-me=
ssage-circle h-3.5 w-3.5 text-muted-foreground"><path d=3D"M7.9 20A9 9 0 1 =
0 4 16.1L2 22Z"></path></svg><span class=3D"text-xs text-muted-foreground">=
13</span></div></div></div></div></div><div class=3D"p-3 bg-gradient-to-r f=
rom-gray-50 to-gray-100 border border-gray-200/50 rounded-none"><div class=
=3D"flex items-start gap-2"><svg xmlns=3D"http://www.w3.org/2000/svg" width=
=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"lucide lucide-sparkles h-4 w-4 text-primary mt-0.5 flex-shrink-0=
"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.96=
2L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 =
2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.43=
7 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path>=
<path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18H3">=
</path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-fore=
ground"><li class=3D"flex items-start justify-between gap-2"><div class=3D"=
flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2=
 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E5=88=A9=E5=
=A5=BD=E3=80=91=E8=8A=AF=E7=89=87=E6=80=A7=E8=83=BD=E5=A4=A7=E5=B9=85=E6=8F=
=90=E5=8D=87=EF=BC=8C=E4=B8=8B=E6=B8=B8=E5=BA=94=E7=94=A8=E9=9C=80=E6=B1=82=
=E6=BF=80=E5=A2=9E=EF=BC=8C=E4=BA=A7=E4=B8=9A=E9=93=BE=E9=BE=99=E5=A4=B4=E5=
=8F=97=E7=9B=8A=E6=98=8E=E6=98=BE</span></div><span class=3D"text-xs text-m=
uted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li =
class=3D"flex items-start justify-between gap-2"><div class=3D"flex items-s=
tart gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink=
-0"></span><span class=3D"line-clamp-1">=E3=80=90=E8=B6=8B=E5=8A=BF=E3=80=
=91AI=E7=AE=97=E5=8A=9B=E9=9C=80=E6=B1=82=E6=8C=81=E7=BB=AD=E7=88=86=E5=8F=
=91=EF=BC=8C=E9=AB=98=E7=AB=AF=E8=8A=AF=E7=89=87=E4=BE=9B=E4=B8=8D=E5=BA=94=
=E6=B1=82=EF=BC=8C=E8=AE=AE=E4=BB=B7=E8=83=BD=E5=8A=9B=E5=A2=9E=E5=BC=BA</s=
pan></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=
=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-b=
etween gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 =
bg-primary rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clam=
p-1">=E3=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E5=9C=B0=E7=BC=98=E6=94=BF=E6=B2=
=BB=E5=BD=B1=E5=93=8D=E4=BE=9B=E5=BA=94=E9=93=BE=E7=A8=B3=E5=AE=9A=EF=BC=8C=
=E9=9C=80=E5=85=B3=E6=B3=A8=E5=9B=BD=E9=99=85=E8=B4=B8=E6=98=93=E6=94=BF=E7=
=AD=96=E5=8F=98=E5=8C=96</span></div><span class=3D"text-xs text-muted-fore=
ground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span></li></ul></div><=
/div></div><div class=3D"space-y-2"><div class=3D"flex items-start gap-2 px=
-[15px]"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"2=
4" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=
=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide l=
ucide-message-square h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0"><p=
ath d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></p=
ath></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-foregro=
und"><li class=3D"flex items-start justify-between gap-2"><div class=3D"fle=
x items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-ful=
l mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">OpenAI=E3=
=80=81=E8=B0=B7=E6=AD=8C=E3=80=81Meta=E7=AD=89=E5=85=AC=E5=8F=B8=E5=B7=B2=
=E9=A2=84=E8=AE=A2H200=E8=8A=AF=E7=89=87=E4=BA=A7=E8=83=BD=E8=B6=85100=E4=
=B8=87=E7=89=87</span></div><span class=3D"text-xs text-muted-foreground fl=
ex-shrink-0">6=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li class=3D"flex item=
s-start justify-between gap-2"><div class=3D"flex items-start gap-2"><span =
class=3D"w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></spa=
n><span class=3D"line-clamp-1 flex-1">H200=E8=8A=AF=E7=89=87=E6=B5=8B=E8=AF=
=95=E6=98=BE=E7=A4=BA=E8=AE=AD=E7=BB=83GPT-4=E7=BA=A7=E5=88=AB=E6=A8=A1=E5=
=9E=8B=E6=88=90=E6=9C=AC=E9=99=8D=E4=BD=8E40%</span></div><span class=3D"te=
xt-xs text-muted-foreground flex-shrink-0">9=E5=B0=8F=E6=97=B6=E5=89=8D</sp=
an></li></ul></div></div></div></div><div class=3D"w-32 border-l bg-muted/2=
0 p-3 flex flex-col"><div class=3D"text-xs text-muted-foreground mb-2 font-=
medium">Following</div><div class=3D"space-y-1.5 flex-1"><button class=3D"g=
roup relative w-full p-2 bg-card border border-border rounded-lg hover:bord=
er-primary/30 hover:bg-accent/50 transition-all duration-200 text-left"><di=
v class=3D"absolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></=
div><div class=3D"flex justify-between items-center"><div class=3D"font-san=
s text-xs font-medium text-foreground group-hover:text-primary transition-c=
olors">NVDA</div><div class=3D"text-xs font-medium text-success">****%</div=
></div></button><button class=3D"group relative w-full p-2 bg-card border b=
order-border rounded-lg hover:border-primary/30 hover:bg-accent/50 transiti=
on-all duration-200 text-left"><div class=3D"flex justify-between items-cen=
ter"><div class=3D"font-sans text-xs font-medium text-foreground group-hove=
r:text-primary transition-colors">TSM</div><div class=3D"text-xs font-mediu=
m text-destructive">-1.2%</div></div></button><button class=3D"group relati=
ve w-full p-2 bg-card border border-border rounded-lg hover:border-primary/=
30 hover:bg-accent/50 transition-all duration-200 text-left"><div class=3D"=
absolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div><div cl=
ass=3D"flex justify-between items-center"><div class=3D"font-sans text-xs f=
ont-medium text-foreground group-hover:text-primary transition-colors">AMZN=
</div><div class=3D"text-xs font-medium text-success">+0.8%</div></div></bu=
tton><button class=3D"group relative w-full p-2 bg-card border border-borde=
r rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all dura=
tion-200 text-left"><div class=3D"flex justify-between items-center"><div c=
lass=3D"font-sans text-xs font-medium text-foreground group-hover:text-prim=
ary transition-colors">GOOGL</div><div class=3D"text-xs font-medium text-su=
ccess">+4.1%</div></div></button><button class=3D"group relative w-full p-2=
 bg-card border border-border rounded-lg hover:border-primary/30 hover:bg-a=
ccent/50 transition-all duration-200 text-left"><div class=3D"flex justify-=
between items-center"><div class=3D"font-sans text-xs font-medium text-fore=
ground group-hover:text-primary transition-colors">META</div><div class=3D"=
text-xs font-medium text-destructive">-0.5%</div></div></button></div><butt=
on class=3D"mt-2 w-full py-1.5 text-xs text-muted-foreground hover:text-pri=
mary transition-colors border-t border-border/50 pt-2">more</button></div><=
/div></div><div class=3D"rounded-lg text-card-foreground shadow-sm cursor-p=
ointer bg-gradient-card hover:shadow-elevated transition-all duration-300 h=
over:scale-[1.02] group border"><div class=3D"flex h-full"><div class=3D"fl=
ex-1 p-6 flex flex-col gap-4"><div class=3D"flex items-start gap-3"><svg xm=
lns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 =
0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-li=
necap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-sparkles h=
-5 w-5 text-primary flex-shrink-0 mt-1"><path d=3D"M9.937 15.5A2 2 0 0 0 8.=
5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582=
-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0=
 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 =
0z"></path><path d=3D"M20 3v4"></path><path d=3D"M22 5h-4"></path><path d=
=3D"M4 17v2"></path><path d=3D"M5 18H3"></path></svg><div class=3D"flex-1 m=
in-w-0"><h3 class=3D"font-bold text-base leading-tight line-clamp-2 group-h=
over:text-primary transition-colors mb-2">Meta=E5=8F=91=E5=B8=83Reality Lab=
s=E6=96=B0=E4=B8=80=E4=BB=A3VR=E5=A4=B4=E6=98=BE=EF=BC=8C=E5=85=83=E5=AE=87=
=E5=AE=99=E4=BD=93=E9=AA=8C=E9=9D=A9=E5=91=BD</h3><div class=3D"flex items-=
center justify-between gap-2"><div class=3D"flex items-center gap-2"><div c=
lass=3D"inline-flex items-center rounded-full border font-semibold transiti=
on-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset=
-2 text-foreground text-xs px-2 py-1">=E5=85=83=E5=AE=87=E5=AE=99</div><div=
 class=3D"inline-flex items-center rounded-full border font-semibold transi=
tion-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offs=
et-2 text-xs px-2 py-1 text-primary border-primary/30">=E6=8E=A8=E8=8D=90</=
div></div><div class=3D"flex items-center gap-1.5"><div class=3D"relative">=
<svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBo=
x=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" st=
roke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-cha=
rt-column h-3.5 w-3.5 text-primary animate-pulse"><path d=3D"M3 3v16a2 2 0 =
0 0 2 2h16"></path><path d=3D"M18 17V9"></path><path d=3D"M13 17V5"></path>=
<path d=3D"M8 17v-3"></path></svg><div class=3D"absolute -top-1 -right-1 w-=
1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div></div><svg xmlns=
=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 2=
4 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linec=
ap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-mail h-3.5 w-=
3.5 text-muted-foreground hover:text-primary transition-colors cursor-point=
er"><rect width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"2"></rect><path =
d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><div class=3D"=
flex items-center gap-1 cursor-pointer hover:text-primary transition-colors=
"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" view=
Box=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" =
stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-m=
essage-circle h-3.5 w-3.5 text-muted-foreground"><path d=3D"M7.9 20A9 9 0 1=
 0 4 16.1L2 22Z"></path></svg><span class=3D"text-xs text-muted-foreground"=
>21</span></div></div></div></div></div><div class=3D"p-3 bg-gradient-to-r =
from-gray-50 to-gray-100 border border-gray-200/50 rounded-none"><div class=
=3D"flex items-start gap-2"><svg xmlns=3D"http://www.w3.org/2000/svg" width=
=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"lucide lucide-sparkles h-4 w-4 text-primary mt-0.5 flex-shrink-0=
"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.96=
2L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 =
2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.43=
7 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path>=
<path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18H3">=
</path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-fore=
ground"><li class=3D"flex items-start justify-between gap-2"><div class=3D"=
flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2=
 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E5=88=A9=E5=
=A5=BD=E3=80=91=E7=A1=AC=E4=BB=B6=E6=8A=80=E6=9C=AF=E7=AA=81=E7=A0=B4=E4=B8=
=B4=E7=95=8C=E7=82=B9=EF=BC=8C=E7=94=A8=E6=88=B7=E4=BD=93=E9=AA=8C=E5=A4=A7=
=E5=B9=85=E6=94=B9=E5=96=84=EF=BC=8C=E6=B6=88=E8=B4=B9=E8=80=85=E6=8E=A5=E5=
=8F=97=E5=BA=A6=E6=8F=90=E5=8D=87</span></div><span class=3D"text-xs text-m=
uted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D</span></li><li =
class=3D"flex items-start justify-between gap-2"><div class=3D"flex items-s=
tart gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-shrink=
-0"></span><span class=3D"line-clamp-1">=E3=80=90=E8=B6=8B=E5=8A=BF=E3=80=
=91=E5=86=85=E5=AE=B9=E7=94=9F=E6=80=81=E9=80=90=E6=AD=A5=E5=AE=8C=E5=96=84=
=EF=BC=8C=E5=95=86=E4=B8=9A=E6=A8=A1=E5=BC=8F=E5=A4=9A=E6=A0=B7=E5=8C=96=EF=
=BC=8CB=E7=AB=AFC=E7=AB=AF=E5=B9=B6=E8=A1=8C=E5=8F=91=E5=B1=95</span></div>=
<span class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=B0=8F=E6=
=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-between ga=
p-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primar=
y rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=
=80=90=E9=A3=8E=E9=99=A9=E3=80=91=E5=B8=82=E5=9C=BA=E6=95=99=E8=82=B2=E4=BB=
=8D=E9=9C=80=E6=97=B6=E9=97=B4=EF=BC=8C=E7=9F=AD=E6=9C=9F=E5=86=85=E9=9A=BE=
=E4=BB=A5=E5=AE=9E=E7=8E=B0=E5=A4=A7=E8=A7=84=E6=A8=A1=E7=9B=88=E5=88=A9</s=
pan></div><span class=3D"text-xs text-muted-foreground flex-shrink-0">30=E5=
=88=86=E9=92=9F=E5=89=8D</span></li></ul></div></div></div><div class=3D"sp=
ace-y-2"><div class=3D"flex items-start gap-2 px-[15px]"><svg xmlns=3D"http=
://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fi=
ll=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"ro=
und" stroke-linejoin=3D"round" class=3D"lucide lucide-message-square h-4 w-=
4 text-muted-foreground mt-0.5 flex-shrink-0"><path d=3D"M21 15a2 2 0 0 1-2=
 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg><div class=3D"fle=
x-1"><ul class=3D"space-y-2 text-sm text-foreground"><li class=3D"flex item=
s-start justify-between gap-2"><div class=3D"flex items-start gap-2"><span =
class=3D"w-1 h-1 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></spa=
n><span class=3D"line-clamp-1 flex-1">Quest 4=E5=A4=B4=E6=98=BE=E9=A2=84=E8=
=AE=A2=E9=87=8F=E7=AA=81=E7=A0=B4200=E4=B8=87=E5=8F=B0=EF=BC=8C=E5=88=9BVR=
=E8=AE=BE=E5=A4=87=E9=A2=84=E5=94=AE=E7=BA=AA=E5=BD=95</span></div><span cl=
ass=3D"text-xs text-muted-foreground flex-shrink-0">30=E5=88=86=E9=92=9F=E5=
=89=8D</span></li><li class=3D"flex items-start justify-between gap-2"><div=
 class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-muted-foregroun=
d rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1 flex-=
1">Meta=E4=B8=8E=E5=BE=AE=E8=BD=AF=E5=90=88=E4=BD=9C=EF=BC=8COffice=E5=BA=
=94=E7=94=A8=E5=B0=86=E5=8E=9F=E7=94=9F=E6=94=AF=E6=8C=81VR=E7=8E=AF=E5=A2=
=83</span></div><span class=3D"text-xs text-muted-foreground flex-shrink-0"=
>3=E5=B0=8F=E6=97=B6=E5=89=8D</span></li></ul></div></div></div></div><div =
class=3D"w-32 border-l bg-muted/20 p-3 flex flex-col"><div class=3D"text-xs=
 text-muted-foreground mb-2 font-medium">Following</div><div class=3D"space=
-y-1.5 flex-1"><button class=3D"group relative w-full p-2 bg-card border bo=
rder-border rounded-lg hover:border-primary/30 hover:bg-accent/50 transitio=
n-all duration-200 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-=
2 bg-destructive rounded-full"></div><div class=3D"flex justify-between ite=
ms-center"><div class=3D"font-sans text-xs font-medium text-foreground grou=
p-hover:text-primary transition-colors">META</div><div class=3D"text-xs fon=
t-medium text-success">****%</div></div></button><button class=3D"group rel=
ative w-full p-2 bg-card border border-border rounded-lg hover:border-prima=
ry/30 hover:bg-accent/50 transition-all duration-200 text-left"><div class=
=3D"flex justify-between items-center"><div class=3D"font-sans text-xs font=
-medium text-foreground group-hover:text-primary transition-colors">NVDA</d=
iv><div class=3D"text-xs font-medium text-destructive">-1.2%</div></div></b=
utton><button class=3D"group relative w-full p-2 bg-card border border-bord=
er rounded-lg hover:border-primary/30 hover:bg-accent/50 transition-all dur=
ation-200 text-left"><div class=3D"absolute -top-1 -right-1 w-2 h-2 bg-dest=
ructive rounded-full"></div><div class=3D"flex justify-between items-center=
"><div class=3D"font-sans text-xs font-medium text-foreground group-hover:t=
ext-primary transition-colors">QCOM</div><div class=3D"text-xs font-medium =
text-success">+0.8%</div></div></button><button class=3D"group relative w-f=
ull p-2 bg-card border border-border rounded-lg hover:border-primary/30 hov=
er:bg-accent/50 transition-all duration-200 text-left"><div class=3D"flex j=
ustify-between items-center"><div class=3D"font-sans text-xs font-medium te=
xt-foreground group-hover:text-primary transition-colors">AAPL</div><div cl=
ass=3D"text-xs font-medium text-success">+4.1%</div></div></button><button =
class=3D"group relative w-full p-2 bg-card border border-border rounded-lg =
hover:border-primary/30 hover:bg-accent/50 transition-all duration-200 text=
-left"><div class=3D"flex justify-between items-center"><div class=3D"font-=
sans text-xs font-medium text-foreground group-hover:text-primary transitio=
n-colors">MSFT</div><div class=3D"text-xs font-medium text-destructive">-0.=
5%</div></div></button></div><button class=3D"mt-2 w-full py-1.5 text-xs te=
xt-muted-foreground hover:text-primary transition-colors border-t border-bo=
rder/50 pt-2">more</button></div></div></div><div class=3D"rounded-lg text-=
card-foreground shadow-sm cursor-pointer bg-gradient-card hover:shadow-elev=
ated transition-all duration-300 hover:scale-[1.02] group border"><div clas=
s=3D"flex h-full"><div class=3D"flex-1 p-6 flex flex-col gap-4"><div class=
=3D"flex items-start gap-3"><svg xmlns=3D"http://www.w3.org/2000/svg" width=
=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"lucide lucide-sparkles h-5 w-5 text-primary flex-shrink-0 mt-1">=
<path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L=
8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 =
0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 =
1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></path><p=
ath d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18H3"></=
path></svg><div class=3D"flex-1 min-w-0"><h3 class=3D"font-bold text-base l=
eading-tight line-clamp-2 group-hover:text-primary transition-colors mb-2">=
=E4=BA=9A=E9=A9=AC=E9=80=8AAWS=E6=8E=A8=E5=87=BA=E4=BA=91=E5=8E=9F=E7=94=9F=
AI=E6=9C=8D=E5=8A=A1=E5=B9=B3=E5=8F=B0=EF=BC=8C=E4=BC=81=E4=B8=9AAI=E9=83=
=A8=E7=BD=B2=E9=97=A8=E6=A7=9B=E5=A4=A7=E9=99=8D</h3><div class=3D"flex ite=
ms-center justify-between gap-2"><div class=3D"flex items-center gap-2"><di=
v class=3D"inline-flex items-center rounded-full border font-semibold trans=
ition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-off=
set-2 text-foreground text-xs px-2 py-1">=E4=BA=91=E6=9C=8D=E5=8A=A1</div><=
div class=3D"inline-flex items-center rounded-full border font-semibold tra=
nsition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-o=
ffset-2 text-xs px-2 py-1 text-primary border-primary/30">=E6=8E=A8=E8=8D=
=90</div></div><div class=3D"flex items-center gap-1.5"><div class=3D"relat=
ive"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" v=
iewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"=
2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucid=
e-chart-column h-3.5 w-3.5 text-primary animate-pulse"><path d=3D"M3 3v16a2=
 2 0 0 0 2 2h16"></path><path d=3D"M18 17V9"></path><path d=3D"M13 17V5"></=
path><path d=3D"M8 17v-3"></path></svg><div class=3D"absolute -top-1 -right=
-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div></div><svg xm=
lns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 =
0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-li=
necap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-mail h-3.5=
 w-3.5 text-muted-foreground hover:text-primary transition-colors cursor-po=
inter"><rect width=3D"20" height=3D"16" x=3D"2" y=3D"4" rx=3D"2"></rect><pa=
th d=3D"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg><div class=
=3D"flex items-center gap-1 cursor-pointer hover:text-primary transition-co=
lors"><svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" =
viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D=
"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide luci=
de-message-circle h-3.5 w-3.5 text-muted-foreground"><path d=3D"M7.9 20A9 9=
 0 1 0 4 16.1L2 22Z"></path></svg><span class=3D"text-xs text-muted-foregro=
und">28</span></div></div></div></div></div><div class=3D"p-3 bg-gradient-t=
o-r from-gray-50 to-gray-100 border border-gray-200/50 rounded-none"><div c=
lass=3D"flex items-start gap-2"><svg xmlns=3D"http://www.w3.org/2000/svg" w=
idth=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"cur=
rentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"r=
ound" class=3D"lucide lucide-sparkles h-4 w-4 text-primary mt-0.5 flex-shri=
nk-0"><path d=3D"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0=
-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.=
5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-=
1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d=3D"M20 3v4"></p=
ath><path d=3D"M22 5h-4"></path><path d=3D"M4 17v2"></path><path d=3D"M5 18=
H3"></path></svg><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-=
foreground"><li class=3D"flex items-start justify-between gap-2"><div class=
=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full =
mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E5=88=A9=
=E5=A5=BD=E3=80=91=E4=BC=81=E4=B8=9A=E6=95=B0=E5=AD=97=E5=8C=96=E8=BD=AC=E5=
=9E=8B=E5=8A=A0=E9=80=9F=EF=BC=8CAI=E5=BA=94=E7=94=A8=E6=B8=97=E9=80=8F=E7=
=8E=87=E5=BF=AB=E9=80=9F=E6=8F=90=E5=8D=87=EF=BC=8C=E5=95=86=E4=B8=9A=E5=8C=
=96=E8=BF=9B=E7=A8=8B=E6=98=8E=E7=A1=AE</span></div><span class=3D"text-xs =
text-muted-foreground flex-shrink-0">2=E5=B0=8F=E6=97=B6=E5=89=8D</span></l=
i><li class=3D"flex items-start justify-between gap-2"><div class=3D"flex i=
tems-start gap-2"><span class=3D"w-1 h-1 bg-primary rounded-full mt-2 flex-=
shrink-0"></span><span class=3D"line-clamp-1">=E3=80=90=E8=B6=8B=E5=8A=BF=
=E3=80=91AI Agent=E6=88=90=E4=B8=BA=E6=96=B0=E5=A2=9E=E9=95=BF=E7=82=B9=EF=
=BC=8CB=E7=AB=AF=E5=B8=82=E5=9C=BA=E7=88=86=E5=8F=91=EF=BC=8C=E8=AE=A2=E9=
=98=85=E6=A8=A1=E5=BC=8F=E9=AA=8C=E8=AF=81=E6=88=90=E5=8A=9F</span></div><s=
pan class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=B0=8F=E6=97=
=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-between gap-2=
"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-primary r=
ounded-full mt-2 flex-shrink-0"></span><span class=3D"line-clamp-1">=E3=80=
=90=E9=A3=8E=E9=99=A9=E3=80=91=E7=9B=91=E7=AE=A1=E6=94=BF=E7=AD=96=E8=B6=8B=
=E4=B8=A5=EF=BC=8C=E9=9C=80=E5=85=B3=E6=B3=A8AI=E5=AE=89=E5=85=A8=E5=92=8C=
=E6=95=B0=E6=8D=AE=E9=9A=90=E7=A7=81=E7=9B=B8=E5=85=B3=E6=B3=95=E8=A7=84=E5=
=BD=B1=E5=93=8D</span></div><span class=3D"text-xs text-muted-foreground fl=
ex-shrink-0">30=E5=88=86=E9=92=9F=E5=89=8D</span></li></ul></div></div></di=
v><div class=3D"space-y-2"><div class=3D"flex items-start gap-2 px-[15px]">=
<svg xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBo=
x=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" st=
roke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-mes=
sage-square h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0"><path d=3D"=
M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg=
><div class=3D"flex-1"><ul class=3D"space-y-2 text-sm text-foreground"><li =
class=3D"flex items-start justify-between gap-2"><div class=3D"flex items-s=
tart gap-2"><span class=3D"w-1 h-1 bg-muted-foreground rounded-full mt-2 fl=
ex-shrink-0"></span><span class=3D"line-clamp-1 flex-1">AWS AI Services=E5=
=B9=B3=E5=8F=B0beta=E7=89=88=E5=8F=91=E5=B8=83=EF=BC=8C=E5=B7=B2=E6=9C=8910=
00=E5=AE=B6=E4=BC=81=E4=B8=9A=E7=94=B3=E8=AF=B7=E8=AF=95=E7=94=A8</span></d=
iv><span class=3D"text-xs text-muted-foreground flex-shrink-0">1=E5=B0=8F=
=E6=97=B6=E5=89=8D</span></li><li class=3D"flex items-start justify-between=
 gap-2"><div class=3D"flex items-start gap-2"><span class=3D"w-1 h-1 bg-mut=
ed-foreground rounded-full mt-2 flex-shrink-0"></span><span class=3D"line-c=
lamp-1 flex-1">=E6=98=9F=E5=B7=B4=E5=85=8B=E4=BD=BF=E7=94=A8AWS AI=E4=BC=98=
=E5=8C=96=E9=97=A8=E5=BA=97=E8=BF=90=E8=90=A5=EF=BC=8C=E9=94=80=E5=94=AE=E9=
=A2=9D=E6=8F=90=E5=8D=8715%</span></div><span class=3D"text-xs text-muted-f=
oreground flex-shrink-0">4=E5=B0=8F=E6=97=B6=E5=89=8D</span></li></ul></div=
></div></div></div><div class=3D"w-32 border-l bg-muted/20 p-3 flex flex-co=
l"><div class=3D"text-xs text-muted-foreground mb-2 font-medium">Following<=
/div><div class=3D"space-y-1.5 flex-1"><button class=3D"group relative w-fu=
ll p-2 bg-card border border-border rounded-lg hover:border-primary/30 hove=
r:bg-accent/50 transition-all duration-200 text-left"><div class=3D"absolut=
e -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div><div class=3D"=
flex justify-between items-center"><div class=3D"font-sans text-xs font-med=
ium text-foreground group-hover:text-primary transition-colors">AMZN</div><=
div class=3D"text-xs font-medium text-success">****%</div></div></button><b=
utton class=3D"group relative w-full p-2 bg-card border border-border round=
ed-lg hover:border-primary/30 hover:bg-accent/50 transition-all duration-20=
0 text-left"><div class=3D"flex justify-between items-center"><div class=3D=
"font-sans text-xs font-medium text-foreground group-hover:text-primary tra=
nsition-colors">MSFT</div><div class=3D"text-xs font-medium text-destructiv=
e">-1.2%</div></div></button><button class=3D"group relative w-full p-2 bg-=
card border border-border rounded-lg hover:border-primary/30 hover:bg-accen=
t/50 transition-all duration-200 text-left"><div class=3D"absolute -top-1 -=
right-1 w-2 h-2 bg-destructive rounded-full"></div><div class=3D"flex justi=
fy-between items-center"><div class=3D"font-sans text-xs font-medium text-f=
oreground group-hover:text-primary transition-colors">GOOGL</div><div class=
=3D"text-xs font-medium text-success">+0.8%</div></div></button><button cla=
ss=3D"group relative w-full p-2 bg-card border border-border rounded-lg hov=
er:border-primary/30 hover:bg-accent/50 transition-all duration-200 text-le=
ft"><div class=3D"flex justify-between items-center"><div class=3D"font-san=
s text-xs font-medium text-foreground group-hover:text-primary transition-c=
olors">CRM</div><div class=3D"text-xs font-medium text-success">+4.1%</div>=
</div></button><button class=3D"group relative w-full p-2 bg-card border bo=
rder-border rounded-lg hover:border-primary/30 hover:bg-accent/50 transitio=
n-all duration-200 text-left"><div class=3D"flex justify-between items-cent=
er"><div class=3D"font-sans text-xs font-medium text-foreground group-hover=
:text-primary transition-colors">SNOW</div><div class=3D"text-xs font-mediu=
m text-destructive">-0.5%</div></div></button></div><button class=3D"mt-2 w=
-full py-1.5 text-xs text-muted-foreground hover:text-primary transition-co=
lors border-t border-border/50 pt-2">more</button></div></div></div></div><=
/div></div></main></div><div class=3D"fixed bottom-0 left-0 right-0 bg-card=
 border-t transition-all duration-300 z-40 h-16"><div class=3D"flex items-c=
enter justify-between px-6 h-16 border-b"><div class=3D"flex items-center g=
ap-4"><button class=3D"inline-flex items-center justify-center whitespace-n=
owrap text-sm font-medium ring-offset-background transition-all duration-20=
0 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring f=
ocus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50=
 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg=
-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow=
-md h-9 rounded-md px-3 gap-2"><svg xmlns=3D"http://www.w3.org/2000/svg" wi=
dth=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"curr=
entColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"ro=
und" class=3D"lucide lucide-file-text h-4 w-4"><path d=3D"M15 2H6a2 2 0 0 0=
-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d=3D"M14 2v4a2 2 0 =
0 0 2 2h4"></path><path d=3D"M10 9H8"></path><path d=3D"M16 13H8"></path><p=
ath d=3D"M16 17H8"></path></svg>=E7=AC=94=E8=AE=B0</button><button class=3D=
"inline-flex items-center justify-center whitespace-nowrap text-sm font-med=
ium ring-offset-background transition-all duration-200 focus-visible:outlin=
e-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offs=
et-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-e=
vents-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:te=
xt-accent-foreground h-9 rounded-md px-3 gap-2"><svg xmlns=3D"http://www.w3=
.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"non=
e" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stro=
ke-linejoin=3D"round" class=3D"lucide lucide-message-square h-4 w-4"><path =
d=3D"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>=
</svg>=E8=AF=84=E8=AE=BA<div class=3D"inline-flex items-center rounded-full=
 border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none fo=
cus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-second=
ary text-secondary-foreground hover:bg-secondary/80 ml-1 text-xs">3</div></=
button><button class=3D"inline-flex items-center justify-center whitespace-=
nowrap text-sm font-medium ring-offset-background transition-all duration-2=
00 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring =
focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-5=
0 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h=
over:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 gap-2"><svg=
 xmlns=3D"http://www.w3.org/2000/svg" width=3D"24" height=3D"24" viewBox=3D=
"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke=
-linecap=3D"round" stroke-linejoin=3D"round" class=3D"lucide lucide-bell h-=
4 w-4"><path d=3D"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path><path d=
=3D"M10.3 21a1.94 1.94 0 0 0 3.4 0"></path></svg>=E9=80=9A=E7=9F=A5<div cla=
ss=3D"inline-flex items-center rounded-full border px-2.5 py-0.5 font-semib=
old transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus=
:ring-offset-2 border-transparent bg-secondary text-secondary-foreground ho=
ver:bg-secondary/80 ml-1 text-xs">5</div></button></div><button class=3D"in=
line-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-=
medium ring-offset-background transition-all duration-200 focus-visible:out=
line-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-o=
ffset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointe=
r-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover=
:text-accent-foreground h-9 rounded-md px-3"><svg xmlns=3D"http://www.w3.or=
g/2000/svg" width=3D"24" height=3D"24" viewBox=3D"0 0 24 24" fill=3D"none" =
stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-=
linejoin=3D"round" class=3D"lucide lucide-chevron-up h-4 w-4"><path d=3D"m1=
8 15-6-6-6 6"></path></svg></button></div></div></div></div></div>
 =20
<a id=3D"lovable-badge" target=3D"_blank" href=3D"https://lovable.dev/proje=
cts/0dc5d1cc-3710-494a-823b-da576dbf07b9?utm_source=3Dlovable-badge">
	<span style=3D"color: #A1A1AA;">Edit with</span> <svg width=3D"60" height=
=3D"12" viewBox=3D"0 0 116 22" fill=3D"none" xmlns=3D"http://www.w3.org/200=
0/svg">
<path d=3D"M109.108 21.115C107.649 21.115 106.381 20.8369 105.306 20.2807C1=
04.23 19.7154 103.391 18.8675 102.789 17.7369C102.196 16.6063 101.9 15.2068=
 101.9 13.5382C101.9 11.9518 102.21 10.5841 102.83 9.4353C103.45 8.27736 10=
4.307 7.3975 105.401 6.79574C106.495 6.19398 107.74 5.89309 109.135 5.89309=
C110.475 5.89309 111.665 6.18486 112.705 6.76839C113.744 7.35192 114.551 8.=
19986 115.125 9.31221C115.709 10.4246 116.001 11.7557 116.001 13.3057C116.0=
01 13.8619 115.996 14.3041 115.987 14.6324H105.087V11.7603H113.347L111.788 =
12.2937C111.788 11.546 111.679 10.9215 111.46 10.42C111.25 9.90941 110.94 9=
.52647 110.53 9.27118C110.12 9.01588 109.623 8.88824 109.039 8.88824C108.42=
8 8.88824 107.89 9.03868 107.425 9.33956C106.97 9.63133 106.614 10.069 106.=
359 10.6525C106.112 11.236 105.989 11.9381 105.989 12.7587V14.1674C105.989 =
15.0062 106.117 15.7174 106.372 16.3009C106.628 16.8844 106.992 17.3266 107=
.466 17.6275C107.941 17.9193 108.501 18.0651 109.149 18.0651C109.86 18.0651=
 110.448 17.8828 110.913 17.5181C111.378 17.1443 111.67 16.62 111.788 15.94=
53H115.932C115.805 17.0029 115.444 17.9193 114.852 18.6943C114.268 19.4693 =
113.489 20.0665 112.513 20.4859C111.537 20.9053 110.402 21.115 109.108 21.1=
15Z" fill=3D"#FCFBF8"></path>
<path d=3D"M96.5167 1.1061H100.661V20.7181H96.5167V1.1061Z" fill=3D"#FCFBF8=
"></path>
<path d=3D"M89.4649 21.1148C88.6808 21.1148 87.9788 20.978 87.3588 20.7045C=
86.7479 20.4309 86.2282 20.0207 85.7996 19.4736C85.3711 18.9174 85.052 18.2=
336 84.8423 17.4221L85.2799 17.5452V20.7182H81.177V6.28948H85.321V9.51713L8=
4.856 9.59919C85.0657 8.82419 85.3848 8.16316 85.8133 7.6161C86.251 7.05992=
 86.7844 6.63595 87.4135 6.34419C88.0426 6.04331 88.7492 5.89287 89.5333 5.=
89287C90.7095 5.89287 91.7307 6.19831 92.5968 6.80919C93.463 7.42007 94.128=
6 8.29992 94.5936 9.44875C95.0586 10.5885 95.2911 11.9424 95.2911 13.5107C9=
5.2911 15.0698 95.054 16.4237 94.5799 17.5726C94.1058 18.7123 93.4265 19.58=
76 92.5421 20.1984C91.6668 20.8093 90.6411 21.1148 89.4649 21.1148ZM88.1794=
 17.9555C88.7994 17.9555 89.3191 17.7732 89.7385 17.4084C90.167 17.0437 90.=
4861 16.5286 90.6958 15.863C90.9146 15.1974 91.0241 14.4133 91.0241 13.5107=
C91.0241 12.608 90.9146 11.8239 90.6958 11.1583C90.4861 10.4927 90.167 9.97=
757 89.7385 9.61286C89.3191 9.23904 88.7994 9.05213 88.1794 9.05213C87.5685=
 9.05213 87.0442 9.23904 86.6066 9.61286C86.178 9.97757 85.8544 10.4973 85.=
6355 11.172C85.4167 11.8376 85.3073 12.6171 85.3073 13.5107C85.3073 14.4133=
 85.4167 15.1974 85.6355 15.863C85.8544 16.5286 86.178 17.0437 86.6066 17.4=
084C87.0442 17.7732 87.5685 17.9555 88.1794 17.9555ZM81.177 1.1061H85.321V6=
.28948H81.177V1.1061Z" fill=3D"#FCFBF8"></path>
<path d=3D"M70.7749 21.115C69.8723 21.115 69.0608 20.9372 68.3405 20.5816C6=
7.6293 20.226 67.0686 19.72 66.6583 19.0635C66.2571 18.3979 66.0565 17.6229=
 66.0565 16.7385C66.0565 15.3891 66.4531 14.3588 67.2464 13.6476C68.0396 12=
.9274 69.1839 12.4578 70.6792 12.239L73.182 11.8834C73.6834 11.8104 74.08 1=
1.7193 74.3718 11.6099C74.6636 11.5004 74.8778 11.3546 75.0146 11.1722C75.1=
514 10.9807 75.2197 10.7391 75.2197 10.4474C75.2197 10.1465 75.1377 9.87294=
 74.9736 9.62677C74.8186 9.37147 74.5815 9.17088 74.2624 9.025C73.9524 8.87=
 73.574 8.7925 73.1272 8.7925C72.4161 8.7925 71.8462 8.97941 71.4177 9.3532=
4C70.9892 9.71794 70.7567 10.2194 70.7202 10.8576H66.4395C66.4759 9.89118 6=
6.7677 9.03412 67.3148 8.28647C67.8709 7.52971 68.6414 6.94162 69.6261 6.52=
221C70.6108 6.1028 71.7505 5.89309 73.0452 5.89309C74.4037 5.89309 75.5525 =
6.11648 76.4917 6.56324C77.4308 7.00089 78.1374 7.63 78.6115 8.45059C79.094=
7 9.27118 79.3364 10.2513 79.3364 11.391V17.4087C79.3364 18.056 79.382 18.6=
578 79.4731 19.214C79.5734 19.761 79.7147 20.1075 79.8971 20.2534V20.7184H7=
5.589C75.4887 20.3263 75.4112 19.8841 75.3565 19.3918C75.3018 18.8994 75.26=
99 18.3797 75.2608 17.8326L75.9309 17.5454C75.7577 18.1928 75.4386 18.79 74=
.9736 19.3371C74.5177 19.875 73.9296 20.3081 73.2093 20.6363C72.4981 20.955=
4 71.6867 21.115 70.7749 21.115ZM72.3067 18.0788C72.8902 18.0788 73.4053 17=
.9512 73.8521 17.6959C74.2989 17.4315 74.6408 17.0668 74.8778 16.6018C75.12=
4 16.1368 75.2471 15.6079 75.2471 15.0153V13.1279L75.589 13.3194C75.3702 13=
.6112 75.0967 13.8346 74.7684 13.9896C74.4493 14.1446 74.0162 14.2768 73.46=
92 14.3862L72.4161 14.5913C71.714 14.7281 71.1852 14.9378 70.8296 15.2204C7=
0.4831 15.5031 70.3099 15.8997 70.3099 16.4103C70.3099 16.9209 70.4968 17.3=
266 70.8706 17.6275C71.2445 17.9284 71.7231 18.0788 72.3067 18.0788Z" fill=
=3D"#FCFBF8"></path>
<path d=3D"M51.962 6.28958H56.3659L60.1542 18.6668H58.8276L62.4656 6.28958H=
66.7463L61.7544 20.7182H57.1454L51.962 6.28958Z" fill=3D"#FCFBF8"></path>
<path d=3D"M45.4846 21.115C44.0531 21.115 42.7949 20.805 41.7099 20.185C40.=
634 19.565 39.7997 18.6806 39.2071 17.5318C38.6236 16.3829 38.3318 15.0381 =
38.3318 13.4972C38.3318 11.9563 38.6236 10.616 39.2071 9.47633C39.7997 8.32=
75 40.634 7.44309 41.7099 6.82309C42.7949 6.20309 44.0531 5.89309 45.4846 5=
.89309C46.916 5.89309 48.1697 6.20309 49.2456 6.82309C50.3215 7.44309 51.15=
12 8.3275 51.7347 9.47633C52.3274 10.616 52.6237 11.9563 52.6237 13.4972C52=
.6237 15.0381 52.3274 16.3829 51.7347 17.5318C51.1512 18.6806 50.3215 19.56=
5 49.2456 20.185C48.1697 20.805 46.916 21.115 45.4846 21.115ZM45.4846 17.94=
21C46.0863 17.9421 46.6015 17.7779 47.03 17.4497C47.4585 17.1123 47.7868 16=
.6154 48.0147 15.959C48.2427 15.2934 48.3566 14.4728 48.3566 13.4972C48.356=
6 12.0475 48.1059 10.9488 47.6044 10.2012C47.103 9.44441 46.3963 9.06603 45=
.4846 9.06603C44.8828 9.06603 44.3631 9.23471 43.9255 9.57206C43.4969 9.900=
3 43.1687 10.3972 42.9408 11.0628C42.7128 11.7193 42.5988 12.5307 42.5988 1=
3.4972C42.5988 14.4637 42.7128 15.2797 42.9408 15.9453C43.1687 16.6109 43.4=
969 17.1123 43.9255 17.4497C44.3631 17.7779 44.8828 17.9421 45.4846 17.9421=
Z" fill=3D"#FCFBF8"></path>
<path d=3D"M26.2195 1.10631H30.514V17.6623L29.7481 16.7734C29.7481 16.7734 =
31.8751 16.7734 35.534 16.7734C39.1928 16.7734 38.6925 20.7184 38.6925 20.7=
184H26.2195V1.10631Z" fill=3D"#FCFBF8"></path>
<mask id=3D"mask0_19703_15608" style=3D"mask-type:alpha" maskUnits=3D"userS=
paceOnUse" x=3D"0" y=3D"0" width=3D"20" height=3D"21">
<path fill-rule=3D"evenodd" clip-rule=3D"evenodd" d=3D"M5.90405 0.885124C9.=
16477 0.885124 11.8081 3.53543 11.8081 6.80474V9.05456H13.773C17.0337 9.054=
56 19.677 11.7049 19.677 14.9742C19.677 18.2435 17.0337 20.8938 13.773 20.8=
938H0V6.80474C0 3.53543 2.64333 0.885124 5.90405 0.885124Z" fill=3D"url(#pa=
int0_linear_19703_15608)"></path>
</mask>
<g mask=3D"url(#mask0_19703_15608)">
<g filter=3D"url(#filter0_f_19703_15608)">
<circle cx=3D"8.63157" cy=3D"11.5658" r=3D"13.3199" fill=3D"#4B73FF"></circ=
le>
</g>
<g filter=3D"url(#filter1_f_19703_15608)">
<ellipse cx=3D"10.0949" cy=3D"4.25612" rx=3D"17.0591" ry=3D"13.3199" fill=
=3D"#FF66F4"></ellipse>
</g>
<g filter=3D"url(#filter2_f_19703_15608)">
<ellipse cx=3D"12.8775" cy=3D"1.74957" rx=3D"13.3199" ry=3D"11.6977" fill=
=3D"#FF0105"></ellipse>
</g>
<g filter=3D"url(#filter3_f_19703_15608)">
<circle cx=3D"10.3319" cy=3D"4.25254" r=3D"8.01052" fill=3D"#FE7B02"></circ=
le>
</g>
</g>
<defs>
<filter id=3D"filter0_f_19703_15608" x=3D"-10.6577" y=3D"-7.72354" width=3D=
"38.5786" height=3D"38.5786" filterUnits=3D"userSpaceOnUse" color-interpola=
tion-filters=3D"sRGB">
<feFlood flood-opacity=3D"0" result=3D"BackgroundImageFix"></feFlood>
<feBlend mode=3D"normal" in=3D"SourceGraphic" in2=3D"BackgroundImageFix" re=
sult=3D"shape"></feBlend>
<feGaussianBlur stdDeviation=3D"2.98472" result=3D"effect1_foregroundBlur_1=
9703_15608"></feGaussianBlur>
</filter>
<filter id=3D"filter1_f_19703_15608" x=3D"-12.9337" y=3D"-15.0332" width=3D=
"46.057" height=3D"38.5786" filterUnits=3D"userSpaceOnUse" color-interpolat=
ion-filters=3D"sRGB">
<feFlood flood-opacity=3D"0" result=3D"BackgroundImageFix"></feFlood>
<feBlend mode=3D"normal" in=3D"SourceGraphic" in2=3D"BackgroundImageFix" re=
sult=3D"shape"></feBlend>
<feGaussianBlur stdDeviation=3D"2.98472" result=3D"effect1_foregroundBlur_1=
9703_15608"></feGaussianBlur>
</filter>
<filter id=3D"filter2_f_19703_15608" x=3D"-6.41182" y=3D"-15.9176" width=3D=
"38.5786" height=3D"35.3342" filterUnits=3D"userSpaceOnUse" color-interpola=
tion-filters=3D"sRGB">
<feFlood flood-opacity=3D"0" result=3D"BackgroundImageFix"></feFlood>
<feBlend mode=3D"normal" in=3D"SourceGraphic" in2=3D"BackgroundImageFix" re=
sult=3D"shape"></feBlend>
<feGaussianBlur stdDeviation=3D"2.98472" result=3D"effect1_foregroundBlur_1=
9703_15608"></feGaussianBlur>
</filter>
<filter id=3D"filter3_f_19703_15608" x=3D"-3.64803" y=3D"-9.72742" width=3D=
"27.9599" height=3D"27.9599" filterUnits=3D"userSpaceOnUse" color-interpola=
tion-filters=3D"sRGB">
<feFlood flood-opacity=3D"0" result=3D"BackgroundImageFix"></feFlood>
<feBlend mode=3D"normal" in=3D"SourceGraphic" in2=3D"BackgroundImageFix" re=
sult=3D"shape"></feBlend>
<feGaussianBlur stdDeviation=3D"2.98472" result=3D"effect1_foregroundBlur_1=
9703_15608"></feGaussianBlur>
</filter>
<linearGradient id=3D"paint0_linear_19703_15608" x1=3D"6.62168" y1=3D"4.401=
29" x2=3D"12.6165" y2=3D"20.8863" gradientUnits=3D"userSpaceOnUse">
<stop offset=3D"0.025" stop-color=3D"#FF8E63"></stop>
<stop offset=3D"0.56" stop-color=3D"#FF7EB0"></stop>
<stop offset=3D"0.95" stop-color=3D"#4B73FF"></stop>
</linearGradient>
</defs>
</svg>


	<button id=3D"lovable-badge-close" style=3D"position: absolute; top: -2px;=
 right: 5px; cursor: pointer; font-size: 14px; color: #A1A1AA;">=C3=97</but=
ton>
</a>



</body></html>
------MultipartBoundary--1OOam4t37Q4W0wYzOsI1GkthzSLBuOURyLF2x4eASm----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://idea-pilot-insight.lovable.app/assets/index-Bc_iacBs.css

@charset "utf-8";

*, ::before, ::after { --tw-border-spacing-x: 0; --tw-border-spacing-y: 0; =
--tw-translate-x: 0; --tw-translate-y: 0; --tw-rotate: 0; --tw-skew-x: 0; -=
-tw-skew-y: 0; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: =
; --tw-pinch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-=
from-position: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ;=
 --tw-ordinal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-s=
pacing: ; --tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-widt=
h: 0px; --tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5)=
; --tw-ring-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-sha=
dow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightnes=
s: ; --tw-contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; -=
-tw-saturate: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --t=
w-backdrop-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale:=
 ; --tw-backdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacit=
y: ; --tw-backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; =
--tw-contain-layout: ; --tw-contain-paint: ; --tw-contain-style: ; }

::backdrop { --tw-border-spacing-x: 0; --tw-border-spacing-y: 0; --tw-trans=
late-x: 0; --tw-translate-y: 0; --tw-rotate: 0; --tw-skew-x: 0; --tw-skew-y=
: 0; --tw-scale-x: 1; --tw-scale-y: 1; --tw-pan-x: ; --tw-pan-y: ; --tw-pin=
ch-zoom: ; --tw-scroll-snap-strictness: proximity; --tw-gradient-from-posit=
ion: ; --tw-gradient-via-position: ; --tw-gradient-to-position: ; --tw-ordi=
nal: ; --tw-slashed-zero: ; --tw-numeric-figure: ; --tw-numeric-spacing: ; =
--tw-numeric-fraction: ; --tw-ring-inset: ; --tw-ring-offset-width: 0px; --=
tw-ring-offset-color: #fff; --tw-ring-color: rgb(59 130 246 / .5); --tw-rin=
g-offset-shadow: 0 0 #0000; --tw-ring-shadow: 0 0 #0000; --tw-shadow: 0 0 #=
0000; --tw-shadow-colored: 0 0 #0000; --tw-blur: ; --tw-brightness: ; --tw-=
contrast: ; --tw-grayscale: ; --tw-hue-rotate: ; --tw-invert: ; --tw-satura=
te: ; --tw-sepia: ; --tw-drop-shadow: ; --tw-backdrop-blur: ; --tw-backdrop=
-brightness: ; --tw-backdrop-contrast: ; --tw-backdrop-grayscale: ; --tw-ba=
ckdrop-hue-rotate: ; --tw-backdrop-invert: ; --tw-backdrop-opacity: ; --tw-=
backdrop-saturate: ; --tw-backdrop-sepia: ; --tw-contain-size: ; --tw-conta=
in-layout: ; --tw-contain-paint: ; --tw-contain-style: ; }

*, ::before, ::after { box-sizing: border-box; border-width: 0px; border-st=
yle: solid; border-color: rgb(229, 231, 235); }

::before, ::after { --tw-content: ""; }

html, :host { line-height: 1.5; text-size-adjust: 100%; tab-size: 4; font-f=
amily: Arial, "Microsoft YaHei", sans-serif; font-feature-settings: normal;=
 font-variation-settings: normal; -webkit-tap-highlight-color: transparent;=
 }

body { margin: 0px; line-height: inherit; }

hr { height: 0px; color: inherit; border-top-width: 1px; }

abbr:where([title]) { text-decoration: underline dotted; }

h1, h2, h3, h4, h5, h6 { font-size: inherit; font-weight: inherit; }

a { color: inherit; text-decoration: inherit; }

b, strong { font-weight: bolder; }

code, kbd, samp, pre { font-family: ui-monospace, SFMono-Regular, Menlo, Mo=
naco, Consolas, "Liberation Mono", "Courier New", monospace; font-feature-s=
ettings: normal; font-variation-settings: normal; font-size: 1em; }

small { font-size: 80%; }

sub, sup { font-size: 75%; line-height: 0; position: relative; vertical-ali=
gn: baseline; }

sub { bottom: -0.25em; }

sup { top: -0.5em; }

table { text-indent: 0px; border-color: inherit; border-collapse: collapse;=
 }

button, input, optgroup, select, textarea { font-family: inherit; font-feat=
ure-settings: inherit; font-variation-settings: inherit; font-size: 100%; f=
ont-weight: inherit; line-height: inherit; letter-spacing: inherit; color: =
inherit; margin: 0px; padding: 0px; }

button, select { text-transform: none; }

button, input:where([type=3D"button"]), input:where([type=3D"reset"]), inpu=
t:where([type=3D"submit"]) { appearance: button; background-color: transpar=
ent; background-image: none; }

progress { vertical-align: baseline; }

::-webkit-inner-spin-button, ::-webkit-outer-spin-button { height: auto; }

[type=3D"search"] { appearance: textfield; outline-offset: -2px; }

::-webkit-search-decoration { appearance: none; }

::-webkit-file-upload-button { appearance: button; font: inherit; }

summary { display: list-item; }

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre { margin: 0p=
x; }

fieldset { margin: 0px; padding: 0px; }

legend { padding: 0px; }

ol, ul, menu { list-style: none; margin: 0px; padding: 0px; }

dialog { padding: 0px; }

textarea { resize: vertical; }

input::placeholder, textarea::placeholder { opacity: 1; color: rgb(156, 163=
, 175); }

button, [role=3D"button"] { cursor: pointer; }

:disabled { cursor: default; }

img, svg, video, canvas, audio, iframe, embed, object { display: block; ver=
tical-align: middle; }

img, video { max-width: 100%; height: auto; }

[hidden]:where(:not([hidden=3D"until-found"])) { display: none; }

:root { --background: 220 13% 98%; --foreground: 220 20% 15%; --card: 0 0% =
100%; --card-foreground: 220 20% 15%; --popover: 0 0% 100%; --popover-foreg=
round: 220 20% 15%; --primary: 240 50% 20%; --primary-foreground: 0 0% 98%;=
 --primary-glow: 240 100% 80%; --success: 142 71% 45%; --success-foreground=
: 0 0% 98%; --warning: 38 92% 50%; --warning-foreground: 0 0% 98%; --second=
ary: 220 14% 96%; --secondary-foreground: 220 20% 15%; --muted: 220 14% 96%=
; --muted-foreground: 220 13% 46%; --accent: 240 5% 96%; --accent-foregroun=
d: 240 50% 20%; --destructive: 0 84% 60%; --destructive-foreground: 0 0% 98=
%; --border: 220 13% 91%; --input: 220 13% 91%; --ring: 240 50% 20%; --radi=
us: .75rem; --gradient-primary: linear-gradient(135deg, hsl(240 50% 20%), h=
sl(260 50% 25%)); --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), =
hsl(220 14% 98%)); --gradient-success: linear-gradient(135deg, hsl(142 71% =
45%), hsl(152 81% 55%)); --shadow-card: 0 4px 6px -1px hsl(220 13% 46% / .1=
), 0 2px 4px -1px hsl(220 13% 46% / .06); --shadow-elevated: 0 10px 15px -3=
px hsl(240 50% 20% / .1), 0 4px 6px -2px hsl(240 50% 20% / .05); --shadow-g=
low: 0 0 40px hsl(240 100% 80% / .3); --transition-smooth: all .3s cubic-be=
zier(.4, 0, .2, 1); --transition-bounce: all .3s cubic-bezier(.68, -.55, .2=
65, 1.55); --sidebar-background: 240 10% 8%; --sidebar-foreground: 240 20% =
80%; --sidebar-primary: 240 50% 20%; --sidebar-primary-foreground: 0 0% 98%=
; --sidebar-accent: 240 15% 15%; --sidebar-accent-foreground: 240 20% 80%; =
--sidebar-border: 240 15% 15%; --sidebar-ring: 240 100% 80%; }

.dark { --background: 222.2 84% 4.9%; --foreground: 210 40% 98%; --card: 22=
2.2 84% 4.9%; --card-foreground: 210 40% 98%; --popover: 222.2 84% 4.9%; --=
popover-foreground: 210 40% 98%; --primary: 210 40% 98%; --primary-foregrou=
nd: 222.2 47.4% 11.2%; --secondary: 217.2 32.6% 17.5%; --secondary-foregrou=
nd: 210 40% 98%; --muted: 217.2 32.6% 17.5%; --muted-foreground: 215 20.2% =
65.1%; --accent: 217.2 32.6% 17.5%; --accent-foreground: 210 40% 98%; --des=
tructive: 0 62.8% 30.6%; --destructive-foreground: 210 40% 98%; --border: 2=
17.2 32.6% 17.5%; --input: 217.2 32.6% 17.5%; --ring: 212.7 26.8% 83.9%; --=
sidebar-background: 240 5.9% 10%; --sidebar-foreground: 240 4.8% 95.9%; --s=
idebar-primary: 224.3 76.3% 48%; --sidebar-primary-foreground: 0 0% 100%; -=
-sidebar-accent: 240 3.7% 15.9%; --sidebar-accent-foreground: 240 4.8% 95.9=
%; --sidebar-border: 240 3.7% 15.9%; --sidebar-ring: 217.2 91.2% 59.8%; }

* { border-color: hsl(var(--border)); }

body { background-color: hsl(var(--background)); color: hsl(var(--foregroun=
d)); }

.container { width: 100%; margin-right: auto; margin-left: auto; padding-ri=
ght: 2rem; padding-left: 2rem; }

@media (min-width: 1400px) {
  .container { max-width: 1400px; }
}

.sr-only { position: absolute; width: 1px; height: 1px; padding: 0px; margi=
n: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: now=
rap; border-width: 0px; }

.pointer-events-none { pointer-events: none; }

.pointer-events-auto { pointer-events: auto; }

.visible { visibility: visible; }

.invisible { visibility: hidden; }

.fixed { position: fixed; }

.absolute { position: absolute; }

.relative { position: relative; }

.inset-0 { inset: 0px; }

.inset-x-0 { left: 0px; right: 0px; }

.inset-y-0 { top: 0px; bottom: 0px; }

.-bottom-12 { bottom: -3rem; }

.-left-12 { left: -3rem; }

.-right-1 { right: -0.25rem; }

.-right-12 { right: -3rem; }

.-top-1 { top: -0.25rem; }

.-top-12 { top: -3rem; }

.-top-3 { top: -0.75rem; }

.bottom-0 { bottom: 0px; }

.left-0 { left: 0px; }

.left-1 { left: 0.25rem; }

.left-1\/2 { left: 50%; }

.left-2 { left: 0.5rem; }

.left-3 { left: 0.75rem; }

.left-\[50\%\] { left: 50%; }

.right-0 { right: 0px; }

.right-1 { right: 0.25rem; }

.right-2 { right: 0.5rem; }

.right-3 { right: 0.75rem; }

.right-4 { right: 1rem; }

.top-0 { top: 0px; }

.top-1\.5 { top: 0.375rem; }

.top-1\/2 { top: 50%; }

.top-2 { top: 0.5rem; }

.top-3\.5 { top: 0.875rem; }

.top-4 { top: 1rem; }

.top-\[1px\] { top: 1px; }

.top-\[50\%\] { top: 50%; }

.top-\[60\%\] { top: 60%; }

.top-full { top: 100%; }

.z-10 { z-index: 10; }

.z-20 { z-index: 20; }

.z-40 { z-index: 40; }

.z-50 { z-index: 50; }

.z-\[100\] { z-index: 100; }

.z-\[1\] { z-index: 1; }

.col-span-1 { grid-column: span 1 / span 1; }

.col-span-4 { grid-column: span 4 / span 4; }

.-mx-1 { margin-left: -0.25rem; margin-right: -0.25rem; }

.mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }

.mx-3\.5 { margin-left: 0.875rem; margin-right: 0.875rem; }

.mx-8 { margin-left: 2rem; margin-right: 2rem; }

.mx-auto { margin-left: auto; margin-right: auto; }

.my-0\.5 { margin-top: 0.125rem; margin-bottom: 0.125rem; }

.my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }

.-ml-4 { margin-left: -1rem; }

.-mt-4 { margin-top: -1rem; }

.mb-1 { margin-bottom: 0.25rem; }

.mb-16 { margin-bottom: 4rem; }

.mb-2 { margin-bottom: 0.5rem; }

.mb-3 { margin-bottom: 0.75rem; }

.mb-4 { margin-bottom: 1rem; }

.mb-6 { margin-bottom: 1.5rem; }

.mb-8 { margin-bottom: 2rem; }

.ml-1 { margin-left: 0.25rem; }

.ml-2 { margin-left: 0.5rem; }

.ml-4 { margin-left: 1rem; }

.ml-6 { margin-left: 1.5rem; }

.ml-auto { margin-left: auto; }

.mr-1 { margin-right: 0.25rem; }

.mr-2 { margin-right: 0.5rem; }

.mt-0\.5 { margin-top: 0.125rem; }

.mt-1 { margin-top: 0.25rem; }

.mt-1\.5 { margin-top: 0.375rem; }

.mt-12 { margin-top: 3rem; }

.mt-2 { margin-top: 0.5rem; }

.mt-24 { margin-top: 6rem; }

.mt-4 { margin-top: 1rem; }

.mt-6 { margin-top: 1.5rem; }

.mt-auto { margin-top: auto; }

.line-clamp-1 { overflow: hidden; display: -webkit-box; -webkit-box-orient:=
 vertical; -webkit-line-clamp: 1; }

.line-clamp-2 { overflow: hidden; display: -webkit-box; -webkit-box-orient:=
 vertical; -webkit-line-clamp: 2; }

.line-clamp-3 { overflow: hidden; display: -webkit-box; -webkit-box-orient:=
 vertical; -webkit-line-clamp: 3; }

.block { display: block; }

.flex { display: flex; }

.inline-flex { display: inline-flex; }

.table { display: table; }

.grid { display: grid; }

.hidden { display: none; }

.aspect-square { aspect-ratio: 1 / 1; }

.aspect-video { aspect-ratio: 16 / 9; }

.size-4 { width: 1rem; height: 1rem; }

.h-0\.5 { height: 0.125rem; }

.h-1 { height: 0.25rem; }

.h-1\.5 { height: 0.375rem; }

.h-10 { height: 2.5rem; }

.h-11 { height: 2.75rem; }

.h-12 { height: 3rem; }

.h-16 { height: 4rem; }

.h-2 { height: 0.5rem; }

.h-2\.5 { height: 0.625rem; }

.h-3 { height: 0.75rem; }

.h-3\.5 { height: 0.875rem; }

.h-32 { height: 8rem; }

.h-4 { height: 1rem; }

.h-48 { height: 12rem; }

.h-5 { height: 1.25rem; }

.h-6 { height: 1.5rem; }

.h-64 { height: 16rem; }

.h-7 { height: 1.75rem; }

.h-8 { height: 2rem; }

.h-80 { height: 20rem; }

.h-9 { height: 2.25rem; }

.h-\[1px\] { height: 1px; }

.h-\[320px\] { height: 320px; }

.h-\[400px\] { height: 400px; }

.h-\[calc\(100vh-120px\)\] { height: calc(-120px + 100vh); }

.h-\[var\(--radix-navigation-menu-viewport-height\)\] { height: var(--radix=
-navigation-menu-viewport-height); }

.h-\[var\(--radix-select-trigger-height\)\] { height: var(--radix-select-tr=
igger-height); }

.h-auto { height: auto; }

.h-fit { height: fit-content; }

.h-full { height: 100%; }

.h-px { height: 1px; }

.h-svh { height: 100svh; }

.max-h-64 { max-height: 16rem; }

.max-h-80 { max-height: 20rem; }

.max-h-96 { max-height: 24rem; }

.max-h-\[300px\] { max-height: 300px; }

.max-h-\[320px\] { max-height: 320px; }

.max-h-\[600px\] { max-height: 600px; }

.max-h-screen { max-height: 100vh; }

.min-h-0 { min-height: 0px; }

.min-h-\[100px\] { min-height: 100px; }

.min-h-\[120px\] { min-height: 120px; }

.min-h-\[60vh\] { min-height: 60vh; }

.min-h-\[80px\] { min-height: 80px; }

.min-h-screen { min-height: 100vh; }

.min-h-svh { min-height: 100svh; }

.w-0 { width: 0px; }

.w-0\.5 { width: 0.125rem; }

.w-1 { width: 0.25rem; }

.w-1\.5 { width: 0.375rem; }

.w-10 { width: 2.5rem; }

.w-11 { width: 2.75rem; }

.w-12 { width: 3rem; }

.w-16 { width: 4rem; }

.w-2 { width: 0.5rem; }

.w-2\.5 { width: 0.625rem; }

.w-20 { width: 5rem; }

.w-24 { width: 6rem; }

.w-3 { width: 0.75rem; }

.w-3\.5 { width: 0.875rem; }

.w-3\/4 { width: 75%; }

.w-32 { width: 8rem; }

.w-4 { width: 1rem; }

.w-48 { width: 12rem; }

.w-5 { width: 1.25rem; }

.w-56 { width: 14rem; }

.w-6 { width: 1.5rem; }

.w-64 { width: 16rem; }

.w-7 { width: 1.75rem; }

.w-72 { width: 18rem; }

.w-8 { width: 2rem; }

.w-80 { width: 20rem; }

.w-9 { width: 2.25rem; }

.w-\[--sidebar-width\] { width: var(--sidebar-width); }

.w-\[100px\] { width: 100px; }

.w-\[1px\] { width: 1px; }

.w-\[500px\] { width: 500px; }

.w-auto { width: auto; }

.w-fit { width: fit-content; }

.w-full { width: 100%; }

.w-max { width: max-content; }

.w-px { width: 1px; }

.min-w-0 { min-width: 0px; }

.min-w-5 { min-width: 1.25rem; }

.min-w-\[120px\] { min-width: 120px; }

.min-w-\[12rem\] { min-width: 12rem; }

.min-w-\[200px\] { min-width: 200px; }

.min-w-\[32px\] { min-width: 32px; }

.min-w-\[48px\] { min-width: 48px; }

.min-w-\[64px\] { min-width: 64px; }

.min-w-\[8rem\] { min-width: 8rem; }

.min-w-\[var\(--radix-select-trigger-width\)\] { min-width: var(--radix-sel=
ect-trigger-width); }

.max-w-3xl { max-width: 48rem; }

.max-w-4xl { max-width: 56rem; }

.max-w-7xl { max-width: 80rem; }

.max-w-\[--skeleton-width\] { max-width: var(--skeleton-width); }

.max-w-\[85\%\] { max-width: 85%; }

.max-w-lg { max-width: 32rem; }

.max-w-max { max-width: max-content; }

.max-w-md { max-width: 28rem; }

.flex-1 { flex: 1 1 0%; }

.flex-shrink-0, .shrink-0 { flex-shrink: 0; }

.grow { flex-grow: 1; }

.grow-0 { flex-grow: 0; }

.basis-full { flex-basis: 100%; }

.caption-bottom { caption-side: bottom; }

.border-collapse { border-collapse: collapse; }

.-translate-x-1\/2 { --tw-translate-x: -50%; transform: translate(var(--tw-=
translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-=
skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-=
scale-y)); }

.-translate-x-px { --tw-translate-x: -1px; transform: translate(var(--tw-tr=
anslate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-sk=
ew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-sc=
ale-y)); }

.-translate-y-1\/2 { --tw-translate-y: -50%; transform: translate(var(--tw-=
translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-=
skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-=
scale-y)); }

.translate-x-\[-50\%\] { --tw-translate-x: -50%; transform: translate(var(-=
-tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(-=
-tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(-=
-tw-scale-y)); }

.translate-x-px { --tw-translate-x: 1px; transform: translate(var(--tw-tran=
slate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew=
-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scal=
e-y)); }

.translate-y-\[-50\%\] { --tw-translate-y: -50%; transform: translate(var(-=
-tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(-=
-tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(-=
-tw-scale-y)); }

.rotate-180 { --tw-rotate: 180deg; transform: translate(var(--tw-translate-=
x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) s=
kewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));=
 }

.rotate-45 { --tw-rotate: 45deg; transform: translate(var(--tw-translate-x)=
,var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) ske=
wY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.rotate-90 { --tw-rotate: 90deg; transform: translate(var(--tw-translate-x)=
,var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) ske=
wY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.transform { transform: translate(var(--tw-translate-x),var(--tw-translate-=
y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y))=
 scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

@keyframes pulse {=20
  50% { opacity: 0.5; }
}

.animate-pulse { animation: 2s cubic-bezier(0.4, 0, 0.6, 1) 0s infinite nor=
mal none running pulse; }

@keyframes spin {=20
  100% { transform: rotate(360deg); }
}

.animate-spin { animation: 1s linear 0s infinite normal none running spin; =
}

.cursor-default { cursor: default; }

.cursor-help { cursor: help; }

.cursor-pointer { cursor: pointer; }

.touch-none { touch-action: none; }

.select-none { user-select: none; }

.resize-none { resize: none; }

.list-none { list-style-type: none; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0px, 1fr)); }

.grid-cols-2 { grid-template-columns: repeat(2, minmax(0px, 1fr)); }

.grid-cols-3 { grid-template-columns: repeat(3, minmax(0px, 1fr)); }

.grid-cols-4 { grid-template-columns: repeat(4, minmax(0px, 1fr)); }

.grid-cols-5 { grid-template-columns: repeat(5, minmax(0px, 1fr)); }

.flex-row { flex-direction: row; }

.flex-col { flex-direction: column; }

.flex-col-reverse { flex-direction: column-reverse; }

.flex-wrap { flex-wrap: wrap; }

.items-start { align-items: flex-start; }

.items-end { align-items: flex-end; }

.items-center { align-items: center; }

.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }

.justify-end { justify-content: flex-end; }

.justify-center { justify-content: center; }

.justify-between { justify-content: space-between; }

.gap-1 { gap: 0.25rem; }

.gap-1\.5 { gap: 0.375rem; }

.gap-2 { gap: 0.5rem; }

.gap-3 { gap: 0.75rem; }

.gap-4 { gap: 1rem; }

.gap-6 { gap: 1.5rem; }

.gap-8 { gap: 2rem; }

.space-x-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; mar=
gin-right: calc(.25rem * var(--tw-space-x-reverse)); margin-left: calc(.25r=
em * calc(1 - var(--tw-space-x-reverse))); }

.space-x-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; mar=
gin-right: calc(.5rem * var(--tw-space-x-reverse)); margin-left: calc(.5rem=
 * calc(1 - var(--tw-space-x-reverse))); }

.space-x-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; mar=
gin-right: calc(1rem * var(--tw-space-x-reverse)); margin-left: calc(1rem *=
 calc(1 - var(--tw-space-x-reverse))); }

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; =
margin-top: calc(.125rem * calc(1 - var(--tw-space-y-reverse))); margin-bot=
tom: calc(.125rem * var(--tw-space-y-reverse)); }

.space-y-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom:=
 calc(.25rem * var(--tw-space-y-reverse)); }

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; =
margin-top: calc(.375rem * calc(1 - var(--tw-space-y-reverse))); margin-bot=
tom: calc(.375rem * var(--tw-space-y-reverse)); }

.space-y-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: =
calc(.5rem * var(--tw-space-y-reverse)); }

.space-y-3 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom:=
 calc(.75rem * var(--tw-space-y-reverse)); }

.space-y-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: c=
alc(1rem * var(--tw-space-y-reverse)); }

.space-y-6 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom:=
 calc(1.5rem * var(--tw-space-y-reverse)); }

.space-y-8 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; mar=
gin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: c=
alc(2rem * var(--tw-space-y-reverse)); }

.divide-x > :not([hidden]) ~ :not([hidden]) { --tw-divide-x-reverse: 0; bor=
der-right-width: calc(1px * var(--tw-divide-x-reverse)); border-left-width:=
 calc(1px * calc(1 - var(--tw-divide-x-reverse))); }

.divide-border > :not([hidden]) ~ :not([hidden]) { border-color: hsl(var(--=
border)); }

.overflow-auto { overflow: auto; }

.overflow-hidden { overflow: hidden; }

.overflow-x-auto { overflow-x: auto; }

.overflow-y-auto { overflow-y: auto; }

.overflow-x-hidden { overflow-x: hidden; }

.truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap;=
 }

.whitespace-nowrap { white-space: nowrap; }

.whitespace-pre-line { white-space: pre-line; }

.break-words { overflow-wrap: break-word; }

.rounded { border-radius: 0.25rem; }

.rounded-\[2px\] { border-radius: 2px; }

.rounded-\[inherit\] { border-radius: inherit; }

.rounded-full { border-radius: 9999px; }

.rounded-lg { border-radius: var(--radius); }

.rounded-md { border-radius: calc(var(--radius) - 2px); }

.rounded-none { border-radius: 0px; }

.rounded-sm { border-radius: calc(var(--radius) - 4px); }

.rounded-r-lg { border-top-right-radius: var(--radius); border-bottom-right=
-radius: var(--radius); }

.rounded-t { border-top-left-radius: 0.25rem; border-top-right-radius: 0.25=
rem; }

.rounded-t-\[10px\] { border-top-left-radius: 10px; border-top-right-radius=
: 10px; }

.rounded-t-lg { border-top-left-radius: var(--radius); border-top-right-rad=
ius: var(--radius); }

.rounded-tl-sm { border-top-left-radius: calc(var(--radius) - 4px); }

.border { border-width: 1px; }

.border-0 { border-width: 0px; }

.border-2 { border-width: 2px; }

.border-\[1\.5px\] { border-width: 1.5px; }

.border-y { border-top-width: 1px; border-bottom-width: 1px; }

.border-b { border-bottom-width: 1px; }

.border-l { border-left-width: 1px; }

.border-l-2 { border-left-width: 2px; }

.border-l-4 { border-left-width: 4px; }

.border-r { border-right-width: 1px; }

.border-t { border-top-width: 1px; }

.border-dashed { border-style: dashed; }

.border-\[--color-border\] { border-color: var(--color-border); }

.border-blue-200 { --tw-border-opacity: 1; border-color: rgb(191 219 254 / =
var(--tw-border-opacity, 1)); }

.border-border { border-color: hsl(var(--border)); }

.border-border\/50 { border-color: hsl(var(--border) / .5); }

.border-destructive { border-color: hsl(var(--destructive)); }

.border-destructive\/20 { border-color: hsl(var(--destructive) / .2); }

.border-destructive\/50 { border-color: hsl(var(--destructive) / .5); }

.border-emerald-200 { --tw-border-opacity: 1; border-color: rgb(167 243 208=
 / var(--tw-border-opacity, 1)); }

.border-gray-200 { --tw-border-opacity: 1; border-color: rgb(229 231 235 / =
var(--tw-border-opacity, 1)); }

.border-gray-200\/50 { border-color: rgba(229, 231, 235, 0.5); }

.border-green-200 { --tw-border-opacity: 1; border-color: rgb(187 247 208 /=
 var(--tw-border-opacity, 1)); }

.border-input { border-color: hsl(var(--input)); }

.border-muted { border-color: hsl(var(--muted)); }

.border-primary { border-color: hsl(var(--primary)); }

.border-primary\/30 { border-color: hsl(var(--primary) / .3); }

.border-primary\/50 { border-color: hsl(var(--primary) / .5); }

.border-purple-200 { --tw-border-opacity: 1; border-color: rgb(233 213 255 =
/ var(--tw-border-opacity, 1)); }

.border-red-200 { --tw-border-opacity: 1; border-color: rgb(254 202 202 / v=
ar(--tw-border-opacity, 1)); }

.border-sidebar-border { border-color: hsl(var(--sidebar-border)); }

.border-slate-200 { --tw-border-opacity: 1; border-color: rgb(226 232 240 /=
 var(--tw-border-opacity, 1)); }

.border-slate-300 { --tw-border-opacity: 1; border-color: rgb(203 213 225 /=
 var(--tw-border-opacity, 1)); }

.border-success\/20 { border-color: hsl(var(--success) / .2); }

.border-transparent { border-color: transparent; }

.border-warning\/20 { border-color: hsl(var(--warning) / .2); }

.border-yellow-200 { --tw-border-opacity: 1; border-color: rgb(254 240 138 =
/ var(--tw-border-opacity, 1)); }

.border-l-primary { border-left-color: hsl(var(--primary)); }

.border-l-transparent { border-left-color: transparent; }

.border-t-transparent { border-top-color: transparent; }

.bg-\[--color-bg\] { background-color: var(--color-bg); }

.bg-accent { background-color: hsl(var(--accent)); }

.bg-background { background-color: hsl(var(--background)); }

.bg-background\/50 { background-color: hsl(var(--background) / .5); }

.bg-black\/80 { background-color: rgba(0, 0, 0, 0.8); }

.bg-blue-100 { --tw-bg-opacity: 1; background-color: rgb(219 234 254 / var(=
--tw-bg-opacity, 1)); }

.bg-blue-50 { --tw-bg-opacity: 1; background-color: rgb(239 246 255 / var(-=
-tw-bg-opacity, 1)); }

.bg-blue-500 { --tw-bg-opacity: 1; background-color: rgb(59 130 246 / var(-=
-tw-bg-opacity, 1)); }

.bg-blue-600 { --tw-bg-opacity: 1; background-color: rgb(37 99 235 / var(--=
tw-bg-opacity, 1)); }

.bg-border { background-color: hsl(var(--border)); }

.bg-card { background-color: hsl(var(--card)); }

.bg-card\/50 { background-color: hsl(var(--card) / .5); }

.bg-destructive { background-color: hsl(var(--destructive)); }

.bg-destructive\/20 { background-color: hsl(var(--destructive) / .2); }

.bg-destructive\/5 { background-color: hsl(var(--destructive) / .05); }

.bg-emerald-50 { --tw-bg-opacity: 1; background-color: rgb(236 253 245 / va=
r(--tw-bg-opacity, 1)); }

.bg-foreground { background-color: hsl(var(--foreground)); }

.bg-gray-100 { --tw-bg-opacity: 1; background-color: rgb(243 244 246 / var(=
--tw-bg-opacity, 1)); }

.bg-green-100 { --tw-bg-opacity: 1; background-color: rgb(220 252 231 / var=
(--tw-bg-opacity, 1)); }

.bg-green-500 { --tw-bg-opacity: 1; background-color: rgb(34 197 94 / var(-=
-tw-bg-opacity, 1)); }

.bg-muted { background-color: hsl(var(--muted)); }

.bg-muted-foreground { background-color: hsl(var(--muted-foreground)); }

.bg-muted\/10 { background-color: hsl(var(--muted) / .1); }

.bg-muted\/20 { background-color: hsl(var(--muted) / .2); }

.bg-muted\/30 { background-color: hsl(var(--muted) / .3); }

.bg-muted\/5 { background-color: hsl(var(--muted) / .05); }

.bg-muted\/50 { background-color: hsl(var(--muted) / .5); }

.bg-popover { background-color: hsl(var(--popover)); }

.bg-primary { background-color: hsl(var(--primary)); }

.bg-primary\/10 { background-color: hsl(var(--primary) / .1); }

.bg-primary\/5 { background-color: hsl(var(--primary) / .05); }

.bg-purple-100 { --tw-bg-opacity: 1; background-color: rgb(243 232 255 / va=
r(--tw-bg-opacity, 1)); }

.bg-red-100 { --tw-bg-opacity: 1; background-color: rgb(254 226 226 / var(-=
-tw-bg-opacity, 1)); }

.bg-red-50 { --tw-bg-opacity: 1; background-color: rgb(254 242 242 / var(--=
tw-bg-opacity, 1)); }

.bg-secondary { background-color: hsl(var(--secondary)); }

.bg-sidebar { background-color: hsl(var(--sidebar-background)); }

.bg-sidebar-accent { background-color: hsl(var(--sidebar-accent)); }

.bg-sidebar-border { background-color: hsl(var(--sidebar-border)); }

.bg-slate-100 { --tw-bg-opacity: 1; background-color: rgb(241 245 249 / var=
(--tw-bg-opacity, 1)); }

.bg-slate-200 { --tw-bg-opacity: 1; background-color: rgb(226 232 240 / var=
(--tw-bg-opacity, 1)); }

.bg-slate-50 { --tw-bg-opacity: 1; background-color: rgb(248 250 252 / var(=
--tw-bg-opacity, 1)); }

.bg-slate-700 { --tw-bg-opacity: 1; background-color: rgb(51 65 85 / var(--=
tw-bg-opacity, 1)); }

.bg-slate-800 { --tw-bg-opacity: 1; background-color: rgb(30 41 59 / var(--=
tw-bg-opacity, 1)); }

.bg-success { background-color: hsl(var(--success)); }

.bg-success\/20 { background-color: hsl(var(--success) / .2); }

.bg-success\/5 { background-color: hsl(var(--success) / .05); }

.bg-transparent { background-color: transparent; }

.bg-warning { background-color: hsl(var(--warning)); }

.bg-warning\/10 { background-color: hsl(var(--warning) / .1); }

.bg-white { --tw-bg-opacity: 1; background-color: rgb(255 255 255 / var(--t=
w-bg-opacity, 1)); }

.bg-white\/50 { background-color: rgba(255, 255, 255, 0.5); }

.bg-yellow-100 { --tw-bg-opacity: 1; background-color: rgb(254 249 195 / va=
r(--tw-bg-opacity, 1)); }

.bg-gradient-card { background-image: var(--gradient-card); }

.bg-gradient-primary { background-image: var(--gradient-primary); }

.bg-gradient-to-r { background-image: linear-gradient(to right,var(--tw-gra=
dient-stops)); }

.from-gray-50 { --tw-gradient-from: #f9fafb var(--tw-gradient-from-position=
); --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position); -=
-tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }

.to-gray-100 { --tw-gradient-to: #f3f4f6 var(--tw-gradient-to-position); }

.bg-clip-text { background-clip: text; }

.fill-current { fill: currentcolor; }

.fill-red-500 { fill: rgb(239, 68, 68); }

.p-0 { padding: 0px; }

.p-1 { padding: 0.25rem; }

.p-2 { padding: 0.5rem; }

.p-3 { padding: 0.75rem; }

.p-4 { padding: 1rem; }

.p-6 { padding: 1.5rem; }

.p-8 { padding: 2rem; }

.p-\[1px\] { padding: 1px; }

.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }

.px-1\.5 { padding-left: 0.375rem; padding-right: 0.375rem; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }

.px-2\.5 { padding-left: 0.625rem; padding-right: 0.625rem; }

.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }

.px-4 { padding-left: 1rem; padding-right: 1rem; }

.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }

.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

.px-8 { padding-left: 2rem; padding-right: 2rem; }

.px-\[15px\] { padding-left: 15px; padding-right: 15px; }

.py-0 { padding-top: 0px; padding-bottom: 0px; }

.py-0\.5 { padding-top: 0.125rem; padding-bottom: 0.125rem; }

.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }

.py-1\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }

.py-12 { padding-top: 3rem; padding-bottom: 3rem; }

.py-16 { padding-top: 4rem; padding-bottom: 4rem; }

.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }

.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

.py-4 { padding-top: 1rem; padding-bottom: 1rem; }

.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }

.py-8 { padding-top: 2rem; padding-bottom: 2rem; }

.pb-2 { padding-bottom: 0.5rem; }

.pb-3 { padding-bottom: 0.75rem; }

.pb-4 { padding-bottom: 1rem; }

.pl-10 { padding-left: 2.5rem; }

.pl-2\.5 { padding-left: 0.625rem; }

.pl-3 { padding-left: 0.75rem; }

.pl-4 { padding-left: 1rem; }

.pl-8 { padding-left: 2rem; }

.pr-2 { padding-right: 0.5rem; }

.pr-2\.5 { padding-right: 0.625rem; }

.pr-4 { padding-right: 1rem; }

.pr-8 { padding-right: 2rem; }

.pt-0 { padding-top: 0px; }

.pt-1 { padding-top: 0.25rem; }

.pt-2 { padding-top: 0.5rem; }

.pt-3 { padding-top: 0.75rem; }

.pt-4 { padding-top: 1rem; }

.text-left { text-align: left; }

.text-center { text-align: center; }

.text-right { text-align: right; }

.align-middle { vertical-align: middle; }

.font-mono { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Cons=
olas, "Liberation Mono", "Courier New", monospace; }

.font-sans { font-family: Arial, "Microsoft YaHei", sans-serif; }

.text-2xl { font-size: 1.5rem; line-height: 2rem; }

.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }

.text-\[0\.8rem\] { font-size: 0.8rem; }

.text-base { font-size: 1rem; line-height: 1.5rem; }

.text-lg { font-size: 1.125rem; line-height: 1.75rem; }

.text-sm { font-size: 0.875rem; line-height: 1.25rem; }

.text-xl { font-size: 1.25rem; line-height: 1.75rem; }

.text-xs { font-size: 0.75rem; line-height: 1rem; }

.font-bold { font-weight: 700; }

.font-medium { font-weight: 500; }

.font-normal { font-weight: 400; }

.font-semibold { font-weight: 600; }

.tabular-nums { --tw-numeric-spacing: tabular-nums; font-variant-numeric: v=
ar(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-n=
umeric-spacing) var(--tw-numeric-fraction); }

.leading-none { line-height: 1; }

.leading-relaxed { line-height: 1.625; }

.leading-tight { line-height: 1.25; }

.tracking-tight { letter-spacing: -0.025em; }

.tracking-widest { letter-spacing: 0.1em; }

.text-accent-foreground { color: hsl(var(--accent-foreground)); }

.text-blue-500 { --tw-text-opacity: 1; color: rgb(59 130 246 / var(--tw-tex=
t-opacity, 1)); }

.text-blue-600 { --tw-text-opacity: 1; color: rgb(37 99 235 / var(--tw-text=
-opacity, 1)); }

.text-blue-700 { --tw-text-opacity: 1; color: rgb(29 78 216 / var(--tw-text=
-opacity, 1)); }

.text-card-foreground { color: hsl(var(--card-foreground)); }

.text-current { color: currentcolor; }

.text-destructive { color: hsl(var(--destructive)); }

.text-destructive-foreground { color: hsl(var(--destructive-foreground)); }

.text-emerald-700 { --tw-text-opacity: 1; color: rgb(4 120 87 / var(--tw-te=
xt-opacity, 1)); }

.text-foreground { color: hsl(var(--foreground)); }

.text-foreground\/50 { color: hsl(var(--foreground) / .5); }

.text-gray-600 { --tw-text-opacity: 1; color: rgb(75 85 99 / var(--tw-text-=
opacity, 1)); }

.text-gray-700 { --tw-text-opacity: 1; color: rgb(55 65 81 / var(--tw-text-=
opacity, 1)); }

.text-green-600 { --tw-text-opacity: 1; color: rgb(22 163 74 / var(--tw-tex=
t-opacity, 1)); }

.text-green-700 { --tw-text-opacity: 1; color: rgb(21 128 61 / var(--tw-tex=
t-opacity, 1)); }

.text-muted-foreground { color: hsl(var(--muted-foreground)); }

.text-orange-600 { --tw-text-opacity: 1; color: rgb(234 88 12 / var(--tw-te=
xt-opacity, 1)); }

.text-popover-foreground { color: hsl(var(--popover-foreground)); }

.text-primary { color: hsl(var(--primary)); }

.text-primary-foreground { color: hsl(var(--primary-foreground)); }

.text-primary-foreground\/70 { color: hsl(var(--primary-foreground) / .7); =
}

.text-purple-700 { --tw-text-opacity: 1; color: rgb(126 34 206 / var(--tw-t=
ext-opacity, 1)); }

.text-red-500 { --tw-text-opacity: 1; color: rgb(239 68 68 / var(--tw-text-=
opacity, 1)); }

.text-red-700 { --tw-text-opacity: 1; color: rgb(185 28 28 / var(--tw-text-=
opacity, 1)); }

.text-secondary-foreground { color: hsl(var(--secondary-foreground)); }

.text-sidebar-accent-foreground { color: hsl(var(--sidebar-accent-foregroun=
d)); }

.text-sidebar-foreground { color: hsl(var(--sidebar-foreground)); }

.text-sidebar-foreground\/60 { color: hsl(var(--sidebar-foreground) / .6); =
}

.text-sidebar-foreground\/70 { color: hsl(var(--sidebar-foreground) / .7); =
}

.text-slate-400 { --tw-text-opacity: 1; color: rgb(148 163 184 / var(--tw-t=
ext-opacity, 1)); }

.text-slate-500 { --tw-text-opacity: 1; color: rgb(100 116 139 / var(--tw-t=
ext-opacity, 1)); }

.text-slate-600 { --tw-text-opacity: 1; color: rgb(71 85 105 / var(--tw-tex=
t-opacity, 1)); }

.text-slate-700 { --tw-text-opacity: 1; color: rgb(51 65 85 / var(--tw-text=
-opacity, 1)); }

.text-slate-800 { --tw-text-opacity: 1; color: rgb(30 41 59 / var(--tw-text=
-opacity, 1)); }

.text-success { color: hsl(var(--success)); }

.text-success-foreground { color: hsl(var(--success-foreground)); }

.text-transparent { color: transparent; }

.text-warning { color: hsl(var(--warning)); }

.text-warning-foreground { color: hsl(var(--warning-foreground)); }

.text-white { --tw-text-opacity: 1; color: rgb(255 255 255 / var(--tw-text-=
opacity, 1)); }

.text-yellow-700 { --tw-text-opacity: 1; color: rgb(161 98 7 / var(--tw-tex=
t-opacity, 1)); }

.underline { text-decoration-line: underline; }

.underline-offset-4 { text-underline-offset: 4px; }

.opacity-0 { opacity: 0; }

.opacity-50 { opacity: 0.5; }

.opacity-60 { opacity: 0.6; }

.opacity-70 { opacity: 0.7; }

.opacity-90 { opacity: 0.9; }

.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] { --tw-shadow: 0 0 0 1=
px hsl(var(--sidebar-border)); --tw-shadow-colored: 0 0 0 1px var(--tw-shad=
ow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-rin=
g-shadow, 0 0 #0000),var(--tw-shadow); }

.shadow-card { --tw-shadow: var(--shadow-card); --tw-shadow-colored: var(--=
shadow-card); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-=
ring-shadow, 0 0 #0000),var(--tw-shadow); }

.shadow-lg { --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px =
rgb(0 0 0 / .1); --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-colo=
r), 0 4px 6px -4px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset=
-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow); }

.shadow-md { --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rg=
b(0 0 0 / .1); --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), =
0 2px 4px -2px var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-sha=
dow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow); }

.shadow-none { --tw-shadow: 0 0 #0000; --tw-shadow-colored: 0 0 #0000; box-=
shadow: var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #=
0000),var(--tw-shadow); }

.shadow-sm { --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05); --tw-shadow-colored=
: 0 1px 2px 0 var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shad=
ow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow); }

.shadow-xl { --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / .1), 0 8px 10px -6px=
 rgb(0 0 0 / .1); --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-col=
or), 0 8px 10px -6px var(--tw-shadow-color); box-shadow: var(--tw-ring-offs=
et-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow); }

.shadow-card { --tw-shadow-color: hsl(var(--card)); --tw-shadow: var(--tw-s=
hadow-colored); }

.outline-none { outline: transparent solid 2px; outline-offset: 2px; }

.outline { outline-style: solid; }

.ring-0 { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring=
-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring=
-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);=
 box-shadow: var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-sh=
adow, 0 0 #0000); }

.ring-2 { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring=
-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring=
-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);=
 box-shadow: var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-sh=
adow, 0 0 #0000); }

.ring-ring { --tw-ring-color: hsl(var(--ring)); }

.ring-sidebar-ring { --tw-ring-color: hsl(var(--sidebar-ring)); }

.ring-slate-800 { --tw-ring-opacity: 1; --tw-ring-color: rgb(30 41 59 / var=
(--tw-ring-opacity, 1)); }

.ring-offset-background { --tw-ring-offset-color: hsl(var(--background)); }

.filter { filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) va=
r(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) =
var(--tw-sepia) var(--tw-drop-shadow); }

.backdrop-blur-sm { --tw-backdrop-blur: blur(4px); backdrop-filter: var(--t=
w-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) =
var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdro=
p-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-b=
ackdrop-sepia); }

.transition { transition-property: color, background-color, border-color, t=
ext-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,=
 backdrop-filter, -webkit-backdrop-filter; transition-timing-function: cubi=
c-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-\[left\,right\,width\] { transition-property: left, right, widt=
h; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-dur=
ation: 0.15s; }

.transition-\[margin\,opa\] { transition-property: margin, opa; transition-=
timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; =
}

.transition-\[width\,height\,padding\] { transition-property: width, height=
, padding; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transi=
tion-duration: 0.15s; }

.transition-\[width\] { transition-property: width; transition-timing-funct=
ion: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-all { transition-property: all; transition-timing-function: cub=
ic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-colors { transition-property: color, background-color, border-c=
olor, text-decoration-color, fill, stroke; transition-timing-function: cubi=
c-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-opacity { transition-property: opacity; transition-timing-funct=
ion: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-shadow { transition-property: box-shadow; transition-timing-fun=
ction: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.transition-transform { transition-property: transform; transition-timing-f=
unction: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 0.15s; }

.duration-1000 { transition-duration: 1s; }

.duration-200 { transition-duration: 0.2s; }

.duration-300 { transition-duration: 0.3s; }

.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

.ease-linear { transition-timing-function: linear; }

@keyframes enter {=20
  0% { opacity: var(--tw-enter-opacity, 1); transform: translate3d(var(--tw=
-enter-translate-x, 0),var(--tw-enter-translate-y, 0),0) scale3d(var(--tw-e=
nter-scale, 1),var(--tw-enter-scale, 1),var(--tw-enter-scale, 1)) rotate(va=
r(--tw-enter-rotate, 0)); }
}

@keyframes exit {=20
  100% { opacity: var(--tw-exit-opacity, 1); transform: translate3d(var(--t=
w-exit-translate-x, 0),var(--tw-exit-translate-y, 0),0) scale3d(var(--tw-ex=
it-scale, 1),var(--tw-exit-scale, 1),var(--tw-exit-scale, 1)) rotate(var(--=
tw-exit-rotate, 0)); }
}

.animate-in { animation-name: enter; animation-duration: 0.15s; --tw-enter-=
opacity: initial; --tw-enter-scale: initial; --tw-enter-rotate: initial; --=
tw-enter-translate-x: initial; --tw-enter-translate-y: initial; }

.fade-in-0 { --tw-enter-opacity: 0; }

.fade-in-80 { --tw-enter-opacity: .8; }

.zoom-in-95 { --tw-enter-scale: .95; }

.duration-1000 { animation-duration: 1s; }

.duration-200 { animation-duration: 0.2s; }

.duration-300 { animation-duration: 0.3s; }

.ease-in-out { animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

.ease-linear { animation-timing-function: linear; }

.line-clamp-3 { display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-or=
ient: vertical; overflow: hidden; }

.file\:border-0::file-selector-button { border-width: 0px; }

.file\:bg-transparent::file-selector-button { background-color: transparent=
; }

.file\:text-sm::file-selector-button { font-size: 0.875rem; line-height: 1.=
25rem; }

.file\:font-medium::file-selector-button { font-weight: 500; }

.file\:text-foreground::file-selector-button { color: hsl(var(--foreground)=
); }

.placeholder\:text-muted-foreground::placeholder { color: hsl(var(--muted-f=
oreground)); }

.placeholder\:text-slate-500::placeholder { --tw-text-opacity: 1; color: rg=
b(100 116 139 / var(--tw-text-opacity, 1)); }

.after\:absolute::after { content: var(--tw-content); position: absolute; }

.after\:-inset-2::after { content: var(--tw-content); inset: -0.5rem; }

.after\:inset-y-0::after { content: var(--tw-content); top: 0px; bottom: 0p=
x; }

.after\:left-1\/2::after { content: var(--tw-content); left: 50%; }

.after\:w-1::after { content: var(--tw-content); width: 0.25rem; }

.after\:w-\[2px\]::after { content: var(--tw-content); width: 2px; }

.after\:-translate-x-1\/2::after { content: var(--tw-content); --tw-transla=
te-x: -50%; transform: translate(var(--tw-translate-x),var(--tw-translate-y=
)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) =
scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.first\:rounded-l-md:first-child { border-top-left-radius: calc(var(--radiu=
s) - 2px); border-bottom-left-radius: calc(var(--radius) - 2px); }

.first\:border-l:first-child { border-left-width: 1px; }

.last\:rounded-r-md:last-child { border-top-right-radius: calc(var(--radius=
) - 2px); border-bottom-right-radius: calc(var(--radius) - 2px); }

.focus-within\:relative:focus-within { position: relative; }

.focus-within\:z-20:focus-within { z-index: 20; }

.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform=
: translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-ro=
tate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale=
-x)) scaleY(var(--tw-scale-y)); }

.hover\:scale-\[1\.01\]:hover { --tw-scale-x: 1.01; --tw-scale-y: 1.01; tra=
nsform: translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(-=
-tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw=
-scale-x)) scaleY(var(--tw-scale-y)); }

.hover\:scale-\[1\.02\]:hover { --tw-scale-x: 1.02; --tw-scale-y: 1.02; tra=
nsform: translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(-=
-tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw=
-scale-x)) scaleY(var(--tw-scale-y)); }

.hover\:border-primary\/30:hover { border-color: hsl(var(--primary) / .3); =
}

.hover\:border-primary\/50:hover { border-color: hsl(var(--primary) / .5); =
}

.hover\:bg-accent:hover { background-color: hsl(var(--accent)); }

.hover\:bg-accent\/50:hover { background-color: hsl(var(--accent) / .5); }

.hover\:bg-blue-200:hover { --tw-bg-opacity: 1; background-color: rgb(191 2=
19 254 / var(--tw-bg-opacity, 1)); }

.hover\:bg-blue-700:hover { --tw-bg-opacity: 1; background-color: rgb(29 78=
 216 / var(--tw-bg-opacity, 1)); }

.hover\:bg-destructive\/80:hover { background-color: hsl(var(--destructive)=
 / .8); }

.hover\:bg-destructive\/90:hover { background-color: hsl(var(--destructive)=
 / .9); }

.hover\:bg-muted:hover { background-color: hsl(var(--muted)); }

.hover\:bg-muted\/20:hover { background-color: hsl(var(--muted) / .2); }

.hover\:bg-muted\/30:hover { background-color: hsl(var(--muted) / .3); }

.hover\:bg-muted\/50:hover { background-color: hsl(var(--muted) / .5); }

.hover\:bg-primary:hover { background-color: hsl(var(--primary)); }

.hover\:bg-primary\/80:hover { background-color: hsl(var(--primary) / .8); =
}

.hover\:bg-primary\/90:hover { background-color: hsl(var(--primary) / .9); =
}

.hover\:bg-secondary:hover { background-color: hsl(var(--secondary)); }

.hover\:bg-secondary\/80:hover { background-color: hsl(var(--secondary) / .=
8); }

.hover\:bg-sidebar-accent:hover { background-color: hsl(var(--sidebar-accen=
t)); }

.hover\:bg-sidebar-accent\/50:hover { background-color: hsl(var(--sidebar-a=
ccent) / .5); }

.hover\:bg-slate-100:hover { --tw-bg-opacity: 1; background-color: rgb(241 =
245 249 / var(--tw-bg-opacity, 1)); }

.hover\:bg-slate-50:hover { --tw-bg-opacity: 1; background-color: rgb(248 2=
50 252 / var(--tw-bg-opacity, 1)); }

.hover\:bg-slate-700:hover { --tw-bg-opacity: 1; background-color: rgb(51 6=
5 85 / var(--tw-bg-opacity, 1)); }

.hover\:bg-success\/90:hover { background-color: hsl(var(--success) / .9); =
}

.hover\:bg-transparent:hover { background-color: transparent; }

.hover\:bg-warning\/90:hover { background-color: hsl(var(--warning) / .9); =
}

.hover\:text-accent-foreground:hover { color: hsl(var(--accent-foreground))=
; }

.hover\:text-blue-700:hover { --tw-text-opacity: 1; color: rgb(29 78 216 / =
var(--tw-text-opacity, 1)); }

.hover\:text-destructive:hover { color: hsl(var(--destructive)); }

.hover\:text-foreground:hover { color: hsl(var(--foreground)); }

.hover\:text-muted-foreground:hover { color: hsl(var(--muted-foreground)); =
}

.hover\:text-primary:hover { color: hsl(var(--primary)); }

.hover\:text-primary-foreground:hover { color: hsl(var(--primary-foreground=
)); }

.hover\:text-sidebar-accent-foreground:hover { color: hsl(var(--sidebar-acc=
ent-foreground)); }

.hover\:text-slate-800:hover { --tw-text-opacity: 1; color: rgb(30 41 59 / =
var(--tw-text-opacity, 1)); }

.hover\:underline:hover { text-decoration-line: underline; }

.hover\:opacity-100:hover { opacity: 1; }

.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover { --tw-sh=
adow: 0 0 0 1px hsl(var(--sidebar-accent)); --tw-shadow-colored: 0 0 0 1px =
var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000)=
,var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow); }

.hover\:shadow-elevated:hover { --tw-shadow: var(--shadow-elevated); --tw-s=
hadow-colored: var(--shadow-elevated); box-shadow: var(--tw-ring-offset-sha=
dow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow); }

.hover\:shadow-glow:hover { --tw-shadow: var(--shadow-glow); --tw-shadow-co=
lored: var(--shadow-glow); box-shadow: var(--tw-ring-offset-shadow, 0 0 #00=
00),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow); }

.hover\:shadow-lg:hover { --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 =
4px 6px -4px rgb(0 0 0 / .1); --tw-shadow-colored: 0 10px 15px -3px var(--t=
w-shadow-color), 0 4px 6px -4px var(--tw-shadow-color); box-shadow: var(--t=
w-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-=
shadow); }

.hover\:shadow-md:hover { --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2p=
x 4px -2px rgb(0 0 0 / .1); --tw-shadow-colored: 0 4px 6px -1px var(--tw-sh=
adow-color), 0 2px 4px -2px var(--tw-shadow-color); box-shadow: var(--tw-ri=
ng-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shad=
ow); }

.hover\:shadow-sm:hover { --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05); --tw-s=
hadow-colored: 0 1px 2px 0 var(--tw-shadow-color); box-shadow: var(--tw-rin=
g-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shado=
w); }

.hover\:after\:bg-sidebar-border:hover::after { content: var(--tw-content);=
 background-color: hsl(var(--sidebar-border)); }

.focus\:bg-accent:focus { background-color: hsl(var(--accent)); }

.focus\:bg-primary:focus { background-color: hsl(var(--primary)); }

.focus\:text-accent-foreground:focus { color: hsl(var(--accent-foreground))=
; }

.focus\:text-primary-foreground:focus { color: hsl(var(--primary-foreground=
)); }

.focus\:opacity-100:focus { opacity: 1; }

.focus\:outline-none:focus { outline: transparent solid 2px; outline-offset=
: 2px; }

.focus\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 =
var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: =
var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw=
-ring-color); box-shadow: var(--tw-ring-offset-shadow),var(--tw-ring-shadow=
),var(--tw-shadow, 0 0 #0000); }

.focus\:ring-ring:focus { --tw-ring-color: hsl(var(--ring)); }

.focus\:ring-offset-2:focus { --tw-ring-offset-width: 2px; }

.focus-visible\:outline-none:focus-visible { outline: transparent solid 2px=
; outline-offset: 2px; }

.focus-visible\:ring-0:focus-visible { --tw-ring-offset-shadow: var(--tw-ri=
ng-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --=
tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-=
width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow),var(=
--tw-ring-shadow),var(--tw-shadow, 0 0 #0000); }

.focus-visible\:ring-1:focus-visible { --tw-ring-offset-shadow: var(--tw-ri=
ng-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --=
tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-=
width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow),var(=
--tw-ring-shadow),var(--tw-shadow, 0 0 #0000); }

.focus-visible\:ring-2:focus-visible { --tw-ring-offset-shadow: var(--tw-ri=
ng-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --=
tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-=
width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow),var(=
--tw-ring-shadow),var(--tw-shadow, 0 0 #0000); }

.focus-visible\:ring-ring:focus-visible { --tw-ring-color: hsl(var(--ring))=
; }

.focus-visible\:ring-sidebar-ring:focus-visible { --tw-ring-color: hsl(var(=
--sidebar-ring)); }

.focus-visible\:ring-offset-1:focus-visible { --tw-ring-offset-width: 1px; =
}

.focus-visible\:ring-offset-2:focus-visible { --tw-ring-offset-width: 2px; =
}

.focus-visible\:ring-offset-background:focus-visible { --tw-ring-offset-col=
or: hsl(var(--background)); }

.active\:bg-sidebar-accent:active { background-color: hsl(var(--sidebar-acc=
ent)); }

.active\:text-sidebar-accent-foreground:active { color: hsl(var(--sidebar-a=
ccent-foreground)); }

.disabled\:pointer-events-none:disabled { pointer-events: none; }

.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

.disabled\:opacity-50:disabled { opacity: 0.5; }

.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100 =
{ opacity: 1; }

.group:hover .group-hover\:text-primary { color: hsl(var(--primary)); }

.group:hover .group-hover\:text-slate-600 { --tw-text-opacity: 1; color: rg=
b(71 85 105 / var(--tw-text-opacity, 1)); }

.group\/menu-item:hover .group-hover\/menu-item\:opacity-100, .group:hover =
.group-hover\:opacity-100 { opacity: 1; }

.group.destructive .group-\[\.destructive\]\:border-muted\/40 { border-colo=
r: hsl(var(--muted) / .4); }

.group.toaster .group-\[\.toaster\]\:border-border { border-color: hsl(var(=
--border)); }

.group.toast .group-\[\.toast\]\:bg-muted { background-color: hsl(var(--mut=
ed)); }

.group.toast .group-\[\.toast\]\:bg-primary { background-color: hsl(var(--p=
rimary)); }

.group.toaster .group-\[\.toaster\]\:bg-background { background-color: hsl(=
var(--background)); }

.group.destructive .group-\[\.destructive\]\:text-red-300 { --tw-text-opaci=
ty: 1; color: rgb(252 165 165 / var(--tw-text-opacity, 1)); }

.group.toast .group-\[\.toast\]\:text-muted-foreground { color: hsl(var(--m=
uted-foreground)); }

.group.toast .group-\[\.toast\]\:text-primary-foreground { color: hsl(var(-=
-primary-foreground)); }

.group.toaster .group-\[\.toaster\]\:text-foreground { color: hsl(var(--for=
eground)); }

.group.toaster .group-\[\.toaster\]\:shadow-lg { --tw-shadow: 0 10px 15px -=
3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1); --tw-shadow-colored: 0=
 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-colo=
r); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shado=
w, 0 0 #0000),var(--tw-shadow); }

.group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:=
hover { border-color: hsl(var(--destructive) / .3); }

.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover { =
background-color: hsl(var(--destructive)); }

.group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foregr=
ound:hover { color: hsl(var(--destructive-foreground)); }

.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover { --t=
w-text-opacity: 1; color: rgb(254 242 242 / var(--tw-text-opacity, 1)); }

.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus =
{ --tw-ring-color: hsl(var(--destructive)); }

.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus { --=
tw-ring-opacity: 1; --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacit=
y, 1)); }

.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:foc=
us { --tw-ring-offset-color: #dc2626; }

.peer\/menu-button:hover ~ .peer-hover\/menu-button\:text-sidebar-accent-fo=
reground { color: hsl(var(--sidebar-accent-foreground)); }

.peer:disabled ~ .peer-disabled\:cursor-not-allowed { cursor: not-allowed; =
}

.peer:disabled ~ .peer-disabled\:opacity-70 { opacity: 0.7; }

.has-\[\[data-variant\=3Dinset\]\]\:bg-sidebar:has([data-variant=3D"inset"]=
) { background-color: hsl(var(--sidebar-background)); }

.has-\[\:disabled\]\:opacity-50:has(:disabled) { opacity: 0.5; }

.group\/menu-item:has([data-sidebar=3D"menu-action"]) .group-has-\[\[data-s=
idebar\=3Dmenu-action\]\]\/menu-item\:pr-8 { padding-right: 2rem; }

.aria-disabled\:pointer-events-none[aria-disabled=3D"true"] { pointer-event=
s: none; }

.aria-disabled\:opacity-50[aria-disabled=3D"true"] { opacity: 0.5; }

.aria-selected\:bg-accent[aria-selected=3D"true"] { background-color: hsl(v=
ar(--accent)); }

.aria-selected\:bg-accent\/50[aria-selected=3D"true"] { background-color: h=
sl(var(--accent) / .5); }

.aria-selected\:text-accent-foreground[aria-selected=3D"true"] { color: hsl=
(var(--accent-foreground)); }

.aria-selected\:text-muted-foreground[aria-selected=3D"true"] { color: hsl(=
var(--muted-foreground)); }

.aria-selected\:opacity-100[aria-selected=3D"true"] { opacity: 1; }

.aria-selected\:opacity-30[aria-selected=3D"true"] { opacity: 0.3; }

.data-\[disabled\=3Dtrue\]\:pointer-events-none[data-disabled=3D"true"], .d=
ata-\[disabled\]\:pointer-events-none[data-disabled] { pointer-events: none=
; }

.data-\[panel-group-direction\=3Dvertical\]\:h-px[data-panel-group-directio=
n=3D"vertical"] { height: 1px; }

.data-\[panel-group-direction\=3Dvertical\]\:w-full[data-panel-group-direct=
ion=3D"vertical"] { width: 100%; }

.data-\[side\=3Dbottom\]\:translate-y-1[data-side=3D"bottom"] { --tw-transl=
ate-y: .25rem; transform: translate(var(--tw-translate-x),var(--tw-translat=
e-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y=
)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[side\=3Dleft\]\:-translate-x-1[data-side=3D"left"] { --tw-translate=
-x: -.25rem; transform: translate(var(--tw-translate-x),var(--tw-translate-=
y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y))=
 scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[side\=3Dright\]\:translate-x-1[data-side=3D"right"] { --tw-translat=
e-x: .25rem; transform: translate(var(--tw-translate-x),var(--tw-translate-=
y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y))=
 scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[side\=3Dtop\]\:-translate-y-1[data-side=3D"top"] { --tw-translate-y=
: -.25rem; transform: translate(var(--tw-translate-x),var(--tw-translate-y)=
) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) s=
caleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[state\=3Dchecked\]\:translate-x-5[data-state=3D"checked"] { --tw-tr=
anslate-x: 1.25rem; transform: translate(var(--tw-translate-x),var(--tw-tra=
nslate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-s=
kew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[state\=3Dunchecked\]\:translate-x-0[data-state=3D"unchecked"], .dat=
a-\[swipe\=3Dcancel\]\:translate-x-0[data-swipe=3D"cancel"] { --tw-translat=
e-x: 0px; transform: translate(var(--tw-translate-x),var(--tw-translate-y))=
 rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) sc=
aleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[swipe\=3Dend\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][da=
ta-swipe=3D"end"] { --tw-translate-x: var(--radix-toast-swipe-end-x); trans=
form: translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--t=
w-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-s=
cale-x)) scaleY(var(--tw-scale-y)); }

.data-\[swipe\=3Dmove\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][=
data-swipe=3D"move"] { --tw-translate-x: var(--radix-toast-swipe-move-x); t=
ransform: translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var=
(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--=
tw-scale-x)) scaleY(var(--tw-scale-y)); }

@keyframes accordion-up {=20
  0% { height: var(--radix-accordion-content-height); }
  100% { height: 0px; }
}

.data-\[state\=3Dclosed\]\:animate-accordion-up[data-state=3D"closed"] { an=
imation: 0.2s ease-out 0s 1 normal none running accordion-up; }

@keyframes accordion-down {=20
  0% { height: 0px; }
  100% { height: var(--radix-accordion-content-height); }
}

.data-\[state\=3Dopen\]\:animate-accordion-down[data-state=3D"open"] { anim=
ation: 0.2s ease-out 0s 1 normal none running accordion-down; }

.data-\[panel-group-direction\=3Dvertical\]\:flex-col[data-panel-group-dire=
ction=3D"vertical"] { flex-direction: column; }

.data-\[active\=3Dtrue\]\:bg-sidebar-accent[data-active=3D"true"] { backgro=
und-color: hsl(var(--sidebar-accent)); }

.data-\[active\]\:bg-accent\/50[data-active] { background-color: hsl(var(--=
accent) / .5); }

.data-\[selected\=3D\'true\'\]\:bg-accent[data-selected=3D"true"] { backgro=
und-color: hsl(var(--accent)); }

.data-\[state\=3Dactive\]\:bg-background[data-state=3D"active"] { backgroun=
d-color: hsl(var(--background)); }

.data-\[state\=3Dchecked\]\:bg-primary[data-state=3D"checked"] { background=
-color: hsl(var(--primary)); }

.data-\[state\=3Don\]\:bg-accent[data-state=3D"on"], .data-\[state\=3Dopen\=
]\:bg-accent[data-state=3D"open"] { background-color: hsl(var(--accent)); }

.data-\[state\=3Dopen\]\:bg-accent\/50[data-state=3D"open"] { background-co=
lor: hsl(var(--accent) / .5); }

.data-\[state\=3Dopen\]\:bg-secondary[data-state=3D"open"] { background-col=
or: hsl(var(--secondary)); }

.data-\[state\=3Dselected\]\:bg-muted[data-state=3D"selected"] { background=
-color: hsl(var(--muted)); }

.data-\[state\=3Dunchecked\]\:bg-input[data-state=3D"unchecked"] { backgrou=
nd-color: hsl(var(--input)); }

.data-\[active\=3Dtrue\]\:font-medium[data-active=3D"true"] { font-weight: =
500; }

.data-\[active\=3Dtrue\]\:text-sidebar-accent-foreground[data-active=3D"tru=
e"] { color: hsl(var(--sidebar-accent-foreground)); }

.data-\[selected\=3Dtrue\]\:text-accent-foreground[data-selected=3D"true"] =
{ color: hsl(var(--accent-foreground)); }

.data-\[state\=3Dactive\]\:text-foreground[data-state=3D"active"] { color: =
hsl(var(--foreground)); }

.data-\[state\=3Dchecked\]\:text-primary-foreground[data-state=3D"checked"]=
 { color: hsl(var(--primary-foreground)); }

.data-\[state\=3Don\]\:text-accent-foreground[data-state=3D"on"], .data-\[s=
tate\=3Dopen\]\:text-accent-foreground[data-state=3D"open"] { color: hsl(va=
r(--accent-foreground)); }

.data-\[state\=3Dopen\]\:text-muted-foreground[data-state=3D"open"] { color=
: hsl(var(--muted-foreground)); }

.data-\[disabled\=3Dtrue\]\:opacity-50[data-disabled=3D"true"], .data-\[dis=
abled\]\:opacity-50[data-disabled] { opacity: 0.5; }

.data-\[state\=3Dopen\]\:opacity-100[data-state=3D"open"] { opacity: 1; }

.data-\[state\=3Dactive\]\:shadow-sm[data-state=3D"active"] { --tw-shadow: =
0 1px 2px 0 rgb(0 0 0 / .05); --tw-shadow-colored: 0 1px 2px 0 var(--tw-sha=
dow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ri=
ng-shadow, 0 0 #0000),var(--tw-shadow); }

.data-\[swipe\=3Dmove\]\:transition-none[data-swipe=3D"move"] { transition-=
property: none; }

.data-\[state\=3Dclosed\]\:duration-300[data-state=3D"closed"] { transition=
-duration: 0.3s; }

.data-\[state\=3Dopen\]\:duration-500[data-state=3D"open"] { transition-dur=
ation: 0.5s; }

.data-\[motion\^\=3Dfrom-\]\:animate-in[data-motion^=3D"from-"], .data-\[st=
ate\=3Dopen\]\:animate-in[data-state=3D"open"], .data-\[state\=3Dvisible\]\=
:animate-in[data-state=3D"visible"] { animation-name: enter; animation-dura=
tion: 0.15s; --tw-enter-opacity: initial; --tw-enter-scale: initial; --tw-e=
nter-rotate: initial; --tw-enter-translate-x: initial; --tw-enter-translate=
-y: initial; }

.data-\[motion\^\=3Dto-\]\:animate-out[data-motion^=3D"to-"], .data-\[state=
\=3Dclosed\]\:animate-out[data-state=3D"closed"], .data-\[state\=3Dhidden\]=
\:animate-out[data-state=3D"hidden"], .data-\[swipe\=3Dend\]\:animate-out[d=
ata-swipe=3D"end"] { animation-name: exit; animation-duration: 0.15s; --tw-=
exit-opacity: initial; --tw-exit-scale: initial; --tw-exit-rotate: initial;=
 --tw-exit-translate-x: initial; --tw-exit-translate-y: initial; }

.data-\[motion\^\=3Dfrom-\]\:fade-in[data-motion^=3D"from-"] { --tw-enter-o=
pacity: 0; }

.data-\[motion\^\=3Dto-\]\:fade-out[data-motion^=3D"to-"], .data-\[state\=
=3Dclosed\]\:fade-out-0[data-state=3D"closed"] { --tw-exit-opacity: 0; }

.data-\[state\=3Dclosed\]\:fade-out-80[data-state=3D"closed"] { --tw-exit-o=
pacity: .8; }

.data-\[state\=3Dhidden\]\:fade-out[data-state=3D"hidden"] { --tw-exit-opac=
ity: 0; }

.data-\[state\=3Dopen\]\:fade-in-0[data-state=3D"open"], .data-\[state\=3Dv=
isible\]\:fade-in[data-state=3D"visible"] { --tw-enter-opacity: 0; }

.data-\[state\=3Dclosed\]\:zoom-out-95[data-state=3D"closed"] { --tw-exit-s=
cale: .95; }

.data-\[state\=3Dopen\]\:zoom-in-90[data-state=3D"open"] { --tw-enter-scale=
: .9; }

.data-\[state\=3Dopen\]\:zoom-in-95[data-state=3D"open"] { --tw-enter-scale=
: .95; }

.data-\[motion\=3Dfrom-end\]\:slide-in-from-right-52[data-motion=3D"from-en=
d"] { --tw-enter-translate-x: 13rem; }

.data-\[motion\=3Dfrom-start\]\:slide-in-from-left-52[data-motion=3D"from-s=
tart"] { --tw-enter-translate-x: -13rem; }

.data-\[motion\=3Dto-end\]\:slide-out-to-right-52[data-motion=3D"to-end"] {=
 --tw-exit-translate-x: 13rem; }

.data-\[motion\=3Dto-start\]\:slide-out-to-left-52[data-motion=3D"to-start"=
] { --tw-exit-translate-x: -13rem; }

.data-\[side\=3Dbottom\]\:slide-in-from-top-2[data-side=3D"bottom"] { --tw-=
enter-translate-y: -.5rem; }

.data-\[side\=3Dleft\]\:slide-in-from-right-2[data-side=3D"left"] { --tw-en=
ter-translate-x: .5rem; }

.data-\[side\=3Dright\]\:slide-in-from-left-2[data-side=3D"right"] { --tw-e=
nter-translate-x: -.5rem; }

.data-\[side\=3Dtop\]\:slide-in-from-bottom-2[data-side=3D"top"] { --tw-ent=
er-translate-y: .5rem; }

.data-\[state\=3Dclosed\]\:slide-out-to-bottom[data-state=3D"closed"] { --t=
w-exit-translate-y: 100%; }

.data-\[state\=3Dclosed\]\:slide-out-to-left[data-state=3D"closed"] { --tw-=
exit-translate-x: -100%; }

.data-\[state\=3Dclosed\]\:slide-out-to-left-1\/2[data-state=3D"closed"] { =
--tw-exit-translate-x: -50%; }

.data-\[state\=3Dclosed\]\:slide-out-to-right[data-state=3D"closed"], .data=
-\[state\=3Dclosed\]\:slide-out-to-right-full[data-state=3D"closed"] { --tw=
-exit-translate-x: 100%; }

.data-\[state\=3Dclosed\]\:slide-out-to-top[data-state=3D"closed"] { --tw-e=
xit-translate-y: -100%; }

.data-\[state\=3Dclosed\]\:slide-out-to-top-\[48\%\][data-state=3D"closed"]=
 { --tw-exit-translate-y: -48%; }

.data-\[state\=3Dopen\]\:slide-in-from-bottom[data-state=3D"open"] { --tw-e=
nter-translate-y: 100%; }

.data-\[state\=3Dopen\]\:slide-in-from-left[data-state=3D"open"] { --tw-ent=
er-translate-x: -100%; }

.data-\[state\=3Dopen\]\:slide-in-from-left-1\/2[data-state=3D"open"] { --t=
w-enter-translate-x: -50%; }

.data-\[state\=3Dopen\]\:slide-in-from-right[data-state=3D"open"] { --tw-en=
ter-translate-x: 100%; }

.data-\[state\=3Dopen\]\:slide-in-from-top[data-state=3D"open"] { --tw-ente=
r-translate-y: -100%; }

.data-\[state\=3Dopen\]\:slide-in-from-top-\[48\%\][data-state=3D"open"] { =
--tw-enter-translate-y: -48%; }

.data-\[state\=3Dopen\]\:slide-in-from-top-full[data-state=3D"open"] { --tw=
-enter-translate-y: -100%; }

.data-\[state\=3Dclosed\]\:duration-300[data-state=3D"closed"] { animation-=
duration: 0.3s; }

.data-\[state\=3Dopen\]\:duration-500[data-state=3D"open"] { animation-dura=
tion: 0.5s; }

.data-\[panel-group-direction\=3Dvertical\]\:after\:left-0[data-panel-group=
-direction=3D"vertical"]::after { content: var(--tw-content); left: 0px; }

.data-\[panel-group-direction\=3Dvertical\]\:after\:h-1[data-panel-group-di=
rection=3D"vertical"]::after { content: var(--tw-content); height: 0.25rem;=
 }

.data-\[panel-group-direction\=3Dvertical\]\:after\:w-full[data-panel-group=
-direction=3D"vertical"]::after { content: var(--tw-content); width: 100%; =
}

.data-\[panel-group-direction\=3Dvertical\]\:after\:-translate-y-1\/2[data-=
panel-group-direction=3D"vertical"]::after { content: var(--tw-content); --=
tw-translate-y: -50%; transform: translate(var(--tw-translate-x),var(--tw-t=
ranslate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw=
-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[panel-group-direction\=3Dvertical\]\:after\:translate-x-0[data-pane=
l-group-direction=3D"vertical"]::after { content: var(--tw-content); --tw-t=
ranslate-x: 0px; transform: translate(var(--tw-translate-x),var(--tw-transl=
ate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew=
-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.data-\[state\=3Dopen\]\:hover\:bg-sidebar-accent:hover[data-state=3D"open"=
] { background-color: hsl(var(--sidebar-accent)); }

.data-\[state\=3Dopen\]\:hover\:text-sidebar-accent-foreground:hover[data-s=
tate=3D"open"] { color: hsl(var(--sidebar-accent-foreground)); }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\] { left: calc(var(--sideba=
r-width) * -1); }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\] { right: calc(var(--side=
bar-width) * -1); }

.group[data-side=3D"left"] .group-data-\[side\=3Dleft\]\:-right-4 { right: =
-1rem; }

.group[data-side=3D"right"] .group-data-\[side\=3Dright\]\:left-0 { left: 0=
px; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:-mt-=
8 { margin-top: -2rem; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:hidd=
en { display: none; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:\!si=
ze-8 { width: 2rem !important; height: 2rem !important; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:w-\[=
--sidebar-width-icon\] { width: var(--sidebar-width-icon); }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:w-\[=
calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)\)\] { width: calc(=
var(--sidebar-width-icon) + 1rem); }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:w-\[=
calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)_\+2px\)\] { width:=
 calc(var(--sidebar-width-icon) + 1rem + 2px); }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:w-0 { width: 0px; }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:translate-x-0 { --tw-translate-x: 0px; transform: translate(var(--tw-=
translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-=
skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-=
scale-y)); }

.group[data-side=3D"right"] .group-data-\[side\=3Dright\]\:rotate-180, .gro=
up[data-state=3D"open"] .group-data-\[state\=3Dopen\]\:rotate-180 { --tw-ro=
tate: 180deg; transform: translate(var(--tw-translate-x),var(--tw-translate=
-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)=
) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:over=
flow-hidden { overflow: hidden; }

.group[data-variant=3D"floating"] .group-data-\[variant\=3Dfloating\]\:roun=
ded-lg { border-radius: var(--radius); }

.group[data-variant=3D"floating"] .group-data-\[variant\=3Dfloating\]\:bord=
er { border-width: 1px; }

.group[data-side=3D"left"] .group-data-\[side\=3Dleft\]\:border-r { border-=
right-width: 1px; }

.group[data-side=3D"right"] .group-data-\[side\=3Dright\]\:border-l { borde=
r-left-width: 1px; }

.group[data-variant=3D"floating"] .group-data-\[variant\=3Dfloating\]\:bord=
er-sidebar-border { border-color: hsl(var(--sidebar-border)); }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:\!p-=
0 { padding: 0px !important; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:\!p-=
2 { padding: 0.5rem !important; }

.group[data-collapsible=3D"icon"] .group-data-\[collapsible\=3Dicon\]\:opac=
ity-0 { opacity: 0; }

.group[data-variant=3D"floating"] .group-data-\[variant\=3Dfloating\]\:shad=
ow { --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .=
1); --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px=
 var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000=
),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow); }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:after\:left-full::after { content: var(--tw-content); left: 100%; }

.group[data-collapsible=3D"offcanvas"] .group-data-\[collapsible\=3Doffcanv=
as\]\:hover\:bg-sidebar:hover { background-color: hsl(var(--sidebar-backgro=
und)); }

.peer\/menu-button[data-size=3D"default"] ~ .peer-data-\[size\=3Ddefault\]\=
/menu-button\:top-1\.5 { top: 0.375rem; }

.peer\/menu-button[data-size=3D"lg"] ~ .peer-data-\[size\=3Dlg\]\/menu-butt=
on\:top-2\.5 { top: 0.625rem; }

.peer\/menu-button[data-size=3D"sm"] ~ .peer-data-\[size\=3Dsm\]\/menu-butt=
on\:top-1 { top: 0.25rem; }

.peer[data-variant=3D"inset"] ~ .peer-data-\[variant\=3Dinset\]\:min-h-\[ca=
lc\(100svh-theme\(spacing\.4\)\)\] { min-height: calc(-1rem + 100svh); }

.peer\/menu-button[data-active=3D"true"] ~ .peer-data-\[active\=3Dtrue\]\/m=
enu-button\:text-sidebar-accent-foreground { color: hsl(var(--sidebar-accen=
t-foreground)); }

.dark\:border-destructive:is(.dark *) { border-color: hsl(var(--destructive=
)); }

@media (min-width: 640px) {
  .sm\:bottom-0 { bottom: 0px; }
  .sm\:right-0 { right: 0px; }
  .sm\:top-auto { top: auto; }
  .sm\:mt-0 { margin-top: 0px; }
  .sm\:flex { display: flex; }
  .sm\:w-\[600px\] { width: 600px; }
  .sm\:max-w-\[600px\] { max-width: 600px; }
  .sm\:max-w-sm { max-width: 24rem; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:flex-col { flex-direction: column; }
  .sm\:justify-end { justify-content: flex-end; }
  .sm\:gap-2\.5 { gap: 0.625rem; }
  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: =
0; margin-right: calc(.5rem * var(--tw-space-x-reverse)); margin-left: calc=
(.5rem * calc(1 - var(--tw-space-x-reverse))); }
  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: =
0; margin-right: calc(1rem * var(--tw-space-x-reverse)); margin-left: calc(=
1rem * calc(1 - var(--tw-space-x-reverse))); }
  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: =
0; margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse))); margin-bott=
om: calc(0px * var(--tw-space-y-reverse)); }
  .sm\:rounded-lg { border-radius: var(--radius); }
  .sm\:text-left { text-align: left; }
  .data-\[state\=3Dopen\]\:sm\:slide-in-from-bottom-full[data-state=3D"open=
"] { --tw-enter-translate-y: 100%; }
}

@media (min-width: 768px) {
  .md\:absolute { position: absolute; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:w-1\/2 { width: 50%; }
  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] { width: var(--r=
adix-navigation-menu-viewport-width); }
  .md\:w-auto { width: auto; }
  .md\:max-w-\[420px\] { max-width: 420px; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0px, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0px, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0px, 1fr)); }
  .md\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .md\:opacity-0 { opacity: 0; }
  .after\:md\:hidden::after { content: var(--tw-content); display: none; }
  .peer[data-variant=3D"inset"] ~ .md\:peer-data-\[variant\=3Dinset\]\:m-2 =
{ margin: 0.5rem; }
  .peer[data-state=3D"collapsed"][data-variant=3D"inset"] ~ .md\:peer-data-=
\[state\=3Dcollapsed\]\:peer-data-\[variant\=3Dinset\]\:ml-2 { margin-left:=
 0.5rem; }
  .peer[data-variant=3D"inset"] ~ .md\:peer-data-\[variant\=3Dinset\]\:ml-0=
 { margin-left: 0px; }
  .peer[data-variant=3D"inset"] ~ .md\:peer-data-\[variant\=3Dinset\]\:roun=
ded-xl { border-radius: 0.75rem; }
  .peer[data-variant=3D"inset"] ~ .md\:peer-data-\[variant\=3Dinset\]\:shad=
ow { --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .=
1); --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px=
 var(--tw-shadow-color); box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000=
),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow); }
}

@media (min-width: 1024px) {
  .lg\:col-span-1 { grid-column: span 1 / span 1; }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0px, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0px, 1fr)); }
}

.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) { backgro=
und-color: hsl(var(--accent)); }

.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has([aria-selected])=
:first-child { border-top-left-radius: calc(var(--radius) - 2px); border-bo=
ttom-left-radius: calc(var(--radius) - 2px); }

.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has([aria-selected]):=
last-child { border-top-right-radius: calc(var(--radius) - 2px); border-bot=
tom-right-radius: calc(var(--radius) - 2px); }

.\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has([aria-sel=
ected].day-outside) { background-color: hsl(var(--accent) / .5); }

.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-se=
lected].day-range-end) { border-top-right-radius: calc(var(--radius) - 2px)=
; border-bottom-right-radius: calc(var(--radius) - 2px); }

.\[\&\:has\(\[role\=3Dcheckbox\]\)\]\:pr-0:has([role=3D"checkbox"]) { paddi=
ng-right: 0px; }

.\[\&\>button\]\:hidden > button { display: none; }

.\[\&\>span\:last-child\]\:truncate > span:last-child { overflow: hidden; t=
ext-overflow: ellipsis; white-space: nowrap; }

.\[\&\>span\]\:line-clamp-1 > span { overflow: hidden; display: -webkit-box=
; -webkit-box-orient: vertical; -webkit-line-clamp: 1; }

.\[\&\>svg\+div\]\:translate-y-\[-3px\] > svg + div { --tw-translate-y: -3p=
x; transform: translate(var(--tw-translate-x),var(--tw-translate-y)) rotate=
(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(va=
r(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.\[\&\>svg\]\:absolute > svg { position: absolute; }

.\[\&\>svg\]\:left-4 > svg { left: 1rem; }

.\[\&\>svg\]\:top-4 > svg { top: 1rem; }

.\[\&\>svg\]\:size-3\.5 > svg { width: 0.875rem; height: 0.875rem; }

.\[\&\>svg\]\:size-4 > svg { width: 1rem; height: 1rem; }

.\[\&\>svg\]\:h-2\.5 > svg { height: 0.625rem; }

.\[\&\>svg\]\:h-3 > svg { height: 0.75rem; }

.\[\&\>svg\]\:w-2\.5 > svg { width: 0.625rem; }

.\[\&\>svg\]\:w-3 > svg { width: 0.75rem; }

.\[\&\>svg\]\:shrink-0 > svg { flex-shrink: 0; }

.\[\&\>svg\]\:text-destructive > svg { color: hsl(var(--destructive)); }

.\[\&\>svg\]\:text-foreground > svg { color: hsl(var(--foreground)); }

.\[\&\>svg\]\:text-muted-foreground > svg { color: hsl(var(--muted-foregrou=
nd)); }

.\[\&\>svg\]\:text-sidebar-accent-foreground > svg { color: hsl(var(--sideb=
ar-accent-foreground)); }

.\[\&\>svg\~\*\]\:pl-7 > svg ~ * { padding-left: 1.75rem; }

.\[\&\>tr\]\:last\:border-b-0:last-child > tr { border-bottom-width: 0px; }

.\[\&\[data-panel-group-direction\=3Dvertical\]\>div\]\:rotate-90[data-pane=
l-group-direction=3D"vertical"] > div { --tw-rotate: 90deg; transform: tran=
slate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate))=
 skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) s=
caleY(var(--tw-scale-y)); }

.\[\&\[data-state\=3Dopen\]\>svg\]\:rotate-180[data-state=3D"open"] > svg {=
 --tw-rotate: 180deg; transform: translate(var(--tw-translate-x),var(--tw-t=
ranslate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw=
-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .rechart=
s-cartesian-axis-tick text { fill: hsl(var(--muted-foreground)); }

.\[\&_\.recharts-cartesian-grid_line\[stroke\=3D\'\#ccc\'\]\]\:stroke-borde=
r\/50 .recharts-cartesian-grid line[stroke=3D"#ccc"] { stroke: hsl(var(--bo=
rder) / .5); }

.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-=
curve.recharts-tooltip-cursor { stroke: hsl(var(--border)); }

.\[\&_\.recharts-dot\[stroke\=3D\'\#fff\'\]\]\:stroke-transparent .recharts=
-dot[stroke=3D"#fff"] { stroke: transparent; }

.\[\&_\.recharts-layer\]\:outline-none .recharts-layer { outline: transpare=
nt solid 2px; outline-offset: 2px; }

.\[\&_\.recharts-polar-grid_\[stroke\=3D\'\#ccc\'\]\]\:stroke-border .recha=
rts-polar-grid [stroke=3D"#ccc"] { stroke: hsl(var(--border)); }

.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radia=
l-bar-background-sector, .\[\&_\.recharts-rectangle\.recharts-tooltip-curso=
r\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor { fill: hsl(var=
(--muted)); }

.\[\&_\.recharts-reference-line_\[stroke\=3D\'\#ccc\'\]\]\:stroke-border .r=
echarts-reference-line [stroke=3D"#ccc"] { stroke: hsl(var(--border)); }

.\[\&_\.recharts-sector\[stroke\=3D\'\#fff\'\]\]\:stroke-transparent .recha=
rts-sector[stroke=3D"#fff"] { stroke: transparent; }

.\[\&_\.recharts-sector\]\:outline-none .recharts-sector, .\[\&_\.recharts-=
surface\]\:outline-none .recharts-surface { outline: transparent solid 2px;=
 outline-offset: 2px; }

.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] { padding-left: 0=
.5rem; padding-right: 0.5rem; }

.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] { padding-top:=
 0.375rem; padding-bottom: 0.375rem; }

.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] { font-size: 0=
.75rem; line-height: 1rem; }

.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] { font-wei=
ght: 500; }

.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] =
{ color: hsl(var(--muted-foreground)); }

.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-grou=
p]:not([hidden]) ~ [cmdk-group] { padding-top: 0px; }

.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] { padding-left: 0.5rem; padding-r=
ight: 0.5rem; }

.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg { height: =
1.25rem; }

.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg { width: 1=
.25rem; }

.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] { height: 3rem; }

.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] { padding-left: 0.5rem; padding-rig=
ht: 0.5rem; }

.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] { padding-top: 0.75rem; padding-bot=
tom: 0.75rem; }

.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg { height: 1.25rem; }

.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg { width: 1.25rem; }

.\[\&_p\]\:leading-relaxed p { line-height: 1.625; }

.\[\&_svg\]\:pointer-events-none svg { pointer-events: none; }

.\[\&_svg\]\:size-4 svg { width: 1rem; height: 1rem; }

.\[\&_svg\]\:shrink-0 svg { flex-shrink: 0; }

.\[\&_tr\:last-child\]\:border-0 tr:last-child { border-width: 0px; }

.\[\&_tr\]\:border-b tr { border-bottom-width: 1px; }

[data-side=3D"left"][data-collapsible=3D"offcanvas"] .\[\[data-side\=3Dleft=
\]\[data-collapsible\=3Doffcanvas\]_\&\]\:-right-2 { right: -0.5rem; }

[data-side=3D"left"][data-state=3D"collapsed"] .\[\[data-side\=3Dleft\]\[da=
ta-state\=3Dcollapsed\]_\&\]\:cursor-e-resize { cursor: e-resize; }

[data-side=3D"left"] .\[\[data-side\=3Dleft\]_\&\]\:cursor-w-resize { curso=
r: w-resize; }

[data-side=3D"right"][data-collapsible=3D"offcanvas"] .\[\[data-side\=3Drig=
ht\]\[data-collapsible\=3Doffcanvas\]_\&\]\:-left-2 { left: -0.5rem; }

[data-side=3D"right"][data-state=3D"collapsed"] .\[\[data-side\=3Dright\]\[=
data-state\=3Dcollapsed\]_\&\]\:cursor-w-resize { cursor: w-resize; }

[data-side=3D"right"] .\[\[data-side\=3Dright\]_\&\]\:cursor-e-resize { cur=
sor: e-resize; }
------MultipartBoundary--1OOam4t37Q4W0wYzOsI1GkthzSLBuOURyLF2x4eASm----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

:where(html[dir=3D"ltr"]), :where([data-sonner-toaster][dir=3D"ltr"]) { --t=
oast-icon-margin-start: -3px; --toast-icon-margin-end: 4px; --toast-svg-mar=
gin-start: -1px; --toast-svg-margin-end: 0px; --toast-button-margin-start: =
auto; --toast-button-margin-end: 0; --toast-close-button-start: 0; --toast-=
close-button-end: unset; --toast-close-button-transform: translate(-35%, -3=
5%); }

:where(html[dir=3D"rtl"]), :where([data-sonner-toaster][dir=3D"rtl"]) { --t=
oast-icon-margin-start: 4px; --toast-icon-margin-end: -3px; --toast-svg-mar=
gin-start: 0px; --toast-svg-margin-end: -1px; --toast-button-margin-start: =
0; --toast-button-margin-end: auto; --toast-close-button-start: unset; --to=
ast-close-button-end: 0; --toast-close-button-transform: translate(35%, -35=
%); }

:where([data-sonner-toaster]) { position: fixed; width: var(--width); font-=
family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe=
 UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Colo=
r Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; --gray1:=
 hsl(0, 0%, 99%); --gray2: hsl(0, 0%, 97.3%); --gray3: hsl(0, 0%, 95.1%); -=
-gray4: hsl(0, 0%, 93%); --gray5: hsl(0, 0%, 90.9%); --gray6: hsl(0, 0%, 88=
.7%); --gray7: hsl(0, 0%, 85.8%); --gray8: hsl(0, 0%, 78%); --gray9: hsl(0,=
 0%, 56.1%); --gray10: hsl(0, 0%, 52.3%); --gray11: hsl(0, 0%, 43.5%); --gr=
ay12: hsl(0, 0%, 9%); --border-radius: 8px; box-sizing: border-box; padding=
: 0px; margin: 0px; list-style: none; outline: none; z-index: 999999999; }

:where([data-sonner-toaster][data-x-position=3D"right"]) { right: max(var(-=
-offset),env(safe-area-inset-right)); }

:where([data-sonner-toaster][data-x-position=3D"left"]) { left: max(var(--o=
ffset),env(safe-area-inset-left)); }

:where([data-sonner-toaster][data-x-position=3D"center"]) { left: 50%; tran=
sform: translate(-50%); }

:where([data-sonner-toaster][data-y-position=3D"top"]) { top: max(var(--off=
set),env(safe-area-inset-top)); }

:where([data-sonner-toaster][data-y-position=3D"bottom"]) { bottom: max(var=
(--offset),env(safe-area-inset-bottom)); }

:where([data-sonner-toast]) { --y: translateY(100%); --lift-amount: calc(va=
r(--lift) * var(--gap)); z-index: var(--z-index); position: absolute; opaci=
ty: 0; transform: var(--y); filter: blur(0px); touch-action: none; transiti=
on: transform 0.4s, opacity 0.4s, height 0.4s, box-shadow 0.2s; box-sizing:=
 border-box; outline: none; overflow-wrap: anywhere; }

:where([data-sonner-toast][data-styled=3D"true"]) { padding: 16px; backgrou=
nd: var(--normal-bg); border: 1px solid var(--normal-border); color: var(--=
normal-text); border-radius: var(--border-radius); box-shadow: rgba(0, 0, 0=
, 0.1) 0px 4px 12px; width: var(--width); font-size: 13px; display: flex; a=
lign-items: center; gap: 6px; }

:where([data-sonner-toast]:focus-visible) { box-shadow: rgba(0, 0, 0, 0.1) =
0px 4px 12px, rgba(0, 0, 0, 0.2) 0px 0px 0px 2px; }

:where([data-sonner-toast][data-y-position=3D"top"]) { top: 0px; --y: trans=
lateY(-100%); --lift: 1; --lift-amount: calc(1 * var(--gap)); }

:where([data-sonner-toast][data-y-position=3D"bottom"]) { bottom: 0px; --y:=
 translateY(100%); --lift: -1; --lift-amount: calc(var(--lift) * var(--gap)=
); }

:where([data-sonner-toast]) :where([data-description]) { font-weight: 400; =
line-height: 1.4; color: inherit; }

:where([data-sonner-toast]) :where([data-title]) { font-weight: 500; line-h=
eight: 1.5; color: inherit; }

:where([data-sonner-toast]) :where([data-icon]) { display: flex; height: 16=
px; width: 16px; position: relative; justify-content: flex-start; align-ite=
ms: center; flex-shrink: 0; margin-left: var(--toast-icon-margin-start); ma=
rgin-right: var(--toast-icon-margin-end); }

:where([data-sonner-toast][data-promise=3D"true"]) :where([data-icon]) > sv=
g { opacity: 0; transform: scale(0.8); transform-origin: center center; ani=
mation: 0.3s ease 0s 1 normal forwards running sonner-fade-in; }

:where([data-sonner-toast]) :where([data-icon]) > * { flex-shrink: 0; }

:where([data-sonner-toast]) :where([data-icon]) svg { margin-left: var(--to=
ast-svg-margin-start); margin-right: var(--toast-svg-margin-end); }

:where([data-sonner-toast]) :where([data-content]) { display: flex; flex-di=
rection: column; gap: 2px; }

[data-sonner-toast][data-styled=3D"true"] [data-button] { border-radius: 4p=
x; padding-left: 8px; padding-right: 8px; height: 24px; font-size: 12px; co=
lor: var(--normal-bg); background: var(--normal-text); margin-left: var(--t=
oast-button-margin-start); margin-right: var(--toast-button-margin-end); bo=
rder: none; cursor: pointer; outline: none; display: flex; align-items: cen=
ter; flex-shrink: 0; transition: opacity 0.4s, box-shadow 0.2s; }

:where([data-sonner-toast]) :where([data-button]):focus-visible { box-shado=
w: rgba(0, 0, 0, 0.4) 0px 0px 0px 2px; }

:where([data-sonner-toast]) :where([data-button]):first-of-type { margin-le=
ft: var(--toast-button-margin-start); margin-right: var(--toast-button-marg=
in-end); }

:where([data-sonner-toast]) :where([data-cancel]) { color: var(--normal-tex=
t); background: rgba(0, 0, 0, 0.08); }

:where([data-sonner-toast][data-theme=3D"dark"]) :where([data-cancel]) { ba=
ckground: rgba(255, 255, 255, 0.3); }

:where([data-sonner-toast]) :where([data-close-button]) { position: absolut=
e; left: var(--toast-close-button-start); right: var(--toast-close-button-e=
nd); top: 0px; height: 20px; width: 20px; display: flex; justify-content: c=
enter; align-items: center; padding: 0px; background: var(--gray1); color: =
var(--gray12); border: 1px solid var(--gray4); transform: var(--toast-close=
-button-transform); border-radius: 50%; cursor: pointer; z-index: 1; transi=
tion: opacity 0.1s, background 0.2s, border-color 0.2s; }

:where([data-sonner-toast]) :where([data-close-button]):focus-visible { box=
-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px, rgba(0, 0, 0, 0.2) 0px 0px 0px 2p=
x; }

:where([data-sonner-toast]) :where([data-disabled=3D"true"]) { cursor: not-=
allowed; }

:where([data-sonner-toast]):hover :where([data-close-button]):hover { backg=
round: var(--gray2); border-color: var(--gray5); }

:where([data-sonner-toast][data-swiping=3D"true"])::before { content: ""; p=
osition: absolute; left: 0px; right: 0px; height: 100%; z-index: -1; }

:where([data-sonner-toast][data-y-position=3D"top"][data-swiping=3D"true"])=
::before { bottom: 50%; transform: scaleY(3) translateY(50%); }

:where([data-sonner-toast][data-y-position=3D"bottom"][data-swiping=3D"true=
"])::before { top: 50%; transform: scaleY(3) translateY(-50%); }

:where([data-sonner-toast][data-swiping=3D"false"][data-removed=3D"true"]):=
:before { content: ""; position: absolute; inset: 0px; transform: scaleY(2)=
; }

:where([data-sonner-toast])::after { content: ""; position: absolute; left:=
 0px; height: calc(var(--gap) + 1px); bottom: 100%; width: 100%; }

:where([data-sonner-toast][data-mounted=3D"true"]) { --y: translateY(0); op=
acity: 1; }

:where([data-sonner-toast][data-expanded=3D"false"][data-front=3D"false"]) =
{ --scale: var(--toasts-before) * .05 + 1; --y: translateY(calc(var(--lift-=
amount) * var(--toasts-before))) scale(calc(-1 * var(--scale))); height: va=
r(--front-toast-height); }

:where([data-sonner-toast]) > * { transition: opacity 0.4s; }

:where([data-sonner-toast][data-expanded=3D"false"][data-front=3D"false"][d=
ata-styled=3D"true"]) > * { opacity: 0; }

:where([data-sonner-toast][data-visible=3D"false"]) { opacity: 0; pointer-e=
vents: none; }

:where([data-sonner-toast][data-mounted=3D"true"][data-expanded=3D"true"]) =
{ --y: translateY(calc(var(--lift) * var(--offset))); height: var(--initial=
-height); }

:where([data-sonner-toast][data-removed=3D"true"][data-front=3D"true"][data=
-swipe-out=3D"false"]) { --y: translateY(calc(var(--lift) * -100%)); opacit=
y: 0; }

:where([data-sonner-toast][data-removed=3D"true"][data-front=3D"false"][dat=
a-swipe-out=3D"false"][data-expanded=3D"true"]) { --y: translateY(calc(var(=
--lift) * var(--offset) + var(--lift) * -100%)); opacity: 0; }

:where([data-sonner-toast][data-removed=3D"true"][data-front=3D"false"][dat=
a-swipe-out=3D"false"][data-expanded=3D"false"]) { --y: translateY(40%); op=
acity: 0; transition: transform 0.5s, opacity 0.2s; }

:where([data-sonner-toast][data-removed=3D"true"][data-front=3D"false"])::b=
efore { height: calc(var(--initial-height) + 20%); }

[data-sonner-toast][data-swiping=3D"true"] { transform: var(--y) translateY=
(var(--swipe-amount, 0px)); transition: none; }

[data-sonner-toast][data-swipe-out=3D"true"][data-y-position=3D"bottom"], [=
data-sonner-toast][data-swipe-out=3D"true"][data-y-position=3D"top"] { anim=
ation: 0.2s ease-out 0s 1 normal forwards running swipe-out; }

@keyframes swipe-out {=20
  0% { transform: translateY(calc(var(--lift) * var(--offset) + var(--swipe=
-amount))); opacity: 1; }
  100% { transform: translateY(calc(var(--lift) * var(--offset) + var(--swi=
pe-amount) + var(--lift) * -100%)); opacity: 0; }
}

@media (max-width: 600px) {
  [data-sonner-toaster] { position: fixed; --mobile-offset: 16px; right: va=
r(--mobile-offset); left: var(--mobile-offset); width: 100%; }
  [data-sonner-toaster] [data-sonner-toast] { left: 0px; right: 0px; width:=
 calc(100% - var(--mobile-offset) * 2); }
  [data-sonner-toaster][data-x-position=3D"left"] { left: var(--mobile-offs=
et); }
  [data-sonner-toaster][data-y-position=3D"bottom"] { bottom: 20px; }
  [data-sonner-toaster][data-y-position=3D"top"] { top: 20px; }
  [data-sonner-toaster][data-x-position=3D"center"] { left: var(--mobile-of=
fset); right: var(--mobile-offset); transform: none; }
}

[data-sonner-toaster][data-theme=3D"light"] { --normal-bg: #fff; --normal-b=
order: var(--gray4); --normal-text: var(--gray12); --success-bg: hsl(143, 8=
5%, 96%); --success-border: hsl(145, 92%, 91%); --success-text: hsl(140, 10=
0%, 27%); --info-bg: hsl(208, 100%, 97%); --info-border: hsl(221, 91%, 91%)=
; --info-text: hsl(210, 92%, 45%); --warning-bg: hsl(49, 100%, 97%); --warn=
ing-border: hsl(49, 91%, 91%); --warning-text: hsl(31, 92%, 45%); --error-b=
g: hsl(359, 100%, 97%); --error-border: hsl(359, 100%, 94%); --error-text: =
hsl(360, 100%, 45%); }

[data-sonner-toaster][data-theme=3D"light"] [data-sonner-toast][data-invert=
=3D"true"] { --normal-bg: #000; --normal-border: hsl(0, 0%, 20%); --normal-=
text: var(--gray1); }

[data-sonner-toaster][data-theme=3D"dark"] [data-sonner-toast][data-invert=
=3D"true"] { --normal-bg: #fff; --normal-border: var(--gray3); --normal-tex=
t: var(--gray12); }

[data-sonner-toaster][data-theme=3D"dark"] { --normal-bg: #000; --normal-bo=
rder: hsl(0, 0%, 20%); --normal-text: var(--gray1); --success-bg: hsl(150, =
100%, 6%); --success-border: hsl(147, 100%, 12%); --success-text: hsl(150, =
86%, 65%); --info-bg: hsl(215, 100%, 6%); --info-border: hsl(223, 100%, 12%=
); --info-text: hsl(216, 87%, 65%); --warning-bg: hsl(64, 100%, 6%); --warn=
ing-border: hsl(60, 100%, 12%); --warning-text: hsl(46, 87%, 65%); --error-=
bg: hsl(358, 76%, 10%); --error-border: hsl(357, 89%, 16%); --error-text: h=
sl(358, 100%, 81%); }

[data-rich-colors=3D"true"][data-sonner-toast][data-type=3D"success"], [dat=
a-rich-colors=3D"true"][data-sonner-toast][data-type=3D"success"] [data-clo=
se-button] { background: var(--success-bg); border-color: var(--success-bor=
der); color: var(--success-text); }

[data-rich-colors=3D"true"][data-sonner-toast][data-type=3D"info"], [data-r=
ich-colors=3D"true"][data-sonner-toast][data-type=3D"info"] [data-close-but=
ton] { background: var(--info-bg); border-color: var(--info-border); color:=
 var(--info-text); }

[data-rich-colors=3D"true"][data-sonner-toast][data-type=3D"warning"], [dat=
a-rich-colors=3D"true"][data-sonner-toast][data-type=3D"warning"] [data-clo=
se-button] { background: var(--warning-bg); border-color: var(--warning-bor=
der); color: var(--warning-text); }

[data-rich-colors=3D"true"][data-sonner-toast][data-type=3D"error"], [data-=
rich-colors=3D"true"][data-sonner-toast][data-type=3D"error"] [data-close-b=
utton] { background: var(--error-bg); border-color: var(--error-border); co=
lor: var(--error-text); }

.sonner-loading-wrapper { --size: 16px; height: var(--size); width: var(--s=
ize); position: absolute; inset: 0px; z-index: 10; }

.sonner-loading-wrapper[data-visible=3D"false"] { transform-origin: center =
center; animation: 0.2s ease 0s 1 normal forwards running sonner-fade-out; =
}

.sonner-spinner { position: relative; top: 50%; left: 50%; height: var(--si=
ze); width: var(--size); }

.sonner-loading-bar { animation: 1.2s linear 0s infinite normal none runnin=
g sonner-spin; background: var(--gray11); border-radius: 6px; height: 8%; l=
eft: -10%; position: absolute; top: -3.9%; width: 24%; }

.sonner-loading-bar:nth-child(1) { animation-delay: -1.2s; transform: rotat=
e(0.0001deg) translate(146%); }

.sonner-loading-bar:nth-child(2) { animation-delay: -1.1s; transform: rotat=
e(30deg) translate(146%); }

.sonner-loading-bar:nth-child(3) { animation-delay: -1s; transform: rotate(=
60deg) translate(146%); }

.sonner-loading-bar:nth-child(4) { animation-delay: -0.9s; transform: rotat=
e(90deg) translate(146%); }

.sonner-loading-bar:nth-child(5) { animation-delay: -0.8s; transform: rotat=
e(120deg) translate(146%); }

.sonner-loading-bar:nth-child(6) { animation-delay: -0.7s; transform: rotat=
e(150deg) translate(146%); }

.sonner-loading-bar:nth-child(7) { animation-delay: -0.6s; transform: rotat=
e(180deg) translate(146%); }

.sonner-loading-bar:nth-child(8) { animation-delay: -0.5s; transform: rotat=
e(210deg) translate(146%); }

.sonner-loading-bar:nth-child(9) { animation-delay: -0.4s; transform: rotat=
e(240deg) translate(146%); }

.sonner-loading-bar:nth-child(10) { animation-delay: -0.3s; transform: rota=
te(270deg) translate(146%); }

.sonner-loading-bar:nth-child(11) { animation-delay: -0.2s; transform: rota=
te(300deg) translate(146%); }

.sonner-loading-bar:nth-child(12) { animation-delay: -0.1s; transform: rota=
te(330deg) translate(146%); }

@keyframes sonner-fade-in {=20
  0% { opacity: 0; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes sonner-fade-out {=20
  0% { opacity: 1; transform: scale(1); }
  100% { opacity: 0; transform: scale(0.8); }
}

@keyframes sonner-spin {=20
  0% { opacity: 1; }
  100% { opacity: 0.15; }
}

@media (prefers-reduced-motion) {
  [data-sonner-toast], [data-sonner-toast] > *, .sonner-loading-bar { trans=
ition: none !important; animation: auto ease 0s 1 normal none running none =
!important; }
}

.sonner-loader { position: absolute; top: 50%; left: 50%; transform: transl=
ate(-50%, -50%); transform-origin: center center; transition: opacity 0.2s,=
 transform 0.2s; }

.sonner-loader[data-visible=3D"false"] { opacity: 0; transform: scale(0.8) =
translate(-50%, -50%); }
------MultipartBoundary--1OOam4t37Q4W0wYzOsI1GkthzSLBuOURyLF2x4eASm----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

#lovable-badge { position: fixed; bottom: 10px; right: 10px; width: 141px; =
padding: 5px 13px; background-color: rgb(0, 0, 0); color: rgb(255, 255, 255=
); font-size: 12px; border-radius: 5px; font-family: sans-serif; display: f=
lex; align-items: center; gap: 4px; z-index: 1000000; text-transform: none =
!important; font-feature-settings: normal !important; font-weight: 400 !imp=
ortant; }
------MultipartBoundary--1OOam4t37Q4W0wYzOsI1GkthzSLBuOURyLF2x4eASm------

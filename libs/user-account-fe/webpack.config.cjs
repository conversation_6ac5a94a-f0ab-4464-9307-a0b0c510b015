const path = require('path');

module.exports = {
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  
  entry: path.resolve(__dirname, 'src/index.ts'),
  
  output: {
    path: path.resolve(__dirname, '../../dist/libs/user-account-fe'),
    filename: 'index.js',
    library: {
      type: 'module'
    },
    clean: true
  },
  
  experiments: {
    outputModule: true
  },
  
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              configFile: path.resolve(__dirname, 'tsconfig.lib.json'),
              transpileOnly: false
            }
          }
        ],
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: [
          'style-loader',
          'css-loader',
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                config: path.resolve(__dirname, 'postcss.config.mjs')
              }
            }
          }
        ]
      },
      {
        test: /\.(svg|png|jpg|jpeg|gif|webp)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'assets/[name][ext]'
        }
      }
    ]
  },
  
  externals: [
    {
      react: {
        module: 'react'
      },
      'react-dom': {
        module: 'react-dom'
      },
      zustand: {
        module: 'zustand'
      }
    },
    // 排除所有 actions 目录的导入，避免服务端代码被打包到前端
    function({ request }, callback) {
      // 检查是否是 actions 目录的导入
      if (request && (
        request.includes('/actions/') ||
        request.includes('\\actions\\') ||
        request.endsWith('/actions') ||
        request.endsWith('\\actions')
      )) {
        return callback(null, 'commonjs ' + request);
      }
      callback();
    }
  ],
  
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx', '.css'],
    fallback: {
      fs: false,
      path: require.resolve('path-browserify'),
      http: require.resolve('stream-http'),
      https: require.resolve('https-browserify'),
      os: require.resolve('os-browserify/browser'),
      crypto: require.resolve('crypto-browserify'),
      stream: require.resolve('stream-browserify'),
      buffer: require.resolve('buffer'),
      util: require.resolve('util'),
      url: require.resolve('url'),
      zlib: require.resolve('browserify-zlib'),
      net: false,
      tls: false,
      module: false
    }
  },
  
  resolveLoader: {
    modules: [
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules')
    ]
  }
};
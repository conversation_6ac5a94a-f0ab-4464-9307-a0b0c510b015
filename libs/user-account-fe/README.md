# User Account Frontend

用户账户功能前端组件库

## 功能特性

- 用户登录/注册
- 用户信息管理
- 账户设置
- 用户头像管理

## 使用方法

```typescript
import { UserAccountFeature, UserProfile, LoginForm } from '@yai-investor-insight/user-account-fe';

// 在你的应用中使用
<UserAccountFeature />
```

## 组件

- `UserProfile`: 用户资料展示组件
- `LoginForm`: 登录表单组件
- `RegisterForm`: 注册表单组件
- `AccountSettings`: 账户设置组件

## 开发

```bash
# 运行测试
nx test user-account-fe

# 代码检查
nx lint user-account-fe
``` 
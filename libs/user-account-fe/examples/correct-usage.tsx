/**
 * 正确的使用方式示例
 * 展示如何在Client Components中正确调用Server Actions
 */

'use client';

import { useState } from 'react';
import { 
  sendOtpCode, 
  loginWithMobile, 
  getCurrentUser, 
  logout,
  getUserProfile 
} from '../src/lib/actions/userAccountActions';

// ✅ 正确方式1: 简单场景直接调用Server Actions
export function SimpleLoginForm() {
  const [mobile, setMobile] = useState('');
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSendOtp = async () => {
    if (!mobile) return;
    
    setLoading(true);
    try {
      await sendOtpCode(mobile); // 直接调用Server Action
      setMessage('验证码发送成功');
    } catch (error: any) {
      setMessage(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async () => {
    if (!mobile || !code) return;
    
    setLoading(true);
    try {
      const result = await loginWithMobile(mobile, code); // 直接调用Server Action
      setMessage('登录成功');
      console.log('登录结果:', result);
    } catch (error: any) {
      setMessage(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <input
        type="tel"
        value={mobile}
        onChange={(e) => setMobile(e.target.value)}
        placeholder="手机号"
        className="w-full p-2 border rounded"
      />
      
      <div className="flex space-x-2">
        <input
          type="text"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          placeholder="验证码"
          className="flex-1 p-2 border rounded"
        />
        <button
          onClick={handleSendOtp}
          disabled={loading}
          className="px-4 py-2 bg-gray-500 text-white rounded"
        >
          发送验证码
        </button>
      </div>
      
      <button
        onClick={handleLogin}
        disabled={loading}
        className="w-full p-2 bg-blue-500 text-white rounded"
      >
        {loading ? '处理中...' : '登录'}
      </button>
      
      {message && <p className="text-sm text-gray-600">{message}</p>}
    </div>
  );
}

// ✅ 正确方式2: 复杂场景可以创建自定义Hook封装Server Actions
function useAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendOtp = async (mobile: string) => {
    setLoading(true);
    setError(null);
    try {
      await sendOtpCode(mobile); // 调用Server Action
      return { success: true };
    } catch (err: any) {
      setError(err.message);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  const login = async (mobile: string, code: string) => {
    setLoading(true);
    setError(null);
    try {
      const result = await loginWithMobile(mobile, code); // 调用Server Action
      return { success: true, data: result };
    } catch (err: any) {
      setError(err.message);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  return { sendOtp, login, loading, error };
}

// ✅ 使用自定义Hook的组件
export function AdvancedLoginForm() {
  const [mobile, setMobile] = useState('');
  const [code, setCode] = useState('');
  const { sendOtp, login, loading, error } = useAuth();

  const handleSendOtp = () => sendOtp(mobile);
  const handleLogin = () => login(mobile, code);

  return (
    <div className="space-y-4">
      <input
        type="tel"
        value={mobile}
        onChange={(e) => setMobile(e.target.value)}
        placeholder="手机号"
        className="w-full p-2 border rounded"
      />
      
      <div className="flex space-x-2">
        <input
          type="text"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          placeholder="验证码"
          className="flex-1 p-2 border rounded"
        />
        <button
          onClick={handleSendOtp}
          disabled={loading}
          className="px-4 py-2 bg-gray-500 text-white rounded"
        >
          发送验证码
        </button>
      </div>
      
      <button
        onClick={handleLogin}
        disabled={loading}
        className="w-full p-2 bg-blue-500 text-white rounded"
      >
        {loading ? '处理中...' : '登录'}
      </button>
      
      {error && <p className="text-sm text-red-600">{error}</p>}
    </div>
  );
}

// ✅ 用户信息展示组件
export function UserProfile() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const loadUserProfile = async () => {
    setLoading(true);
    try {
      const profile = await getUserProfile(); // 直接调用Server Action
      setUser(profile);
    } catch (error) {
      console.error('Failed to load user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout(); // 直接调用Server Action
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <div className="space-y-4">
      {!user ? (
        <button
          onClick={loadUserProfile}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded"
        >
          {loading ? '加载中...' : '加载用户信息'}
        </button>
      ) : (
        <div>
          <h3>用户信息</h3>
          <p>姓名: {user.name}</p>
          <p>手机: {user.mobile}</p>
          <button
            onClick={handleLogout}
            className="px-4 py-2 bg-red-500 text-white rounded"
          >
            退出登录
          </button>
        </div>
      )}
    </div>
  );
}

// ❌ 错误方式: Client Component直接调用API Client
// import { sendOtpCode as sendOtpCodeApi } from '../api-client';
// const result = await sendOtpCodeApi(payload); // 这是错误的！

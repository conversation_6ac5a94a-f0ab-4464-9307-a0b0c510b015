{"name": "@yai-investor-insight/user-account-fe", "version": "0.0.1", "type": "module", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}}, "dependencies": {"@tanstack/react-query": "^5.83.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@yai-investor-insight/shared-fe-core": "workspace:*", "axios": "^1.11.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@nx/webpack": "^20.0.0", "@tailwindcss/postcss": "^4", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "css-loader": "^7.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss-loader": "^8.0.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^4.0.0", "tailwindcss": "^4", "ts-loader": "^9.5.0", "typescript": "^5.0.0", "url": "^0.11.4", "util": "^0.12.5", "webpack": "^5.90.0"}, "peerDependencies": {"iron-session": "^8.0.0", "next": "^15.0.0", "react": "^19.0.0", "react-dom": "^19.0.0"}}
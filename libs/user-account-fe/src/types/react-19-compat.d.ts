declare global {
  namespace React {
    interface ReactPortal {
      children?: React.ReactNode;
    }
    
    interface ReactElement {
      children?: React.ReactNode;
    }
    
    // Fix for React 19 type compatibility
    type ReactNode = import('react').ReactNode;
  }
}

// Fix for Next.js Link component compatibility with React 19
declare module 'next/link' {
  import { ComponentType } from 'react';
  import { LinkProps } from 'next/link';
  
  const Link: ComponentType<LinkProps & { children?: React.ReactNode }>;
  export default Link;
}

export {};
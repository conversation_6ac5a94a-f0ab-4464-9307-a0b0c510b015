import { createCoagentServer, CoagentAction, CoagentServerOptions, axios } from '@yai-investor-insight/shared-fe-core';

// 定义公共接口的 URL 列表
const PUBLIC_URLS = [
  '/account/login/authWithMobile',
  '/account/login/sendOtpCode',
  // 其他需要公开访问的 URL
];

// coagent 运行在服务端，可以直接访问 K8s 内部服务
const ACCOUNT_SERVICE_URL = 'http://yai-account-service-java:8080';

// 创建 axios 实例
const axiosInstance = axios.create({
  baseURL: ACCOUNT_SERVICE_URL,
  timeout: 30000,
});

// 全局认证配置，可以通过 setAuthConfig 设置
let globalAuthConfig: CoagentServerOptions | undefined;

/**
 * 设置全局认证配置
 * 这个函数应该在应用启动时由 web-app 调用
 */
export function setAuthConfig(authConfig: CoagentServerOptions) {
  globalAuthConfig = authConfig;
}

// 创建 coagent server，使用全局认证配置
function createCoagentServerWithAuth() {
  return createCoagentServer(axiosInstance, globalAuthConfig);
}

export const customInstance = async <T>(
  url: string,
  options: RequestInit = {},
): Promise<T> => {
  const method = (options.method || 'GET').toUpperCase();
  const isPublic = PUBLIC_URLS.includes(url);

  try {
    // 创建 coagent server（每次调用时创建，以确保使用最新的认证配置）
    const coagentServer = createCoagentServerWithAuth();
    
    // 创建 coagent action
    // 添加调试信息确保URL是字符串
    if (typeof url !== 'string') {
      console.error('URL is not a string in customInstance:', url, typeof url);
    }
    
    const action: CoagentAction = {
      type: method,
      payload: {
        url: url, // 使用相对路径，因为 baseURL 已经设置
        data: options.body ? JSON.parse(options.body as string) : undefined,
        headers: options.headers,
        isPublic,
      },
    };

    const response = await coagentServer.dispatch(action);
    
    // 构造符合 TypeScript 类型定义的响应对象
    // CoagentResponse 只包含 data 和 error 属性
    return {
      data: response.data,
      status: 200, // 默认状态码，因为 coagent 成功响应时没有 HTTP 状态码
      headers: new Headers() // 空的 Headers 对象
    } as T;

  } catch (error) {
    console.error(`Coagent request failed for URL: ${url}`, error);
    throw error;
  }
};

// 创建自定义实例的工厂函数
export function createCustomInstance(config: { baseURL: string }) {
  return {
    get: async <T>(url: string, options?: RequestInit): Promise<{ data: T }> => {
      console.log('createCustomInstance.get called with:', { url, config });
      
      // 直接创建 coagent server 和 action，避免双重URL拼接
      const coagentServer = createCoagentServerWithAuth();
      
      const action: CoagentAction = {
        type: 'GET',
        payload: {
          url: `${config.baseURL}${url}`, // 使用完整的相对路径
          headers: options?.headers,
          isPublic: PUBLIC_URLS.includes(`${config.baseURL}${url}`),
        },
      };
      
      const response = await coagentServer.dispatch(action);
      return { data: response.data as T };
    },
    post: async <T>(url: string, data?: any, options?: RequestInit): Promise<{ data: T }> => {
      const coagentServer = createCoagentServerWithAuth();
      
      const action: CoagentAction = {
        type: 'POST',
        payload: {
          url: `${config.baseURL}${url}`,
          data: data,
          headers: {
            'Content-Type': 'application/json',
            ...options?.headers,
          },
          isPublic: PUBLIC_URLS.includes(`${config.baseURL}${url}`),
        },
      };
      
      const response = await coagentServer.dispatch(action);
      return { data: response.data as T };
    },
    put: async <T>(url: string, data?: any, options?: RequestInit): Promise<{ data: T }> => {
      const coagentServer = createCoagentServerWithAuth();
      
      const action: CoagentAction = {
        type: 'PUT',
        payload: {
          url: `${config.baseURL}${url}`,
          data: data,
          headers: {
            'Content-Type': 'application/json',
            ...options?.headers,
          },
          isPublic: PUBLIC_URLS.includes(`${config.baseURL}${url}`),
        },
      };
      
      const response = await coagentServer.dispatch(action);
      return { data: response.data as T };
    },
    delete: async <T>(url: string, options?: RequestInit): Promise<{ data: T }> => {
      const coagentServer = createCoagentServerWithAuth();
      
      const action: CoagentAction = {
        type: 'DELETE',
        payload: {
          url: `${config.baseURL}${url}`,
          headers: options?.headers,
          isPublic: PUBLIC_URLS.includes(`${config.baseURL}${url}`),
        },
      };
      
      const response = await coagentServer.dispatch(action);
      return { data: response.data as T };
    }
  };
}

export default customInstance;
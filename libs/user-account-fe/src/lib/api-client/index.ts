/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * OpenAPI definition
 * OpenAPI spec version: v0
 */
import { customInstance } from "./custom-instance";
export type GateWayResponseObjectData = { [key: string]: unknown };

export interface GateWayResponseObject {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: GateWayResponseObjectData;
}

export interface GateWayResponseQrCodeVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: QrCodeVO;
}

export interface QrCodeVO {
  qrcode?: string;
}

/**
 * 用户信息VO
 */
export interface UserInfoVO {
  /** ID */
  id?: number;
  /**
   * 名字
   * @minLength 0
   * @maxLength 25
   */
  username: string;
  /**
   * 手机号
   * @pattern ^1[3-9]\d{9}$
   */
  mobile: string;
  /** 邮箱 */
  email?: string;
  /**
   * 学校
   * @minLength 0
   * @maxLength 60
   */
  school?: string;
  /** 考试科类 */
  exam_type?: string;
  /**
   * 排名
   * @minimum 1
   * @maximum 1500000
   */
  sort: number;
  /** 性别 */
  gender_key?: string;
  /**
   * @minLength 0
   * @maxLength 15
   */
  exam_no?: string;
  /** 省份 */
  province_key: string;
  /** 考试科目 */
  exam_subjects: string;
  /**
   * 总分
   * @minimum 0
   * @maximum 750
   */
  total_score: number;
  /**
   * 语文分数
   * @minimum 0
   * @maximum 150
   */
  chinese_score: number;
  /**
   * 数学分数
   * @minimum 0
   * @maximum 150
   */
  math_score: number;
  /**
   * 英语分数
   * @minimum 0
   * @maximum 150
   */
  english_score: number;
  /**
   * 物理分数
   * @minimum 0
   * @maximum 100
   */
  physics_score?: number;
  /**
   * 化学分数
   * @minimum 0
   * @maximum 100
   */
  chemistry_score?: number;
  /**
   * 生物分数
   * @minimum 0
   * @maximum 100
   */
  biology_score?: number;
  /**
   * 政治分数
   * @minimum 0
   * @maximum 100
   */
  politics_score?: number;
  /**
   * 历史分数
   * @minimum 0
   * @maximum 100
   */
  history_score?: number;
  /**
   * 地理分数
   * @minimum 0
   * @maximum 100
   */
  geography_score?: number;
  /**
   * 其他分数
   * @minimum 0
   */
  other_score?: number;
  /** 创建时间 */
  created_at?: string;
  /** 更新时间 */
  updated_at?: string;
  /** 删除时间，null表示未删除 */
  deleted_at?: string;
}

export interface EmptyData {
  [key: string]: unknown;
}

export interface GateWayResponseEmptyData {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: EmptyData;
}

export interface GateWayResponseListProvinceConfig {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: ProvinceConfig[];
}

export interface ProvinceConfig {
  /** key */
  key?: string;
  /** 省份名称 */
  name?: string;
  /** 选考科目类型 1表示3+1+2模式，2表示3+3模式 */
  type?: number;
}

export interface GateWayResponseUserInfoResVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: UserInfoResVO;
}

/**
 * 用户积分VO
 */
export interface UserCreditVo {
  /** 积分过期时间 */
  credit?: number;
  /** 积分过期时间 */
  expired_at?: string;
}

export interface UserInfoResVO {
  user_info_vo?: UserInfoVO;
  /** 是否已经填写初始化数据 0-手机号未填写 1-必填信息未填写 2-已填写 */
  init_data?: number;
  user_credit?: UserCreditVo;
}

export interface XcxLoginArgVo {
  /** 小程序授权手机号后的encryptedData */
  encryptedData: string;
  /** 小程序授权手机号后的iv */
  iv: string;
  /** 邀请人ID */
  inviterId?: number;
  /** 产品线编码 (qianfan_zhiyuan, stardust_whisper, investor_insight) */
  productLine: string;
  code?: string;
}

export type GateWayResponseData = { [key: string]: unknown };

export interface GateWayResponse {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: GateWayResponseData;
}

export interface XcxLoginV2ArgVo {
  /** 小程序登录后的code */
  loginCode: string;
  /** 手机号授权后的code */
  phoneNumberCode: string;
  /** 邀请人ID */
  inviterId?: number;
  /** 产品线编码 (qianfan_zhiyuan, stardust_whisper, investor_insight) */
  productLine: string;
}

export interface SendOtpCodeArgVO {
  /**
   * 手机号
   * @pattern ^1[3-9]\d{9}$
   */
  mobile: string;
  /** 微信临时token */
  wxTempToken?: string;
}

export interface WxCallbackArgVO {
  /** 微信回调的code */
  code: string;
  state?: string;
  /** 产品线编码 (qianfan_zhiyuan, stardust_whisper, investor_insight) */
  productLine: string;
}

export interface AuthResVO {
  /** token (使用HS256算法加密) */
  token?: string;
  user?: LoginUserVO;
  /** 是否已经填写初始化数据 0-手机号未填写 1-必填信息未填写 2-已填写 */
  init_data?: number;
  /** 微信临时token */
  wx_temp_token?: string;
}

export interface GateWayResponseAuthResVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: AuthResVO;
}

/**
 * 用户数据
 */
export interface LoginUserVO {
  /** ID */
  id?: number;
  /** 名字 */
  name?: string;
  /** 手机号 */
  mobile?: string;
  /** 头像 */
  avatar?: string;
  /** 创建时间 */
  created_at?: string;
  /** 更新时间 */
  updated_at?: string;
}

export interface AuthMobileArgVO {
  /**
   * 手机号
   * @pattern ^1[3-9]\d{9}$
   */
  mobile: string;
  /**
   * 短信验证码
   * @minLength 6
   * @maxLength 6
   * @pattern ^[0-9]+$
   */
  code: string;
  /** 邀请人ID */
  inviterId?: number;
  /** 产品线编码 (qianfan_zhiyuan, stardust_whisper, investor_insight) */
  productLine: string;
}

export interface GateWayResponseUserCreditVo {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: UserCreditVo;
}

/**
 * 积分商品信息
 */
export interface CreditProductResultVO {
  /** 商品标识 */
  key?: string;
  /** 商品价格，单位：元 */
  price?: string;
  /** 积分数量 */
  credit?: string;
}

export interface GateWayResponseListCreditProductResultVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: CreditProductResultVO[];
}

/**
 * 积分商品信息
 */
export interface CreditProductV2ResultVO {
  credit_product_list?: CreditProductResultVO[];
  credit_explain_url?: string;
}

export interface GateWayResponseCreditProductV2ResultVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: CreditProductV2ResultVO;
}

export interface OrderStatusArgVO {
  /** 订单id */
  order_id?: string;
}

export interface GateWayResponseOrderStatusVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: OrderStatusVO;
}

export interface OrderStatusVO {
  /** 订单id */
  order_id?: string;
  /** 订单状态 pending：未支付，success：支付成功，failed：支付失败 closed：关闭，包括支付超时 */
  status?: string;
}

/**
 * 积分明细入参
 */
export interface GetCreditListsArgVO {
  nextId?: string;
  pageSize?: number;
}

export interface GateWayResponseScrollPageDataGetCreditListsResultVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: ScrollPageDataGetCreditListsResultVO;
}

/**
 * 积分商品信息
 */
export interface GetCreditListsResultVO {
  /** id */
  id?: string;
  /** 描述 */
  title?: string;
  /** 积分类型 */
  credit_type?: string;
  /** 积分金额 */
  credit_amount?: string;
  /** 创建时间 */
  create_time?: string;
}

export interface ScrollPageDataGetCreditListsResultVO {
  nextDataId?: string;
  hasMore?: boolean;
  pageData?: GetCreditListsResultVO[];
}

export interface GateWayResponseGenerateReportConfirmCardResultVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: GenerateReportConfirmCardResultVO;
}

/**
 * 报告生成确认卡片
 */
export interface GenerateReportConfirmCardResultVO {
  /** 用户当前可用积分 */
  userCredit?: number;
  /** 原价 */
  originalPrice?: number;
  /** 现价 */
  currentPrice?: number;
}

/**
 * 创建订单请求
 */
export interface CreateOrderArgVO {
  /** 商品标识 */
  productKey?: string;
  /** 支付类型,微信扫码支付:WECHAT_SCAN,微信小程序支付:WECHAT_MINI,支付宝支付:ALIPAY */
  payType?: string;
}

/**
 * 创建订单响应
 */
export interface CreateOrderResultVO {
  /** 支付二维码URL */
  qrCode?: string;
  /** 订单号 */
  orderId?: string;
  miniAppWechatPayment?: PrepayVO;
}

export interface GateWayResponseCreateOrderResultVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: CreateOrderResultVO;
}

/**
 * 小程序支付参数
 */
export interface PrepayVO {
  appId?: string;
  timeStamp?: string;
  nonceStr?: string;
  packageStr?: string;
  signType?: string;
  paySign?: string;
}

export interface GateWayResponseWechatCallbackResultVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: WechatCallbackResultVO;
}

/**
 * 微信回调返回值
 */
export interface WechatCallbackResultVO {
  /** 错误码，SUCCESS为清算机构接收成功，其他错误码为失败。 */
  code?: string;
  /** 返回信息，如非空，为错误原因。 */
  message?: string;
}

export interface GateWayResponseQueryInviteRecordSummaryResultVO {
  code?: string;
  message?: string;
  debug?: string;
  showRequestId?: boolean;
  requestId?: string;
  data?: QueryInviteRecordSummaryResultVO;
}

/**
 * 查询邀请记录响应
 */
export interface QueryInviteRecordSummaryResultVO {
  invite_records?: ScrollPageDataQueryInviteRecordsResultVO;
  invite_count?: number;
  total_credit?: number;
}

/**
 * 查询邀请记录明细响应
 */
export interface QueryInviteRecordsResultVO {
  /** 记录ID */
  id?: number;
  /** 邀请人ID */
  inviter_id?: number;
  /** 被邀请人ID */
  invitee_id?: number;
  /** 邀请渠道 */
  channel?: string;
  /** 更新时间 */
  updated_at?: string;
  /** 创建时间 */
  created_at?: string;
  /** 奖励积分 */
  prize_credit_amount?: number;
  /** 用户名 */
  invitee_user_name?: string;
}

export interface ScrollPageDataQueryInviteRecordsResultVO {
  nextDataId?: string;
  hasMore?: boolean;
  pageData?: QueryInviteRecordsResultVO[];
}

export type Login1Params = {
  product_line: string;
};

export type QueryInviteRecordsParams = {
  channel: string;
  nextId?: string;
  pageSize?: number;
};

/**
 * @summary 获取微信二维码
 */
export type getWechatQrcodeResponse200 = {
  data: GateWayResponseQrCodeVO;
  status: 200;
};

export type getWechatQrcodeResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type getWechatQrcodeResponseComposite =
  | getWechatQrcodeResponse200
  | getWechatQrcodeResponse500;

export type getWechatQrcodeResponse = getWechatQrcodeResponseComposite & {
  headers: Headers;
};

export const getGetWechatQrcodeUrl = () => {
  return `/account/user/wechat/qrcode`;
};

export const getWechatQrcode = async (
  options?: RequestInit,
): Promise<getWechatQrcodeResponse> => {
  return customInstance<getWechatQrcodeResponse>(getGetWechatQrcodeUrl(), {
    ...options,
    method: "POST",
  });
};

/**
 * @summary 保存用户信息
 */
export type saveUserInfoResponse200 = {
  data: GateWayResponseEmptyData;
  status: 200;
};

export type saveUserInfoResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type saveUserInfoResponseComposite =
  | saveUserInfoResponse200
  | saveUserInfoResponse500;

export type saveUserInfoResponse = saveUserInfoResponseComposite & {
  headers: Headers;
};

export const getSaveUserInfoUrl = () => {
  return `/account/user/save`;
};

export const saveUserInfo = async (
  userInfoVO: UserInfoVO,
  options?: RequestInit,
): Promise<saveUserInfoResponse> => {
  return customInstance<saveUserInfoResponse>(getSaveUserInfoUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(userInfoVO),
  });
};

/**
 * @summary 获取省份列表
 */
export type getProvinceInfoResponse200 = {
  data: GateWayResponseListProvinceConfig;
  status: 200;
};

export type getProvinceInfoResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type getProvinceInfoResponseComposite =
  | getProvinceInfoResponse200
  | getProvinceInfoResponse500;

export type getProvinceInfoResponse = getProvinceInfoResponseComposite & {
  headers: Headers;
};

export const getGetProvinceInfoUrl = () => {
  return `/account/user/provinces`;
};

export const getProvinceInfo = async (
  options?: RequestInit,
): Promise<getProvinceInfoResponse> => {
  return customInstance<getProvinceInfoResponse>(getGetProvinceInfoUrl(), {
    ...options,
    method: "POST",
  });
};

/**
 * @summary 获取用户信息详情
 */
export type getUserInfoResponse200 = {
  data: GateWayResponseUserInfoResVO;
  status: 200;
};

export type getUserInfoResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type getUserInfoResponseComposite =
  | getUserInfoResponse200
  | getUserInfoResponse500;

export type getUserInfoResponse = getUserInfoResponseComposite & {
  headers: Headers;
};

export const getGetUserInfoUrl = () => {
  return `/account/user/detail`;
};

export const getUserInfo = async (
  options?: RequestInit,
): Promise<getUserInfoResponse> => {
  return customInstance<getUserInfoResponse>(getGetUserInfoUrl(), {
    ...options,
    method: "POST",
  });
};

/**
 * @summary 微信小程序登录
 */
export type loginResponse200 = {
  data: GateWayResponse;
  status: 200;
};

export type loginResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type loginResponseComposite = loginResponse200 | loginResponse500;

export type loginResponse = loginResponseComposite & {
  headers: Headers;
};

export const getLoginUrl = () => {
  return `/account/login/wx/xcxLogin`;
};

export const login = async (
  xcxLoginArgVo: XcxLoginArgVo,
  options?: RequestInit,
): Promise<loginResponse> => {
  return customInstance<loginResponse>(getLoginUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(xcxLoginArgVo),
  });
};

/**
 * @summary 微信小程序登录
 */
export type loginV2Response200 = {
  data: GateWayResponse;
  status: 200;
};

export type loginV2Response500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type loginV2ResponseComposite = loginV2Response200 | loginV2Response500;

export type loginV2Response = loginV2ResponseComposite & {
  headers: Headers;
};

export const getLoginV2Url = () => {
  return `/account/login/wx/xcxLoginV2`;
};

export const loginV2 = async (
  xcxLoginV2ArgVo: XcxLoginV2ArgVo,
  options?: RequestInit,
): Promise<loginV2Response> => {
  return customInstance<loginV2Response>(getLoginV2Url(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(xcxLoginV2ArgVo),
  });
};

/**
 * @summary 发送绑定微信验证码
 */
export type sendOtpCodeForBindWxResponse200 = {
  data: GateWayResponseEmptyData;
  status: 200;
};

export type sendOtpCodeForBindWxResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type sendOtpCodeForBindWxResponseComposite =
  | sendOtpCodeForBindWxResponse200
  | sendOtpCodeForBindWxResponse500;

export type sendOtpCodeForBindWxResponse =
  sendOtpCodeForBindWxResponseComposite & {
    headers: Headers;
  };

export const getSendOtpCodeForBindWxUrl = () => {
  return `/account/login/wx/sendOtpCodeForBindWx`;
};

export const sendOtpCodeForBindWx = async (
  sendOtpCodeArgVO: SendOtpCodeArgVO,
  options?: RequestInit,
): Promise<sendOtpCodeForBindWxResponse> => {
  return customInstance<sendOtpCodeForBindWxResponse>(
    getSendOtpCodeForBindWxUrl(),
    {
      ...options,
      method: "POST",
      headers: { "Content-Type": "application/json", ...options?.headers },
      body: JSON.stringify(sendOtpCodeArgVO),
    },
  );
};

/**
 * @summary 微信扫码登录
 */
export type login1Response200 = {
  data: GateWayResponse;
  status: 200;
};

export type login1Response500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type login1ResponseComposite = login1Response200 | login1Response500;

export type login1Response = login1ResponseComposite & {
  headers: Headers;
};

export const getLogin1Url = (params: Login1Params) => {
  const normalizedParams = new URLSearchParams();

  Object.entries(params || {}).forEach(([key, value]) => {
    if (value !== undefined) {
      normalizedParams.append(key, value === null ? "null" : value.toString());
    }
  });

  const stringifiedParams = normalizedParams.toString();

  return stringifiedParams.length > 0
    ? `/account/login/wx/login?${stringifiedParams}`
    : `/account/login/wx/login`;
};

export const login1 = async (
  params: Login1Params,
  options?: RequestInit,
): Promise<login1Response> => {
  return customInstance<login1Response>(getLogin1Url(params), {
    ...options,
    method: "POST",
  });
};

/**
 * @summary 微信扫码登录回调接口
 */
export type callbackResponse200 = {
  data: GateWayResponseAuthResVO;
  status: 200;
};

export type callbackResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type callbackResponseComposite =
  | callbackResponse200
  | callbackResponse500;

export type callbackResponse = callbackResponseComposite & {
  headers: Headers;
};

export const getCallbackUrl = () => {
  return `/account/login/wx/callback`;
};

export const callback = async (
  wxCallbackArgVO: WxCallbackArgVO,
  options?: RequestInit,
): Promise<callbackResponse> => {
  return customInstance<callbackResponse>(getCallbackUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(wxCallbackArgVO),
  });
};

/**
 * @summary 绑定手机号
 */
export type bindMobileResponse200 = {
  data: GateWayResponse;
  status: 200;
};

export type bindMobileResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type bindMobileResponseComposite =
  | bindMobileResponse200
  | bindMobileResponse500;

export type bindMobileResponse = bindMobileResponseComposite & {
  headers: Headers;
};

export const getBindMobileUrl = () => {
  return `/account/login/wx/bindMobile`;
};

export const bindMobile = async (
  authMobileArgVO: AuthMobileArgVO,
  options?: RequestInit,
): Promise<bindMobileResponse> => {
  return customInstance<bindMobileResponse>(getBindMobileUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(authMobileArgVO),
  });
};

/**
 * @summary 发送验证码
 */
export type sendOtpCodeResponse200 = {
  data: GateWayResponseEmptyData;
  status: 200;
};

export type sendOtpCodeResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type sendOtpCodeResponseComposite =
  | sendOtpCodeResponse200
  | sendOtpCodeResponse500;

export type sendOtpCodeResponse = sendOtpCodeResponseComposite & {
  headers: Headers;
};

export const getSendOtpCodeUrl = () => {
  return `/account/login/sendOtpCode`;
};

export const sendOtpCode = async (
  sendOtpCodeArgVO: SendOtpCodeArgVO,
  options?: RequestInit,
): Promise<sendOtpCodeResponse> => {
  return customInstance<sendOtpCodeResponse>(getSendOtpCodeUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(sendOtpCodeArgVO),
  });
};

/**
 * @summary 手机号注册或者登录
 */
export type authWithMobileResponse200 = {
  data: GateWayResponseAuthResVO;
  status: 200;
};

export type authWithMobileResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type authWithMobileResponseComposite =
  | authWithMobileResponse200
  | authWithMobileResponse500;

export type authWithMobileResponse = authWithMobileResponseComposite & {
  headers: Headers;
};

export const getAuthWithMobileUrl = () => {
  return `/account/login/authWithMobile`;
};

export const authWithMobile = async (
  authMobileArgVO: AuthMobileArgVO,
  options?: RequestInit,
): Promise<authWithMobileResponse> => {
  return customInstance<authWithMobileResponse>(getAuthWithMobileUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(authMobileArgVO),
  });
};

/**
 * @summary 获取用户积分信息
 */
export type getCreditInfoResponse200 = {
  data: GateWayResponseUserCreditVo;
  status: 200;
};

export type getCreditInfoResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type getCreditInfoResponseComposite =
  | getCreditInfoResponse200
  | getCreditInfoResponse500;

export type getCreditInfoResponse = getCreditInfoResponseComposite & {
  headers: Headers;
};

export const getGetCreditInfoUrl = () => {
  return `/account/credit/info`;
};

export const getCreditInfo = async (
  options?: RequestInit,
): Promise<getCreditInfoResponse> => {
  return customInstance<getCreditInfoResponse>(getGetCreditInfoUrl(), {
    ...options,
    method: "POST",
  });
};

/**
 * @summary 获取积分商品列表
 */
export type getProductListResponse200 = {
  data: GateWayResponseListCreditProductResultVO;
  status: 200;
};

export type getProductListResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type getProductListResponseComposite =
  | getProductListResponse200
  | getProductListResponse500;

export type getProductListResponse = getProductListResponseComposite & {
  headers: Headers;
};

export const getGetProductListUrl = () => {
  return `/account/credit/getProductList`;
};

export const getProductList = async (
  options?: RequestInit,
): Promise<getProductListResponse> => {
  return customInstance<getProductListResponse>(getGetProductListUrl(), {
    ...options,
    method: "POST",
  });
};

/**
 * @summary 获取积分商品列表
 */
export type getProductListV2Response200 = {
  data: GateWayResponseCreditProductV2ResultVO;
  status: 200;
};

export type getProductListV2Response500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type getProductListV2ResponseComposite =
  | getProductListV2Response200
  | getProductListV2Response500;

export type getProductListV2Response = getProductListV2ResponseComposite & {
  headers: Headers;
};

export const getGetProductListV2Url = () => {
  return `/account/credit/getProductListV2`;
};

export const getProductListV2 = async (
  options?: RequestInit,
): Promise<getProductListV2Response> => {
  return customInstance<getProductListV2Response>(getGetProductListV2Url(), {
    ...options,
    method: "POST",
  });
};

/**
 * @summary 获取订单状态
 */
export type getOrderStatusResponse200 = {
  data: GateWayResponseOrderStatusVO;
  status: 200;
};

export type getOrderStatusResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type getOrderStatusResponseComposite =
  | getOrderStatusResponse200
  | getOrderStatusResponse500;

export type getOrderStatusResponse = getOrderStatusResponseComposite & {
  headers: Headers;
};

export const getGetOrderStatusUrl = () => {
  return `/account/credit/getOrderStatus`;
};

export const getOrderStatus = async (
  orderStatusArgVO: OrderStatusArgVO,
  options?: RequestInit,
): Promise<getOrderStatusResponse> => {
  return customInstance<getOrderStatusResponse>(getGetOrderStatusUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(orderStatusArgVO),
  });
};

/**
 * @summary 获取积分明细
 */
export type getCreditListsResponse200 = {
  data: GateWayResponseScrollPageDataGetCreditListsResultVO;
  status: 200;
};

export type getCreditListsResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type getCreditListsResponseComposite =
  | getCreditListsResponse200
  | getCreditListsResponse500;

export type getCreditListsResponse = getCreditListsResponseComposite & {
  headers: Headers;
};

export const getGetCreditListsUrl = () => {
  return `/account/credit/getCreditLists`;
};

export const getCreditLists = async (
  getCreditListsArgVO: GetCreditListsArgVO,
  options?: RequestInit,
): Promise<getCreditListsResponse> => {
  return customInstance<getCreditListsResponse>(getGetCreditListsUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(getCreditListsArgVO),
  });
};

/**
 * @summary 生成报告确认卡片
 */
export type generateReportConfirmCardResponse200 = {
  data: GateWayResponseGenerateReportConfirmCardResultVO;
  status: 200;
};

export type generateReportConfirmCardResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type generateReportConfirmCardResponseComposite =
  | generateReportConfirmCardResponse200
  | generateReportConfirmCardResponse500;

export type generateReportConfirmCardResponse =
  generateReportConfirmCardResponseComposite & {
    headers: Headers;
  };

export const getGenerateReportConfirmCardUrl = () => {
  return `/account/credit/generateReportConfirmCard`;
};

export const generateReportConfirmCard = async (
  options?: RequestInit,
): Promise<generateReportConfirmCardResponse> => {
  return customInstance<generateReportConfirmCardResponse>(
    getGenerateReportConfirmCardUrl(),
    {
      ...options,
      method: "POST",
    },
  );
};

/**
 * @summary 创建积分充值订单
 */
export type createOrderResponse200 = {
  data: GateWayResponseCreateOrderResultVO;
  status: 200;
};

export type createOrderResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type createOrderResponseComposite =
  | createOrderResponse200
  | createOrderResponse500;

export type createOrderResponse = createOrderResponseComposite & {
  headers: Headers;
};

export const getCreateOrderUrl = () => {
  return `/account/credit/createOrder`;
};

export const createOrder = async (
  createOrderArgVO: CreateOrderArgVO,
  options?: RequestInit,
): Promise<createOrderResponse> => {
  return customInstance<createOrderResponse>(getCreateOrderUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(createOrderArgVO),
  });
};

/**
 * @summary 微信支付回调
 */
export type wechatCallbackResponse200 = {
  data: GateWayResponseWechatCallbackResultVO;
  status: 200;
};

export type wechatCallbackResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type wechatCallbackResponseComposite =
  | wechatCallbackResponse200
  | wechatCallbackResponse500;

export type wechatCallbackResponse = wechatCallbackResponseComposite & {
  headers: Headers;
};

export const getWechatCallbackUrl = () => {
  return `/account/callback/wechatCallback`;
};

export const wechatCallback = async (
  wechatCallbackBody: string,
  options?: RequestInit,
): Promise<wechatCallbackResponse> => {
  return customInstance<wechatCallbackResponse>(getWechatCallbackUrl(), {
    ...options,
    method: "POST",
    headers: { "Content-Type": "application/json", ...options?.headers },
    body: JSON.stringify(wechatCallbackBody),
  });
};

/**
 * 分页查询当前用户的邀请记录
 * @summary 查询邀请记录
 */
export type queryInviteRecordsResponse200 = {
  data: GateWayResponseQueryInviteRecordSummaryResultVO;
  status: 200;
};

export type queryInviteRecordsResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type queryInviteRecordsResponseComposite =
  | queryInviteRecordsResponse200
  | queryInviteRecordsResponse500;

export type queryInviteRecordsResponse = queryInviteRecordsResponseComposite & {
  headers: Headers;
};

export const getQueryInviteRecordsUrl = (params: QueryInviteRecordsParams) => {
  const normalizedParams = new URLSearchParams();

  Object.entries(params || {}).forEach(([key, value]) => {
    if (value !== undefined) {
      normalizedParams.append(key, value === null ? "null" : value.toString());
    }
  });

  const stringifiedParams = normalizedParams.toString();

  return stringifiedParams.length > 0
    ? `/account/invite/records?${stringifiedParams}`
    : `/account/invite/records`;
};

export const queryInviteRecords = async (
  params: QueryInviteRecordsParams,
  options?: RequestInit,
): Promise<queryInviteRecordsResponse> => {
  return customInstance<queryInviteRecordsResponse>(
    getQueryInviteRecordsUrl(params),
    {
      ...options,
      method: "GET",
    },
  );
};

/**
 * @summary 用户注销（逻辑删除）
 */
export type deleteUserResponse200 = {
  data: GateWayResponseEmptyData;
  status: 200;
};

export type deleteUserResponse500 = {
  data: GateWayResponseObject;
  status: 500;
};

export type deleteUserResponseComposite =
  | deleteUserResponse200
  | deleteUserResponse500;

export type deleteUserResponse = deleteUserResponseComposite & {
  headers: Headers;
};

export const getDeleteUserUrl = () => {
  return `/account/user/deleteUser`;
};

export const deleteUser = async (
  options?: RequestInit,
): Promise<deleteUserResponse> => {
  return customInstance<deleteUserResponse>(getDeleteUserUrl(), {
    ...options,
    method: "DELETE",
  });
};

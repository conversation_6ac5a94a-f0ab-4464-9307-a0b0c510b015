# Gateway 日志记录系统

## 概述
这是一个为 Gateway API 设计的统一日志记录系统，自动记录所有 API 请求的输入输出。

## 功能特性
- 🔍 自动记录所有 API 请求和响应
- 📊 记录请求耗时，便于性能分析
- 🆔 为每个请求分配唯一 ID，便于追踪
- 📈 支持不同的日志级别（ERROR、WARN、INFO、DEBUG）
- ⚙️ 通过环境变量控制日志级别

## 使用方法

### 基本使用
所有通过 `@src/lib/api-client/` 的 API 调用都会自动记录日志，无需任何额外配置。

```typescript
import { getUserInfo } from '@src/lib/api-client';

// 这个调用会自动记录请求和响应日志
const userInfo = await getUserInfo();
```

### 配置日志级别
在 `.env` 文件中设置日志级别：

```bash
# 设置日志级别为 DEBUG（显示所有日志）
LOG_LEVEL=debug

# 设置日志级别为 INFO（默认，显示请求/响应日志）
LOG_LEVEL=info

# 设置日志级别为 WARN（只显示警告和错误）
LOG_LEVEL=warn

# 设置日志级别为 ERROR（只显示错误）
LOG_LEVEL=error
```

## 日志格式

### 请求日志
```javascript
[Gateway INFO] abc123def456 {
  level: 'info',
  requestId: 'abc123def456',
  url: 'https://gateway-test.beiyi-tech.top/account/login/authWithMobile',
  method: 'POST',
  body: { mobile: '***********', code: '123456' },
  headers: { 'Content-Type': 'application/json' },
  timestamp: '2023-12-06T10:30:00.000Z'
}
```

### 响应日志
```javascript
[Gateway INFO] abc123def456 {
  level: 'info',
  requestId: 'abc123def456',
  status: 200,
  statusText: 'OK',
  data: { code: '0000', message: 'success', data: {...} },
  duration: '245ms',
  timestamp: '2023-12-06T10:30:00.245Z'
}
```

### 错误日志
```javascript
[Gateway ERROR] abc123def456 {
  level: 'error',
  requestId: 'abc123def456',
  error: '网络请求失败',
  duration: '5000ms',
  timestamp: '2023-12-06T10:30:05.000Z'
}
```

## 日志级别说明

| 级别 | 显示内容 |
|------|----------|
| DEBUG | 所有日志（包括调试信息） |
| INFO | 请求/响应日志（默认） |
| WARN | 警告和错误日志 |
| ERROR | 仅错误日志 |

## 生产环境建议
- 开发环境：使用 `INFO` 或 `DEBUG` 级别
- 测试环境：使用 `INFO` 级别
- 生产环境：使用 `WARN` 或 `ERROR` 级别

## 注意事项
- 日志中可能包含敏感信息，请在生产环境中谨慎处理
- 建议在生产环境中使用结构化日志收集工具（如 ELK Stack）
- 大量日志可能影响性能，建议根据需要调整日志级别
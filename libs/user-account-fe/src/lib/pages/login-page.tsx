'use client';

import { LoginForm } from '../components/login-form';

interface LoginPageProps {
  onLogin?: (phone: string, code: string) => Promise<{ success: boolean; error?: string }>;
  onLoginSuccess?: () => void;
  homeLink?: React.ReactNode;
  registerLink?: React.ReactNode;
  variant?: 'dark' | 'light';
  title?: string;
  subtitle?: string;
}

export function LoginPage({ 
  onLogin, 
  onLoginSuccess, 
  homeLink, 
  registerLink, 
  variant = 'light',
  title = 'AI 投资洞察',
  subtitle = '登录您的账户'
}: LoginPageProps) {
  return (
    <div className="h-screen w-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-50 via-cyan-50 via-indigo-100 to-purple-50">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        {/* 主要装饰圆形 */}
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full blur-[48px] opacity-30 animate-pulse bg-gradient-to-br from-blue-300 to-cyan-300"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full blur-[48px] opacity-30 animate-pulse bg-gradient-to-br from-purple-400 to-pink-300" style={{animationDelay: '1s'}}></div>
        
        {/* 次要装饰元素 */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 rounded-full blur-[32px] opacity-20 bg-indigo-300"></div>
        <div className="absolute bottom-1/4 right-1/4 w-24 h-24 rounded-full blur-[32px] opacity-20 bg-emerald-300"></div>
        
        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      {/* 主要内容容器 */}
      <div className="w-[480px] min-h-[680px] flex flex-col justify-center relative z-10 p-8">
        {/* 标题区域 */}
        <div className="text-center mb-10">
          {homeLink ? (
            <div className="mb-6">
              {homeLink}
            </div>
          ) : (
            <div className="mb-6">
              {/* 品牌图标 */}
              <div className="relative mb-8">
                <div className="inline-flex items-center justify-center w-24 h-24 rounded-full mb-2 relative overflow-hidden bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 shadow-[0_25px_50px_-12px_rgba(59,130,246,0.3)]">
                  {/* 内部光晕效果 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-full"></div>
                  <svg className="w-12 h-12 text-white relative z-10 drop-shadow-[0_4px_8px_rgba(0,0,0,0.1)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                {/* 图标下方光晕 */}
                <div className="absolute top-0 left-1/2 -translate-x-1/2 w-24 h-24 rounded-full blur-[48px] opacity-40 bg-blue-300"></div>
              </div>
              
              <h1 className="text-5xl font-bold mb-3 bg-gradient-to-r from-gray-800 via-blue-500 to-purple-500 bg-clip-text text-transparent m-0 leading-tight">
                {title}
              </h1>
            </div>
          )}
          
          <h2 className="text-2xl font-semibold mb-4 text-gray-700 m-0">
            {subtitle}
          </h2>
          
          <p className="text-base text-gray-500 m-0 leading-relaxed">
            欢迎回来！请输入您的手机号和验证码。
          </p>
        </div>

        {/* 登录卡片 */}
        <div className="relative">
          {/* 卡片背景光晕 */}
          <div className="absolute inset-0 rounded-3xl blur-[32px] opacity-20 bg-gradient-to-br from-purple-500/30 to-blue-500/30"></div>
          
          <div className="relative rounded-3xl p-10 backdrop-blur-[20px] border-2 border-white/40 shadow-[0_25px_50px_-12px_rgba(0,0,0,0.25),0_0_0_1px_rgba(255,255,255,0.8)] transition-all duration-300 bg-white/90">
            {/* 内部装饰边框 */}
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/20 via-transparent to-white/20"></div>
             
            <div className="relative z-10">
              <LoginForm
                onLogin={onLogin}
                onLoginSuccess={onLoginSuccess}
                variant="light"
                registerLink={registerLink}
              />
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}

export default LoginPage;
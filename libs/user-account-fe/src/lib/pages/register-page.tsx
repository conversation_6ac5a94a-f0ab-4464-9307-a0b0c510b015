'use client';

import { RegisterForm } from '../components/register-form';
import Link from 'next/link';

export function RegisterPage() {
  return (
    <div className="relative flex min-h-screen flex-col items-center justify-center overflow-hidden bg-[#0a0a14]">
      {/* Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0a0a14] via-[#0a0a14] to-[#1a1a3a]"></div>
      
      {/* Grid Overlay */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>

      {/* Centered Card */}
      <div className="relative z-10 w-full max-w-md px-4">
        <div className="rounded-2xl border border-white/10 bg-slate-900/80 p-8 text-white shadow-2xl backdrop-blur-lg">
          
          {/* Header */}
          <div className="text-center">
            <Link
              href="/"
              className="mb-2 inline-block text-3xl font-bold text-white transition-opacity hover:opacity-80"
            >
              AI 投资洞察
            </Link>
            <h1 className="text-2xl font-semibold tracking-tight">
              创建您的新账户
            </h1>
            <p className="mt-2 text-sm text-gray-400">
              加入我们，开启智能投资之旅。
            </p>
          </div>

          {/* Register Form Component */}
          <RegisterForm />

        </div>
      </div>
    </div>
  );
}

export default RegisterPage;
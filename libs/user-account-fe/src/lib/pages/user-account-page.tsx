'use client';

import React, { useState } from 'react';
import { UserAccountFeature } from '../user-account-fe';

export interface UserAccountPageProps {
  initialMode?: 'profile' | 'login' | 'settings';
  userId?: string;
  className?: string;
}

export function UserAccountPage({ 
  initialMode = 'profile', 
  userId, 
  className = '' 
}: UserAccountPageProps) {
  const [currentMode, setCurrentMode] = useState<'profile' | 'login' | 'settings'>(initialMode);
  const [currentUserId, setCurrentUserId] = useState<string | undefined>(userId);

  const handleLogin = (userData: any) => {
    // 这是一个示例页面，实际登录逻辑应该在应用层实现
    console.log('Login success with userData:', userData);
    
    // 模拟登录成功，从userData中获取用户ID或使用默认值
    const userId = userData?.userId || `user_${Date.now()}`;
    setCurrentUserId(userId);
    setCurrentMode('profile');
  };

  const handleLogout = () => {
    setCurrentUserId(undefined);
    setCurrentMode('login');
  };

  const handleModeSwitch = (mode: 'profile' | 'login' | 'settings') => {
    setCurrentMode(mode);
  };

  return (
    <div className={`min-h-screen w-full flex flex-col relative overflow-hidden bg-gradient-to-br from-slate-50 via-cyan-50 via-indigo-50 to-purple-50 p-0 ${className}`}>
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden z-0">
        {/* 主要装饰圆形 */}
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full blur-[48px] opacity-30 animate-pulse bg-gradient-to-br from-blue-300 to-cyan-300"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full blur-[48px] opacity-30 animate-pulse bg-gradient-to-br from-purple-400 to-pink-300" style={{animationDelay: '1s'}}></div>
        
        {/* 次要装饰元素 */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 rounded-full blur-[32px] opacity-20 animate-pulse bg-gradient-to-br from-amber-400 to-orange-500" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-3/5 right-[15%] w-24 h-24 rounded-full blur-[24px] opacity-25 animate-pulse bg-gradient-to-br from-emerald-500 to-emerald-600" style={{animationDelay: '3s'}}></div>
        
        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-50" style={{
          backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)',
          backgroundSize: '20px 20px'
        }}></div>
      </div>

      {/* 头部区域 */}
      <header className="relative z-10 text-center pt-15 pb-10 px-6 bg-white/10 backdrop-blur-[20px] border-b border-white/20 shadow-[0_8px_32px_rgba(0,0,0,0.1)]">
        {/* 品牌图标 */}
        <div className="relative mb-6">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full relative overflow-hidden bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 shadow-[0_25px_50px_-12px_rgba(59,130,246,0.3)]">
            {/* 内部光晕效果 */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-full"></div>
            <svg className="w-10 h-10 text-white relative z-10 drop-shadow-[0_4px_8px_rgba(0,0,0,0.1)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          {/* 图标下方光晕 */}
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-20 h-20 rounded-full blur-[48px] opacity-40 bg-blue-300"></div>
        </div>
        
        <h1 className="text-4xl font-bold mb-3 bg-gradient-to-r from-gray-800 via-blue-500 to-purple-500 bg-clip-text text-transparent m-0 leading-tight">
          用户中心
        </h1>
        
        <p className="text-base text-gray-500 mt-2 mb-0 leading-relaxed">
          管理您的个人信息和账户设置
        </p>
      </header>

      {/* 模式切换导航 */}
      {currentUserId && (
        <nav className="py-4 px-6 relative z-10 flex justify-center bg-white/70 backdrop-blur-[8px] border-b border-white/50 shadow-[0_4px_12px_rgba(0,0,0,0.03)]">
          <div className="flex p-2 rounded-2xl backdrop-blur-[20px] border border-white/30 bg-white/70 shadow-[0_8px_32px_rgba(0,0,0,0.1)]">
            <button
              onClick={() => handleModeSwitch('profile')}
              className={`py-3 px-6 rounded-xl border-none text-sm font-medium cursor-pointer transition-all duration-200 outline-none ${
                currentMode === 'profile' 
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-[0_4px_12px_rgba(59,130,246,0.3)]' 
                  : 'bg-transparent text-gray-500 hover:bg-white/50'
              }`}
            >
              个人资料
            </button>
            <button
              onClick={() => handleModeSwitch('settings')}
              className={`py-3 px-6 rounded-xl border-none text-sm font-medium cursor-pointer transition-all duration-200 outline-none ${
                currentMode === 'settings' 
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-[0_4px_12px_rgba(59,130,246,0.3)]' 
                  : 'bg-transparent text-gray-500 hover:bg-white/50'
              }`}
            >
              账户设置
            </button>
          </div>
        </nav>
      )}

      {/* 主内容区域 */}
      <main className="flex-1 py-8 px-6 relative z-10 flex justify-center items-start">
        {/* 用户账户功能卡片 */}
        <div className="w-full max-w-[600px] relative">
          {/* 内容装饰 */}
          <div className="absolute -top-[50px] -right-[50px] w-[100px] h-[100px] rounded-full bg-gradient-to-br from-blue-500/10 to-purple-500/10 blur-[20px]"></div>
          
          <div className="w-full max-w-[600px] bg-white/80 backdrop-blur-[20px] rounded-3xl border border-white/30 shadow-[0_20px_40px_rgba(0,0,0,0.1)] p-8 relative overflow-hidden">
            {/* 内部装饰边框 */}
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/20 via-transparent to-white/20"></div>
             
            <div className="relative z-10">
              <UserAccountFeature
                mode={currentMode}
                userId={currentUserId}
                onLoginSuccess={handleLogin}
                onLogout={handleLogout}
              />
            </div>
          </div>
        </div>
      </main>


    </div>
  );
}
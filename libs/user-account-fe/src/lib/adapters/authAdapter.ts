/**
 * 认证适配器接口 - 用于集成不同的认证方式
 */

export interface SessionResult {
  isLoggedIn: boolean;
  userId?: string;
  phone?: string;
  userName?: string;
  token?: string;
  avatar?: string;
  joinDate?: string;
}

export interface AuthResult {
  success: boolean;
  error?: string;
}

/**
 * 认证适配器接口
 */
export interface AuthAdapter {
  /**
   * 用户登录
   */
  loginWithCode: (phone: string, code: string) => Promise<AuthResult>;
  
  /**
   * 获取当前用户会话
   */
  getCurrentUser: () => Promise<SessionResult>;
  
  /**
   * 用户登出
   */
  logout: () => Promise<AuthResult>;
  
  /**
   * 检查是否已认证
   */
  isAuthenticated: () => Promise<boolean>;
}

/**
 * 创建 localStorage 认证适配器
 */
export function createLocalStorageAuthAdapter(): AuthAdapter {
  const isAuthenticated = (): boolean => {
    if (typeof window === 'undefined') return false;
    return !!localStorage.getItem('access_token');
  };

  const getAuthToken = (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('access_token');
  };

  const saveAuthToken = (token: string): void => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token);
    }
  };

  const clearAuthTokens = (): void => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  };

  return {
    async loginWithCode(phone: string, code: string): Promise<AuthResult> {
      // 这里应该调用实际的登录 API
      // 由于这是客户端适配器，实际的 API 调用应该在 userAccountActions 中处理
      throw new Error('localStorage adapter should use userAccountActions.loginWithMobile');
    },

    async getCurrentUser(): Promise<SessionResult> {
      const authStatus = isAuthenticated();
      const token = getAuthToken();
      
      if (authStatus && token) {
        return {
          isLoggedIn: true,
          token
        };
      }
      
      return { isLoggedIn: false };
    },

    async logout(): Promise<AuthResult> {
      try {
        clearAuthTokens();
        return { success: true };
      } catch (error) {
        return { success: false, error: '退出登录失败' };
      }
    },

    async isAuthenticated(): Promise<boolean> {
      return isAuthenticated();
    }
  };
}

/**
 * 创建 cookies 认证适配器（需要传入 Server Actions）
 */
export function createCookiesAuthAdapter(serverActions: {
  loginWithCode: (phone: string, code: string) => Promise<AuthResult>;
  getCurrentUser: () => Promise<SessionResult>;
  logout: () => Promise<AuthResult>;
  isAuthenticated: () => Promise<boolean>;
}): AuthAdapter {
  return {
    loginWithCode: serverActions.loginWithCode,
    getCurrentUser: serverActions.getCurrentUser,
    logout: serverActions.logout,
    isAuthenticated: serverActions.isAuthenticated
  };
}
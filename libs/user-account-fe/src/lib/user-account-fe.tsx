'use client';

import React from 'react';
import { loginWithMobile } from './actions/userAccountActions';
import { UserProfile } from './components/user-profile';
import { LoginForm } from './components/login-form';
import { AccountSettings } from './components/account-settings';
import './user-account-fe.module.css';

export interface UserAccountFeatureProps {
  mode?: 'profile' | 'login' | 'settings';
  userId?: string;
  onLoginSuccess?: (userData: any) => void;
  onLogout?: () => void;
  className?: string;
}

export function UserAccountFeature({ 
  mode = 'profile', 
  userId,
  onLoginSuccess,
  onLogout,
  className = ''
}: UserAccountFeatureProps) {
  const renderContent = () => {
    switch (mode) {
      case 'login':
        const handleLogin = async (phone: string, code: string) => {
          try {
            const result = await loginWithMobile(phone, code);
            if (result.token) {
              return { success: true };
            } else {
              return { success: false, error: 'Login failed' };
            }
          } catch (error: any) {
            return { success: false, error: error.message || 'Login failed' };
          }
        };
        return <LoginForm onLogin={handleLogin} onLoginSuccess={() => onLoginSuccess && onLoginSuccess(null)} />;
      case 'settings':
        return <AccountSettings userId={userId} />;
      case 'profile':
      default:
        return <UserProfile onLogout={onLogout} />;
    }
  };

  return (
    <div className={`user-account-feature ${className}`}>
      {renderContent()}
    </div>
  );
}
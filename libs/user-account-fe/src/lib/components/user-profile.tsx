'use client';

import React, { useEffect, useState } from 'react';
import { getUserProfile, UserData } from '../actions/userAccountActions';

export interface UserProfileProps {
  variant?: 'default' | 'simple' | 'detailed';
  className?: string;
  onLogout?: () => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({
  variant = 'default',
  className = '',
  onLogout,
}) => {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const profile = await getUserProfile();
        setUserData(profile);
      } catch (err) {
        setError('Error fetching user data');
        console.error('Error fetching user data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (error || !userData?.user_info_vo) {
    return (
      <div className={`text-red-500 ${className}`}>
        <p>{error || 'No user data available'}</p>
      </div>
    );
  }

  const userInfo = userData.user_info_vo;
  const userCredit = userData.user_credit;

  const renderSimpleVariant = () => (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
        {userInfo.username?.charAt(0).toUpperCase() || 'U'}
      </div>
      <div>
        <p className="text-sm font-medium text-gray-900">{userInfo.username || 'Unknown User'}</p>
        <p className="text-xs text-gray-500">{userInfo.email || userInfo.mobile}</p>
      </div>
    </div>
  );

  const renderDetailedVariant = () => (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <div className="flex items-center space-x-4 mb-6">
        <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-lg font-medium">
          {userInfo.username?.charAt(0).toUpperCase() || 'U'}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{userInfo.username || 'Unknown User'}</h3>
          <p className="text-sm text-gray-500">用户档案</p>
        </div>
      </div>
      
      {/* 基本信息 */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-800 mb-3 border-b pb-1">基本信息</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {userInfo.mobile && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 w-20">手机号:</span>
              <span className="text-sm text-gray-600">{userInfo.mobile}</span>
            </div>
          )}
          {userInfo.email && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 w-20">邮箱:</span>
              <span className="text-sm text-gray-600">{userInfo.email}</span>
            </div>
          )}
          {userInfo.school && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 w-20">学校:</span>
              <span className="text-sm text-gray-600">{userInfo.school}</span>
            </div>
          )}
          {userInfo.gender_key && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 w-20">性别:</span>
              <span className="text-sm text-gray-600">{userInfo.gender_key}</span>
            </div>
          )}
        </div>
      </div>

      {/* 考试信息 */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-800 mb-3 border-b pb-1">考试信息</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {userInfo.exam_type && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 w-20">考试科类:</span>
              <span className="text-sm text-gray-600">{userInfo.exam_type}</span>
            </div>
          )}
          {userInfo.exam_no && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 w-20">考号:</span>
              <span className="text-sm text-gray-600">{userInfo.exam_no}</span>
            </div>
          )}
          {userInfo.province_key && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 w-20">省份:</span>
              <span className="text-sm text-gray-600">{userInfo.province_key}</span>
            </div>
          )}
          {userInfo.exam_subjects && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 w-20">考试科目:</span>
              <span className="text-sm text-gray-600">{userInfo.exam_subjects}</span>
            </div>
          )}
          {userInfo.sort && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 w-20">排名:</span>
              <span className="text-sm text-gray-600">{userInfo.sort}</span>
            </div>
          )}
        </div>
      </div>

      {/* 成绩信息 */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-800 mb-3 border-b pb-1">成绩信息</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          <div className="bg-blue-50 p-3 rounded-lg">
            <span className="text-sm font-medium text-blue-700">总分</span>
            <p className="text-lg font-bold text-blue-900">{userInfo.total_score}</p>
          </div>
          <div className="bg-green-50 p-3 rounded-lg">
            <span className="text-sm font-medium text-green-700">语文</span>
            <p className="text-lg font-bold text-green-900">{userInfo.chinese_score}</p>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg">
            <span className="text-sm font-medium text-purple-700">数学</span>
            <p className="text-lg font-bold text-purple-900">{userInfo.math_score}</p>
          </div>
          <div className="bg-orange-50 p-3 rounded-lg">
            <span className="text-sm font-medium text-orange-700">英语</span>
            <p className="text-lg font-bold text-orange-900">{userInfo.english_score}</p>
          </div>
          {userInfo.physics_score !== undefined && (
            <div className="bg-red-50 p-3 rounded-lg">
              <span className="text-sm font-medium text-red-700">物理</span>
              <p className="text-lg font-bold text-red-900">{userInfo.physics_score}</p>
            </div>
          )}
          {userInfo.chemistry_score !== undefined && (
            <div className="bg-yellow-50 p-3 rounded-lg">
              <span className="text-sm font-medium text-yellow-700">化学</span>
              <p className="text-lg font-bold text-yellow-900">{userInfo.chemistry_score}</p>
            </div>
          )}
          {userInfo.biology_score !== undefined && (
            <div className="bg-teal-50 p-3 rounded-lg">
              <span className="text-sm font-medium text-teal-700">生物</span>
              <p className="text-lg font-bold text-teal-900">{userInfo.biology_score}</p>
            </div>
          )}
          {userInfo.politics_score !== undefined && (
            <div className="bg-indigo-50 p-3 rounded-lg">
              <span className="text-sm font-medium text-indigo-700">政治</span>
              <p className="text-lg font-bold text-indigo-900">{userInfo.politics_score}</p>
            </div>
          )}
          {userInfo.history_score !== undefined && (
            <div className="bg-pink-50 p-3 rounded-lg">
              <span className="text-sm font-medium text-pink-700">历史</span>
              <p className="text-lg font-bold text-pink-900">{userInfo.history_score}</p>
            </div>
          )}
          {userInfo.geography_score !== undefined && (
            <div className="bg-cyan-50 p-3 rounded-lg">
              <span className="text-sm font-medium text-cyan-700">地理</span>
              <p className="text-lg font-bold text-cyan-900">{userInfo.geography_score}</p>
            </div>
          )}
          {userInfo.other_score !== undefined && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <span className="text-sm font-medium text-gray-700">其他</span>
              <p className="text-lg font-bold text-gray-900">{userInfo.other_score}</p>
            </div>
          )}
        </div>
      </div>

      {/* 积分信息 */}
      {userCredit && (
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-800 mb-3 border-b pb-1">积分信息</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {userCredit.credit !== undefined && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700 w-20">当前积分:</span>
                <span className="text-sm text-gray-600">{userCredit.credit}</span>
              </div>
            )}
            {userCredit.expired_at && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700 w-20">过期时间:</span>
                <span className="text-sm text-gray-600">{new Date(userCredit.expired_at).toLocaleDateString()}</span>
              </div>
            )}
          </div>
        </div>
      )}
      
      {onLogout && (
        <div className="mt-4 pt-4 border-t">
          <button
            onClick={onLogout}
            className="text-sm text-red-600 hover:text-red-800 transition-colors"
          >
            退出登录
          </button>
        </div>
      )}
    </div>
  );

  const renderDefaultVariant = () => (
    <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
            {userInfo.username?.charAt(0).toUpperCase() || 'U'}
          </div>
          <div>
            <p className="font-medium text-gray-900">{userInfo.username || 'Unknown User'}</p>
            <p className="text-sm text-gray-500">{userInfo.email || userInfo.mobile}</p>
            {userInfo.school && (
              <p className="text-xs text-gray-400">{userInfo.school}</p>
            )}
          </div>
        </div>
        <div className="text-right">
          {userInfo.total_score && (
            <p className="text-sm font-medium text-blue-600">总分: {userInfo.total_score}</p>
          )}
          {userCredit?.credit !== undefined && (
            <p className="text-xs text-gray-500">积分: {userCredit.credit}</p>
          )}
        </div>
        {onLogout && (
          <button
            onClick={onLogout}
            className="ml-4 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            退出
          </button>
        )}
      </div>
    </div>
  );

  if (variant === 'simple') {
    return renderSimpleVariant();
  }

  if (variant === 'detailed') {
    return renderDetailedVariant();
  }

  return renderDefaultVariant();
};

/**
 * 简化版用户信息组件 - 仅显示用户名和登出按钮
 * @deprecated 使用 UserProfile 组件的 variant="simple" 属性替代
 */
export function UserProfileSimple(props: Omit<UserProfileProps, 'variant'>) {
  return <UserProfile {...props} variant="simple" />;
}
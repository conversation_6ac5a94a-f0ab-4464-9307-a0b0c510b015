"use client";

import React, { useState } from 'react';
import { sendOtpCode, loginWithMobile } from '../actions/userAccountActions';

export interface CoagentLoginExampleProps {
  onLoginSuccess?: (userData: any) => void;
  onLoginError?: (error: string) => void;
}

/**
 * 登录示例组件
 * 演示如何直接使用 Server Actions 进行发送验证码和登录操作
 */
export function CoagentLoginExample({
  onLoginSuccess,
  onLoginError
}: CoagentLoginExampleProps) {
  const [mobile, setMobile] = useState('');
  const [code, setCode] = useState('');
  const [step, setStep] = useState<'mobile' | 'code'>('mobile');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSendOtp = async () => {
    if (!mobile) {
      alert('请输入手机号');
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      await sendOtpCode(mobile); // 直接调用Server Action
      setStep('code');
      alert('验证码已发送');
    } catch (err: any) {
      const errorMsg = err.message || '发送验证码失败';
      setError(errorMsg);
      alert(errorMsg);
      onLoginError?.(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async () => {
    if (!code) {
      alert('请输入验证码');
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      const result = await loginWithMobile(mobile, code); // 直接调用Server Action
      alert('登录成功');
      onLoginSuccess?.(result);
    } catch (err: any) {
      const errorMsg = err.message || '登录失败';
      setError(errorMsg);
      alert(errorMsg);
      onLoginError?.(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    setStep('mobile');
    setCode('');
  };

  return (
    <div className="login-example">
      <h3>登录示例 (Server Actions)</h3>
      
      {error && (
        <div className="error-message" style={{ color: 'red', marginBottom: '10px' }}>
          错误: {error}
        </div>
      )}

      {step === 'mobile' ? (
        <div className="mobile-step">
          <div className="form-group">
            <label htmlFor="mobile">手机号:</label>
            <input
              id="mobile"
              type="tel"
              value={mobile}
              onChange={(e) => setMobile(e.target.value)}
              placeholder="请输入手机号"
              disabled={isLoading}
              style={{
                width: '100%',
                padding: '8px',
                margin: '5px 0',
                border: '1px solid #ccc',
                borderRadius: '4px'
              }}
            />
          </div>
          <button
            onClick={handleSendOtp}
            disabled={isLoading || !mobile}
            style={{
              width: '100%',
              padding: '10px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              opacity: isLoading ? 0.6 : 1
            }}
          >
            {isLoading ? '发送中...' : '发送验证码'}
          </button>
        </div>
      ) : (
        <div className="code-step">
          <div className="form-group">
            <label htmlFor="code">验证码:</label>
            <input
              id="code"
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              placeholder="请输入验证码"
              disabled={isLoading}
              style={{
                width: '100%',
                padding: '8px',
                margin: '5px 0',
                border: '1px solid #ccc',
                borderRadius: '4px'
              }}
            />
          </div>
          <div className="button-group" style={{ display: 'flex', gap: '10px' }}>
            <button
              onClick={handleBack}
              disabled={isLoading}
              style={{
                flex: 1,
                padding: '10px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                opacity: isLoading ? 0.6 : 1
              }}
            >
              返回
            </button>
            <button
              onClick={handleLogin}
              disabled={isLoading || !code}
              style={{
                flex: 2,
                padding: '10px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                opacity: isLoading ? 0.6 : 1
              }}
            >
              {isLoading ? '登录中...' : '登录'}
            </button>
          </div>
        </div>
      )}

      <div className="info" style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <p>📱 手机号: {mobile}</p>
        <p>🔄 当前步骤: {step === 'mobile' ? '输入手机号' : '输入验证码'}</p>
        <p>⏳ 加载状态: {isLoading ? '加载中' : '空闲'}</p>
      </div>
    </div>
  );
}

export default CoagentLoginExample;
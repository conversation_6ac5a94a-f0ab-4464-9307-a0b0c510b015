'use client';

import React, { useState } from 'react';

export interface SimpleLoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLogin?: (userData: any) => void;
}

export function SimpleLoginModal({
  isOpen,
  onClose,
  onLogin,
}: SimpleLoginModalProps) {
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);

  const handleSendCode = async () => {
    if (!phone.trim()) {
      alert('请输入手机号');
      return;
    }

    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!phone.trim() || !code.trim()) {
      alert('请填写完整信息');
      return;
    }

    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      if (onLogin) {
        onLogin({ phone, success: true });
      }
      onClose();
    }, 1000);
  };

  if (!isOpen) return null;

  return (
    <>
      {/* 背景遮罩 - 浅色半透明 */}
      <div 
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.4)',
          backdropFilter: 'blur(8px)',
          zIndex: 9999,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '20px'
        }}
        onClick={onClose}
      >
        {/* 弹窗内容 - 白色背景 */}
        <div 
          style={{
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
            borderRadius: '20px',
            padding: '32px',
            width: '100%',
            maxWidth: '420px',
            color: '#1f2937',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.8)',
            border: '1px solid rgba(139, 92, 246, 0.2)',
            position: 'relative'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 装饰性渐变边框效果 */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: '20px',
              background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.03) 0%, rgba(59, 130, 246, 0.03) 100%)',
              pointerEvents: 'none'
            }}
          />

          {/* 头部 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center', 
            marginBottom: '24px',
            position: 'relative',
            zIndex: 1
          }}>
            <h2 style={{ 
              fontSize: '28px', 
              fontWeight: 'bold', 
              margin: 0, 
              color: '#1f2937',
              background: 'linear-gradient(135deg, #1f2937 0%, #374151 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              登录
            </h2>
            <button 
              onClick={onClose}
              style={{
                background: 'rgba(0, 0, 0, 0.05)',
                border: 'none',
                color: '#6b7280',
                fontSize: '20px',
                cursor: 'pointer',
                padding: '8px',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '36px',
                height: '36px',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                (e.target as HTMLButtonElement).style.background = 'rgba(0, 0, 0, 0.1)';
                (e.target as HTMLButtonElement).style.color = '#374151';
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLButtonElement).style.background = 'rgba(0, 0, 0, 0.05)';
                (e.target as HTMLButtonElement).style.color = '#6b7280';
              }}
            >
              ×
            </button>
          </div>

          <p style={{ 
            color: '#6b7280', 
            marginBottom: '28px', 
            fontSize: '15px',
            position: 'relative',
            zIndex: 1
          }}>
            请输入您的手机号和验证码以继续。
          </p>

          {/* 表单 */}
          <form onSubmit={handleSubmit} style={{ position: 'relative', zIndex: 1 }}>
            {/* 手机号 */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '10px', 
                color: '#374151', 
                fontSize: '15px',
                fontWeight: '500'
              }}>
                手机号
              </label>
              <input
                type="tel"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                placeholder="请输入手机号"
                required
                style={{
                  width: '100%',
                  padding: '14px 18px',
                  background: '#ffffff',
                  border: '1px solid #d1d5db',
                  borderRadius: '12px',
                  color: '#1f2937',
                  fontSize: '15px',
                  outline: 'none',
                  boxSizing: 'border-box',
                  transition: 'all 0.2s ease'
                }}
                onFocus={(e) => {
                  (e.target as HTMLInputElement).style.border = '1px solid #8b5cf6';
                  (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(139, 92, 246, 0.1)';
                }}
                onBlur={(e) => {
                  (e.target as HTMLInputElement).style.border = '1px solid #d1d5db';
                  (e.target as HTMLInputElement).style.boxShadow = 'none';
                }}
              />
            </div>

            {/* 验证码 */}
            <div style={{ marginBottom: '28px' }}>
              <label style={{ 
                display: 'block', 
                marginBottom: '10px', 
                color: '#374151', 
                fontSize: '15px',
                fontWeight: '500'
              }}>
                验证码
              </label>
              <div style={{ display: 'flex', gap: '12px' }}>
                <input
                  type="text"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder="请输入验证码"
                  required
                  style={{
                    flex: 1,
                    padding: '14px 18px',
                    background: '#ffffff',
                    border: '1px solid #d1d5db',
                    borderRadius: '12px',
                    color: '#1f2937',
                    fontSize: '15px',
                    outline: 'none',
                    boxSizing: 'border-box',
                    transition: 'all 0.2s ease'
                  }}
                  onFocus={(e) => {
                    (e.target as HTMLInputElement).style.border = '1px solid #8b5cf6';
                    (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(139, 92, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    (e.target as HTMLInputElement).style.border = '1px solid #d1d5db';
                    (e.target as HTMLInputElement).style.boxShadow = 'none';
                  }}
                />
                <button
                  type="button"
                  onClick={handleSendCode}
                  disabled={countdown > 0}
                  style={{
                    padding: '14px 20px',
                    background: countdown > 0 
                      ? '#f3f4f6' 
                      : 'linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%)',
                    border: countdown > 0 ? '1px solid #d1d5db' : 'none',
                    borderRadius: '12px',
                    color: countdown > 0 ? '#9ca3af' : 'white',
                    fontSize: '14px',
                    cursor: countdown > 0 ? 'not-allowed' : 'pointer',
                    opacity: countdown > 0 ? 0.6 : 1,
                    whiteSpace: 'nowrap',
                    transition: 'all 0.2s ease',
                    fontWeight: '500'
                  }}
                  onMouseEnter={(e) => {
                    if (countdown === 0) {
                      (e.target as HTMLButtonElement).style.background = 'linear-gradient(135deg, #7c3aed 0%, #2563eb 100%)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (countdown === 0) {
                      (e.target as HTMLButtonElement).style.background = 'linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%)';
                    }
                  }}
                >
                  {countdown > 0 ? `${countdown}s` : '发送验证码'}
                </button>
              </div>
            </div>

            {/* 按钮组 */}
            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                type="button"
                onClick={onClose}
                style={{
                  flex: 1,
                  padding: '14px',
                  background: '#f9fafb',
                  border: '1px solid #d1d5db',
                  borderRadius: '12px',
                  color: '#6b7280',
                  fontSize: '15px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  fontWeight: '500'
                }}
                onMouseEnter={(e) => {
                  (e.target as HTMLButtonElement).style.background = '#f3f4f6';
                  (e.target as HTMLButtonElement).style.color = '#374151';
                }}
                onMouseLeave={(e) => {
                  (e.target as HTMLButtonElement).style.background = '#f9fafb';
                  (e.target as HTMLButtonElement).style.color = '#6b7280';
                }}
              >
                取消
              </button>
              <button
                type="submit"
                disabled={loading}
                style={{
                  flex: 1,
                  padding: '14px',
                  background: loading 
                    ? '#d1d5db' 
                    : 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  color: 'white',
                  fontSize: '15px',
                  cursor: loading ? 'not-allowed' : 'pointer',
                  opacity: loading ? 0.7 : 1,
                  transition: 'all 0.2s ease',
                  fontWeight: '600',
                  boxShadow: loading ? 'none' : '0 4px 15px rgba(59, 130, 246, 0.3)'
                }}
                onMouseEnter={(e) => {
                  if (!loading) {
                    (e.target as HTMLButtonElement).style.background = 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)';
                    (e.target as HTMLButtonElement).style.transform = 'translateY(-1px)';
                    (e.target as HTMLButtonElement).style.boxShadow = '0 6px 20px rgba(59, 130, 246, 0.4)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading) {
                    (e.target as HTMLButtonElement).style.background = 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)';
                    (e.target as HTMLButtonElement).style.transform = 'translateY(0)';
                    (e.target as HTMLButtonElement).style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';
                  }
                }}
              >
                {loading ? '登录中...' : '登录'}
              </button>
            </div>
          </form>

          <div style={{ 
            textAlign: 'center', 
            marginTop: '24px', 
            fontSize: '13px', 
            color: '#9ca3af',
            position: 'relative',
            zIndex: 1
          }}>
            请输入真实手机号获取验证码进行登录
          </div>
        </div>
      </div>
    </>
  );
}

export default SimpleLoginModal;
'use client';

import React, { useState, useEffect } from 'react';
import { getUserProfile, updateUserProfile, changePassword } from '../actions/userAccountActions';

export interface AccountSettingsProps {
  userId?: string;
  className?: string;
}

interface ProfileData {
  name: string;
  email: string;
  avatar?: string;
}

interface PasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export function AccountSettings({ userId, className = '' }: AccountSettingsProps) {
  const [profileData, setProfileData] = useState<ProfileData>({
    name: '',
    email: ''
  });
  const [passwordData, setPasswordData] = useState<PasswordData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!userId) return;

      try {
        setLoading(true);
        const userData = await getUserProfile();
        setProfileData({
          name: userData.user_info_vo?.username || '',
          email: userData.user_info_vo?.email || '',
          avatar: undefined // UserDetailInfo 接口中没有 avatar 字段
        });
      } catch (err) {
        setError('获取用户信息失败');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [userId]);

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
    clearMessages();
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
    clearMessages();
  };

  const clearMessages = () => {
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userId) return;

    try {
      setSaving(true);
      clearMessages();
      
      // await updateUserProfile(profileData); // 功能暂未实现
      setSuccess('个人信息更新成功');
    } catch (err: any) {
      setError(err.message || '更新个人信息失败');
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userId) return;

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('新密码两次输入不一致');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setError('新密码长度不能少于6位');
      return;
    }

    try {
      setSaving(true);
      clearMessages();
      
      // await changePassword({ // 功能暂未实现
      //   currentPassword: passwordData.currentPassword,
      //   newPassword: passwordData.newPassword
      // });
      
      setSuccess('密码修改成功');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (err: any) {
      setError(err.message || '修改密码失败');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className={`account-settings ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`account-settings ${className}`}>
      <h2 className="text-2xl font-bold text-gray-900 mb-6">账户设置</h2>
      
      {error && (
        <div className="error-message mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="success-message mb-4">
          {success}
        </div>
      )}

      {/* 个人信息设置 */}
      <div className="settings-section">
        <h3 className="settings-title">个人信息</h3>
        <form onSubmit={handleProfileSubmit} className="space-y-4">
          <div className="form-group">
            <label htmlFor="name" className="form-label">
              姓名
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={profileData.name}
              onChange={handleProfileChange}
              className="form-input"
              disabled={saving}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="email" className="form-label">
              邮箱地址
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={profileData.email}
              onChange={handleProfileChange}
              className="form-input"
              disabled={saving}
              required
            />
          </div>

          <button
            type="submit"
            className="form-button"
            disabled={saving}
          >
            {saving ? '保存中...' : '保存个人信息'}
          </button>
        </form>
      </div>

      {/* 密码修改 */}
      <div className="settings-section">
        <h3 className="settings-title">修改密码</h3>
        <form onSubmit={handlePasswordSubmit} className="space-y-4">
          <div className="form-group">
            <label htmlFor="currentPassword" className="form-label">
              当前密码
            </label>
            <input
              type="password"
              id="currentPassword"
              name="currentPassword"
              value={passwordData.currentPassword}
              onChange={handlePasswordChange}
              className="form-input"
              disabled={saving}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="newPassword" className="form-label">
              新密码
            </label>
            <input
              type="password"
              id="newPassword"
              name="newPassword"
              value={passwordData.newPassword}
              onChange={handlePasswordChange}
              className="form-input"
              placeholder="至少6位字符"
              disabled={saving}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="confirmPassword" className="form-label">
              确认新密码
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={passwordData.confirmPassword}
              onChange={handlePasswordChange}
              className="form-input"
              disabled={saving}
              required
            />
          </div>

          <button
            type="submit"
            className="form-button"
            disabled={saving}
          >
            {saving ? '修改中...' : '修改密码'}
          </button>
        </form>
      </div>
    </div>
  );
}
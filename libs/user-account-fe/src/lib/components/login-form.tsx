'use client';

import { useState } from 'react';
import { sendOtpCode, loginWithMobile } from '../actions/userAccountActions';

interface LoginFormProps {
  onLogin?: (phone: string, code: string) => Promise<{ success: boolean; error?: string }>;
  onLoginSuccess?: () => void;
  className?: string;
  variant?: 'dark' | 'light';
  registerLink?: React.ReactNode;
}

export function LoginForm({ onLogin, onLoginSuccess, className = '', variant = 'light', registerLink }: LoginFormProps) {
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSendCode = async () => {
    if (!phone.trim()) {
      setError('请输入手机号');
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      setError('请输入正确的手机号');
      return;
    }

    setError('');
    setCountdown(60);
    
    // 倒计时
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // 调用发送验证码API
    try {
      await sendOtpCode(phone);
      console.log('验证码发送成功:', phone);
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      setError(error.message || '发送验证码失败，请重试');
      setCountdown(0); // 重置倒计时
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!phone.trim() || !code.trim()) {
      setError('请填写完整信息');
      return;
    }

    setLoading(true);
    setError('');

    try {
      if (onLogin) {
        // 使用外部提供的登录函数
        const result = await onLogin(phone, code);
        
        if (result.success) {
          // 登录成功回调
          onLoginSuccess?.();
        } else {
          setError(result.error || '登录失败');
        }
      } else {
        // 使用内置的登录逻辑
        await loginWithMobile(phone, code);

        // 登录成功回调
        onLoginSuccess?.();
      }
    } catch (error: any) {
      setError(error.message || '登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ position: 'relative', zIndex: 1 }}>
      <form onSubmit={handleSubmit} style={{ position: 'relative', zIndex: 1 }}>
        {/* 错误提示 */}
        {error && (
          <div style={{
            marginBottom: '24px',
            padding: '16px 20px',
            background: 'rgba(254, 226, 226, 0.8)',
            backdropFilter: 'blur(8px)',
            borderRadius: '16px',
            border: '1px solid rgba(248, 113, 113, 0.3)',
            color: '#dc2626',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}>
            <svg style={{ width: '20px', height: '20px', flexShrink: 0 }} fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        )}

        {/* 手机号输入 */}
        <div style={{ marginBottom: '24px' }}>
          <label style={{ 
            display: 'block', 
            marginBottom: '10px', 
            color: '#374151', 
            fontSize: '15px',
            fontWeight: '500'
          }}>
            手机号
          </label>
          <input
            type="tel"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            placeholder="请输入手机号"
            required
            style={{
              width: '100%',
              padding: '16px 20px',
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(8px)',
              border: '1px solid rgba(209, 213, 219, 0.8)',
              borderRadius: '16px',
              color: '#1f2937',
              fontSize: '15px',
              outline: 'none',
              boxSizing: 'border-box',
              transition: 'all 0.2s ease',
              fontWeight: '400'
            }}
            onFocus={(e) => {
              (e.target as HTMLInputElement).style.border = '1px solid #8b5cf6';
              (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(139, 92, 246, 0.1)';
              (e.target as HTMLInputElement).style.background = 'rgba(255, 255, 255, 0.95)';
            }}
            onBlur={(e) => {
              (e.target as HTMLInputElement).style.border = '1px solid rgba(209, 213, 219, 0.8)';
              (e.target as HTMLInputElement).style.boxShadow = 'none';
              (e.target as HTMLInputElement).style.background = 'rgba(255, 255, 255, 0.9)';
            }}
          />
        </div>

        {/* 验证码输入 */}
        <div style={{ marginBottom: '32px' }}>
          <label style={{ 
            display: 'block', 
            marginBottom: '10px', 
            color: '#374151', 
            fontSize: '15px',
            fontWeight: '500'
          }}>
            验证码
          </label>
          <div style={{ display: 'flex', gap: '12px' }}>
            <input
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              placeholder="请输入验证码"
              required
              style={{
                flex: 1,
                padding: '16px 20px',
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(8px)',
                border: '1px solid rgba(209, 213, 219, 0.8)',
                borderRadius: '16px',
                color: '#1f2937',
                fontSize: '15px',
                outline: 'none',
                boxSizing: 'border-box',
                transition: 'all 0.2s ease',
                fontWeight: '400'
              }}
              onFocus={(e) => {
                (e.target as HTMLInputElement).style.border = '1px solid #8b5cf6';
                (e.target as HTMLInputElement).style.boxShadow = '0 0 0 3px rgba(139, 92, 246, 0.1)';
                (e.target as HTMLInputElement).style.background = 'rgba(255, 255, 255, 0.95)';
              }}
              onBlur={(e) => {
                (e.target as HTMLInputElement).style.border = '1px solid rgba(209, 213, 219, 0.8)';
                (e.target as HTMLInputElement).style.boxShadow = 'none';
                (e.target as HTMLInputElement).style.background = 'rgba(255, 255, 255, 0.9)';
              }}
            />
            <button
              type="button"
              onClick={handleSendCode}
              disabled={countdown > 0 || !phone.trim()}
              style={{
                padding: '16px 24px',
                background: countdown > 0 || !phone.trim()
                  ? 'rgba(243, 244, 246, 0.8)' 
                  : 'linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%)',
                backdropFilter: 'blur(8px)',
                border: countdown > 0 || !phone.trim() ? '1px solid rgba(209, 213, 219, 0.8)' : 'none',
                borderRadius: '16px',
                color: countdown > 0 || !phone.trim() ? '#9ca3af' : 'white',
                fontSize: '14px',
                cursor: countdown > 0 || !phone.trim() ? 'not-allowed' : 'pointer',
                opacity: countdown > 0 || !phone.trim() ? 0.6 : 1,
                whiteSpace: 'nowrap',
                transition: 'all 0.2s ease',
                fontWeight: '500',
                outline: 'none',
                boxShadow: countdown > 0 || !phone.trim() ? 'none' : '0 4px 15px rgba(139, 92, 246, 0.3)'
              }}
              onMouseEnter={(e) => {
                if (countdown === 0 && phone.trim()) {
                  (e.target as HTMLButtonElement).style.background = 'linear-gradient(135deg, #7c3aed 0%, #2563eb 100%)';
                  (e.target as HTMLButtonElement).style.transform = 'translateY(-1px)';
                  (e.target as HTMLButtonElement).style.boxShadow = '0 6px 20px rgba(139, 92, 246, 0.4)';
                }
              }}
              onMouseLeave={(e) => {
                if (countdown === 0 && phone.trim()) {
                  (e.target as HTMLButtonElement).style.background = 'linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%)';
                  (e.target as HTMLButtonElement).style.transform = 'translateY(0)';
                  (e.target as HTMLButtonElement).style.boxShadow = '0 4px 15px rgba(139, 92, 246, 0.3)';
                }
              }}
            >
              {countdown > 0 ? `${countdown}s` : '发送验证码'}
            </button>
          </div>
        </div>

        {/* 登录按钮 */}
        <button
          type="submit"
          disabled={loading || !phone.trim() || !code.trim()}
          style={{
            width: '100%',
            padding: '18px',
            background: loading || !phone.trim() || !code.trim()
              ? 'rgba(209, 213, 219, 0.8)' 
              : 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
            backdropFilter: 'blur(8px)',
            border: 'none',
            borderRadius: '16px',
            color: 'white',
            fontSize: '16px',
            cursor: loading || !phone.trim() || !code.trim() ? 'not-allowed' : 'pointer',
            opacity: loading || !phone.trim() || !code.trim() ? 0.7 : 1,
            transition: 'all 0.2s ease',
            fontWeight: '600',
            boxShadow: loading || !phone.trim() || !code.trim() ? 'none' : '0 6px 25px rgba(59, 130, 246, 0.4)',
            outline: 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px'
          }}
          onMouseEnter={(e) => {
            if (!loading && phone.trim() && code.trim()) {
              (e.target as HTMLButtonElement).style.background = 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)';
              (e.target as HTMLButtonElement).style.transform = 'translateY(-2px)';
              (e.target as HTMLButtonElement).style.boxShadow = '0 8px 30px rgba(59, 130, 246, 0.5)';
            }
          }}
          onMouseLeave={(e) => {
            if (!loading && phone.trim() && code.trim()) {
              (e.target as HTMLButtonElement).style.background = 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)';
              (e.target as HTMLButtonElement).style.transform = 'translateY(0)';
              (e.target as HTMLButtonElement).style.boxShadow = '0 6px 25px rgba(59, 130, 246, 0.4)';
            }
          }}
        >
          {loading && (
            <svg style={{ width: '20px', height: '20px', animation: 'spin 1s linear infinite' }} fill="none" viewBox="0 0 24 24">
              <circle style={{ opacity: 0.25 }} cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path style={{ opacity: 0.75 }} fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          )}
          {loading ? '登录中...' : '登录'}
        </button>

        {/* 注册链接 */}
        {registerLink && (
          <div style={{ 
            textAlign: 'center', 
            marginTop: '24px',
            position: 'relative',
            zIndex: 1
          }}>
            <p style={{ 
              fontSize: '14px', 
              color: '#6b7280',
              margin: 0
            }}>
              还没有账户？{' '}
              <span style={{ 
                fontWeight: '500',
                color: '#8b5cf6',
                cursor: 'pointer',
                textDecoration: 'none',
                transition: 'color 0.2s ease'
              }}>
                {registerLink}
              </span>
            </p>
          </div>
        )}
      </form>


    </div>
  );
}
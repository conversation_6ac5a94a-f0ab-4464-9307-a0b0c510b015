'use client';

export function RegisterForm() {
  return (
    <form className="mt-8 space-y-6">
      {/* Phone Number */}
      <div>
        <label
          htmlFor="phone"
          className="block text-sm font-medium leading-6 text-gray-300"
        >
          手机号
        </label>
        <div className="mt-2">
          <input
            id="phone"
            type="tel"
            autoComplete="tel"
            placeholder="请输入手机号"
            required
            className="block w-full rounded-md border-0 bg-white/5 py-2.5 px-3 text-white shadow-sm ring-1 ring-inset ring-white/10 transition focus:ring-2 focus:ring-inset focus:ring-blue-500 sm:text-sm sm:leading-6"
          />
        </div>
      </div>

      {/* Verification Code */}
      <div>
        <label
          htmlFor="code"
          className="block text-sm font-medium leading-6 text-gray-300"
        >
          验证码
        </label>
        <div className="mt-2 flex space-x-3">
          <input
            id="code"
            type="text"
            placeholder="请输入验证码"
            required
            className="block w-full rounded-md border-0 bg-white/5 py-2.5 px-3 text-white shadow-sm ring-1 ring-inset ring-white/10 transition focus:ring-2 focus:ring-inset focus:ring-blue-500 sm:text-sm sm:leading-6"
          />
          <button
            type="button"
            className="whitespace-nowrap rounded-md bg-white/10 px-4 py-2.5 text-sm font-semibold text-white shadow-sm transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
          >
            发送验证码
          </button>
        </div>
      </div>

      {/* Password */}
      <div>
        <label
          htmlFor="password"
          className="block text-sm font-medium leading-6 text-gray-300"
        >
          密码
        </label>
        <div className="mt-2">
          <input
            id="password"
            type="password"
            autoComplete="new-password"
            placeholder="请输入密码"
            required
            className="block w-full rounded-md border-0 bg-white/5 py-2.5 px-3 text-white shadow-sm ring-1 ring-inset ring-white/10 transition focus:ring-2 focus:ring-inset focus:ring-blue-500 sm:text-sm sm:leading-6"
          />
        </div>
      </div>

      {/* Confirm Password */}
      <div>
        <label
          htmlFor="confirm-password"
          className="block text-sm font-medium leading-6 text-gray-300"
        >
          确认密码
        </label>
        <div className="mt-2">
          <input
            id="confirm-password"
            type="password"
            autoComplete="new-password"
            placeholder="请再次输入密码"
            required
            className="block w-full rounded-md border-0 bg-white/5 py-2.5 px-3 text-white shadow-sm ring-1 ring-inset ring-white/10 transition focus:ring-2 focus:ring-inset focus:ring-blue-500 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      
      <button
        type="submit"
        className="w-full justify-center rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm transition hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
      >
        注册
      </button>

      <div className="mt-4 text-center text-sm">
        <p className="text-gray-400">
          已有账户？{' '}
          <a
            href="/login"
            className="font-medium text-blue-400 hover:underline"
          >
            立即登录
          </a>
        </p>
      </div>
    </form>
  );
} 
// 用户账户相关的类型定义

// 用户积分信息
export interface UserCreditInfo {
  credit?: number;
  expired_at?: string;
}

// 用户详细信息
export interface UserDetailInfo {
  /** ID */
  id?: number;
  /**
   * 名字
   * @minLength 0
   * @maxLength 25
   */
  username: string;
  /**
   * 手机号
   * @pattern ^1[3-9]\d{9}$
   */
  mobile: string;
  /** 邮箱 */
  email?: string;
  /**
   * 学校
   * @minLength 0
   * @maxLength 60
   */
  school?: string;
  /** 考试科类 */
  exam_type?: string;
  /**
   * 排名
   * @minimum 1
   * @maximum 1500000
   */
  sort: number;
  /** 性别 */
  gender_key?: string;
  /**
   * @minLength 0
   * @maxLength 15
   */
  exam_no?: string;
  /** 省份 */
  province_key: string;
  /** 考试科目 */
  exam_subjects: string;
  /**
   * 总分
   * @minimum 0
   * @maximum 750
   */
  total_score: number;
  /**
   * 语文分数
   * @minimum 0
   * @maximum 150
   */
  chinese_score: number;
  /**
   * 数学分数
   * @minimum 0
   * @maximum 150
   */
  math_score: number;
  /**
   * 英语分数
   * @minimum 0
   * @maximum 150
   */
  english_score: number;
  /**
   * 物理分数
   * @minimum 0
   * @maximum 100
   */
  physics_score?: number;
  /**
   * 化学分数
   * @minimum 0
   * @maximum 100
   */
  chemistry_score?: number;
  /**
   * 生物分数
   * @minimum 0
   * @maximum 100
   */
  biology_score?: number;
  /**
   * 政治分数
   * @minimum 0
   * @maximum 100
   */
  politics_score?: number;
  /**
   * 历史分数
   * @minimum 0
   * @maximum 100
   */
  history_score?: number;
  /**
   * 地理分数
   * @minimum 0
   * @maximum 100
   */
  geography_score?: number;
  /**
   * 其他分数
   * @minimum 0
   * @maximum 300
   */
  other_score?: number;
  /** 创建时间 */
  created_at?: string;
  /** 更新时间 */
  updated_at?: string;
  /** 删除时间 */
  deleted_at?: string;
}

// 完整的用户数据结构，与接口返回结果保持一致
export interface UserData {
  /** 用户详细信息 */
  user_info_vo?: UserDetailInfo;
  /** 初始化数据标识 */
  init_data?: number;
  /** 用户积分信息 */
  user_credit?: UserCreditInfo;
}

export interface SendOtpCodeRequest {
  mobile: string;
  wxTempToken?: string;
}

export interface MobileLoginRequest {
  mobile: string;
  code: string;
  inviterId?: number;
  productLine: string;
}

export interface LoginResult {
  userData: UserData;
  token: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
}

export interface UpdateProfileData {
  name: string;
  email: string;
  avatar?: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}
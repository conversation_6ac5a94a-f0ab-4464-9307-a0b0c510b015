// 主要组件导出
export { UserAccountFeature } from './lib/user-account-fe';
export { UserProfile, UserProfileSimple } from './lib/components/user-profile';
export { LoginForm } from './lib/components/login-form';
export { RegisterForm } from './lib/components/register-form';
export { AccountSettings } from './lib/components/account-settings';
export { SimpleLoginModal } from './lib/components/simple-login-modal';
export { CoagentLoginExample } from './lib/components/coagent-login-example';
export type { CoagentLoginExampleProps } from './lib/components/coagent-login-example';

// Server Actions 导出
export * from './lib/actions';
export {
  loginWithMobile,
  sendOtpCode,
  getUserProfile,
  getCurrentUser,
  logout,
  isAuthenticated
} from './lib/actions/userAccountActions';

// 重新导出 Session 相关类型
export type { SessionData, SessionResult } from '@yai-investor-insight/shared-fe-core';

// Hooks 已删除 - 推荐直接使用 Server Actions
// 简单场景：Components/Pages 直接调用 Server Actions
// 复杂场景：可以在应用层创建自定义 hooks 封装 Server Actions

// 适配器导出
export type { AuthAdapter, AuthResult } from './lib/adapters/authAdapter';

// Pages 导出
export { UserAccountPage } from './lib/pages/user-account-page';
export { LoginPage } from './lib/pages/login-page';
export { RegisterPage } from './lib/pages/register-page';

// 类型导出
export type { UserProfileProps } from './lib/components/user-profile';
export type { 
  UserData, 
  SendOtpCodeRequest, 
  MobileLoginRequest, 
  LoginResult, 
  LoginCredentials, 
  RegisterData, 
  UpdateProfileData, 
  ChangePasswordData 
} from './lib/types/userAccountTypes';

// API Client 导出
export { authWithMobile, type AuthMobileArgVO, type AuthResVO } from './lib/api-client';
export { setAuthConfig } from './lib/api-client/custom-instance';
export * from './lib/api-client/types';

// 认证配置导出 - 已移除Coagent相关功能，专注于核心session管理

// 样式导入
import './lib/user-account-fe.module.css';
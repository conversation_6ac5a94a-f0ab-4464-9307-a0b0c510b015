{"name": "user-account-fe", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/user-account-fe/src", "projectType": "library", "tags": ["scope:user-account", "type:fe"], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit", "cwd": "libs/user-account-fe"}}, "build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "web", "outputPath": "dist/libs/user-account-fe", "main": "libs/user-account-fe/src/index.ts", "tsConfig": "libs/user-account-fe/tsconfig.lib.json", "webpackConfig": "libs/user-account-fe/webpack.config.cjs"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/user-account-fe/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/user-account-fe/jest.config.ts"}}}}
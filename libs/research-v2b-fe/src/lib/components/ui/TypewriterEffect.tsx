'use client';

import React, { useState, useEffect, useRef } from 'react';

interface TypewriterEffectProps {
  text: string;
  speed?: number;
  isActive?: boolean;
  onComplete?: () => void;
}

export function TypewriterEffect({ 
  text, 
  speed = 50, 
  isActive = true, 
  onComplete 
}: TypewriterEffectProps) {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const previousTextRef = useRef('');
  const isCompletedRef = useRef(false);

  useEffect(() => {
    if (!isActive || !text) {
      setDisplayedText(text);
      return;
    }

    // 检查是否是增量更新（新文本包含旧文本）
    const isIncremental = text.startsWith(previousTextRef.current);
    
    if (!isIncremental) {
      // 完全不同的文本，重置打字机
      setCurrentIndex(0);
      setDisplayedText('');
      isCompletedRef.current = false;
      previousTextRef.current = '';
    }

    // 从当前索引开始打字
    const startIndex = isIncremental ? Math.max(previousTextRef.current.length, currentIndex) : 0;
    
    if (startIndex < text.length) {
      const timer = setTimeout(() => {
        const nextIndex = startIndex + 1;
        setDisplayedText(text.slice(0, nextIndex));
        setCurrentIndex(nextIndex);
        previousTextRef.current = text.slice(0, nextIndex);
      }, speed);

      return () => clearTimeout(timer);
    } else if (onComplete && !isCompletedRef.current) {
      isCompletedRef.current = true;
      onComplete();
    }
    
    // Add explicit return for the case where startIndex >= text.length but onComplete is not called
    return undefined;
  }, [text, currentIndex, speed, isActive, onComplete]);

  return (
    <span className="inline-block">
      {displayedText}
      {isActive && currentIndex < text.length && (
        <span className="animate-pulse">|</span>
      )}
    </span>
  );
}
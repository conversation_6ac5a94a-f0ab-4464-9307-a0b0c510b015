'use client';

import { useResearchV2Store } from '../../store/store';

/**
 * 研究完成阶段组件
 */
export function CompletedStage() {
  const { 
    researchTopic, 
    finalReportData, 
    finalReport,
    factVerification,
    impactSimulation,
    thesisRecommendation,
    reset 
  } = useResearchV2Store();

  return (
    <>
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center shadow-sm">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900">
            研究已完成
          </h2>
        </div>
        <p className="text-sm text-gray-600">关于 "{researchTopic}" 的投资研究已全部完成</p>
      </div>

      <div className="flex-1 p-6 space-y-6 overflow-y-auto">
        {/* 完成状态概览 */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">研究概览</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">✓</div>
              <div className="text-sm text-gray-600 mt-1">事实核查</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">✓</div>
              <div className="text-sm text-gray-600 mt-1">影响模拟</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">✓</div>
              <div className="text-sm text-gray-600 mt-1">投资建议</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">✓</div>
              <div className="text-sm text-gray-600 mt-1">最终报告</div>
            </div>
          </div>
        </div>

        {/* 最终报告摘要 */}
        {finalReportData?.executiveSummary && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">执行摘要</h3>
            <div className="text-gray-700 leading-relaxed whitespace-pre-line">
              {finalReportData.executiveSummary}
            </div>
          </div>
        )}

        {/* 置信度评分 */}
        {finalReportData?.confidenceScore && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">分析置信度</h3>
            <div className="flex items-center space-x-4">
              <div className="flex-1 bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${finalReportData.confidenceScore}%` }}
                ></div>
              </div>
              <span className="text-lg font-semibold text-gray-900">
                {finalReportData.confidenceScore}%
              </span>
            </div>
          </div>
        )}

        {/* 关键建议 */}
        {finalReportData?.recommendations && finalReportData.recommendations.length > 0 && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">关键建议</h3>
            <ul className="space-y-2">
              {finalReportData.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span className="text-gray-700">{recommendation}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 风险因素 */}
        {finalReportData?.riskFactors && finalReportData.riskFactors.length > 0 && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">风险因素</h3>
            <ul className="space-y-2">
              {finalReportData.riskFactors.map((risk, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-red-500 mt-1">⚠</span>
                  <span className="text-gray-700">{risk}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex justify-center space-x-4">
            <button
              onClick={reset}
              className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              开始新的研究
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
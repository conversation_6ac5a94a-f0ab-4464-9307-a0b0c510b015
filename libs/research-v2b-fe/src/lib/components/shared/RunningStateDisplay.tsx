'use client';

import { StageState, ExecutionStep } from '../../store/store';

interface RunningStateDisplayProps {
  /** 阶段状态数据 */
  stageState: StageState;
  /** 阶段名称，用于显示 */
  stageName: string;
}

/**
 * 通用的运行状态显示组件
 * 显示详细的执行步骤、进度条和当前动作
 */
export function RunningStateDisplay({ stageState, stageName }: RunningStateDisplayProps) {
  const { steps = [], progress = 0, currentAction = '正在处理...' } = stageState;

  return (
    <div className="flex flex-col h-full">
      {/* 顶部标题区域 */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center space-x-3 mb-4">
          <div className="inline-flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {stageName}进行中
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {currentAction}
            </p>
          </div>
        </div>
        
        {/* 总体进度条 */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600">
            <span>总体进度</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* 步骤详情区域 */}
      <div className="flex-1 p-6 space-y-4 overflow-y-auto">
        <h3 className="text-lg font-medium text-gray-900 mb-4">执行步骤</h3>
        
        <div className="space-y-3">
          {steps.map((step, index) => (
            <StepItem 
              key={index} 
              step={step} 
              stepNumber={index + 1}
              isLast={index === steps.length - 1}
            />
          ))}
        </div>

        {/* 底部提示 */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <p className="text-sm text-blue-700">
              AI正在分析数据，请耐心等待...
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

interface StepItemProps {
  step: ExecutionStep;
  stepNumber: number;
  isLast: boolean;
}

/**
 * 单个步骤显示组件
 */
function StepItem({ step, stepNumber, isLast }: StepItemProps) {
  const getStepIcon = () => {
    switch (step.status) {
      case 'completed':
        return (
          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'running':
        return (
          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent"></div>
          </div>
        );
      case 'pending':
      default:
        return (
          <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
          </div>
        );
    }
  };

  const getStepColor = () => {
    switch (step.status) {
      case 'completed':
        return 'text-green-700';
      case 'running':
        return 'text-blue-700';
      case 'pending':
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="flex items-start space-x-3">
      {/* 步骤图标和连接线 */}
      <div className="flex flex-col items-center">
        {getStepIcon()}
        {!isLast && (
          <div className={`w-0.5 h-8 mt-2 ${
            step.status === 'completed' ? 'bg-green-200' : 'bg-gray-200'
          }`}></div>
        )}
      </div>
      
      {/* 步骤内容 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-900">
            步骤 {stepNumber}
          </span>
          <span className={`text-sm font-medium ${getStepColor()}`}>
            {step.name}
          </span>
          {step.status === 'running' && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              进行中
            </span>
          )}
          {step.status === 'completed' && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              已完成
            </span>
          )}
        </div>
        {step.description && (
          <p className="mt-1 text-sm text-gray-600">
            {step.description}
          </p>
        )}
      </div>
    </div>
  );
}
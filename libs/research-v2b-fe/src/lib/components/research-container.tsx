'use client';

// 添加缺失的导入
import { useState, useEffect, useCallback } from 'react';
import { useResearchV2Store } from '../store/store';
import { useResearchAGUI } from '../hooks/useResearchAGUI';
import { ResearchStage } from '../hooks/event';
import { ResearchSidebar } from './research-sidebar';
import { IdleStage } from './stages/IdleStage';
import { FactVerificationStage } from './stages/FactVerificationStage';
import { ImpactSimulationStage } from './stages/ImpactSimulationStage';
import { ThesisRecommendationStage } from './stages/ThesisRecommendationStage';
import { FinalReportStage } from './stages/FinalReportStage';
import { CompletedStage } from './stages/CompletedStage';

/**
 * 研究容器组件 - 组合侧边栏和主区域，根据当前阶段显示对应内容
 */
const ResearchContainer = () => {
  const { 
    currentTaskId, 
    threadId, 
    setResumeTaskInfo,
    currentStage, 
    isRunning,
    error,
    handleResearchStarted,
    handleStageStarted,
    handleStageProgress,
    handleStageCompleted,
    handleResearchCompleted,
    handleResearchFailed,
    handleStepStarted,    // 添加这个
    handleStepFinished,   // 添加这个
    clearError
  } = useResearchV2Store();
  
  const [currentTypingContent, setCurrentTypingContent] = useState('');
  const [currentTypingStage, setCurrentTypingStage] = useState<ResearchStage | null>(null);

  // AGUI事件处理
  const { connectionStatus, startResearch } = useResearchAGUI({
    onResearchStarted: (event) => {
      console.log('研究已开始:', event);
      handleResearchStarted(event);
    },
    onStageStarted: (event) => {
      console.log('阶段开始:', event);
      handleStageStarted(event);
      setCurrentTypingStage(event.data.stage);
      setCurrentTypingContent('');
    },
    onStageProgress: (event) => {
      console.log('阶段进度:', event);
      handleStageProgress(event);
      
      // 处理打字机效果内容
      if (event.data.content && event.data.stage === currentTypingStage) {
        setCurrentTypingContent(prev => prev + event.data.content);
      }
    },
    onStageCompleted: (event) => {
      console.log('阶段完成:', event);
      handleStageCompleted(event);
    },
    onStepStarted: (event) => {              // 新增步骤开始处理
      console.log('步骤开始:', event);
      handleStepStarted(event);
    },
    onStepFinished: (event) => {             // 新增步骤完成处理
      console.log('步骤完成:', event);
      handleStepFinished(event);
    },
    onResearchCompleted: (event) => {
      console.log('研究完成:', event);
      handleResearchCompleted(event);
      setCurrentTypingStage(null);
      setCurrentTypingContent('');
    },
    onResearchFailed: (event) => {
      console.error('研究失败:', event);
      handleResearchFailed(event);
      setCurrentTypingStage(null);
      setCurrentTypingContent('');
    },
    onError: (event) => {
      console.error('AGUI错误:', event);
      handleResearchFailed({
        type: 'research_failed',
        task_id: '',
        timestamp: new Date().toISOString(),
        data: {
          error_code: event.data.error_code,
          error_message: event.data.error_message,
          retry_possible: event.data.recoverable
        }
      } as any);
    }
  });

  // 错误处理
  useEffect(() => {
    if (error) {
      // 可以在这里添加错误通知逻辑
      console.error('研究过程中发生错误:', error);
    }
  }, [error]);

  // 根据当前阶段渲染对应组件
  const renderMainArea = () => {
    switch (currentStage) {
      case ResearchStage.IDLE:
        return <IdleStage />;
      case ResearchStage.FACT_VERIFICATION:
        return (
          <FactVerificationStage 
            typingContent={currentTypingStage === ResearchStage.FACT_VERIFICATION ? currentTypingContent : undefined}
            startResearch={startResearch}
          />
        );
      case ResearchStage.IMPACT_SIMULATION:
        return (
          <ImpactSimulationStage 
            typingContent={currentTypingStage === ResearchStage.IMPACT_SIMULATION ? currentTypingContent : undefined}
          />
        );
      case ResearchStage.THESIS_RECOMMENDATION:
        return (
          <ThesisRecommendationStage 
            typingContent={currentTypingStage === ResearchStage.THESIS_RECOMMENDATION ? currentTypingContent : undefined}
          />
        );
      case ResearchStage.FINAL_REPORT:
        return (
          <FinalReportStage 
            typingContent={currentTypingStage === ResearchStage.FINAL_REPORT ? currentTypingContent : undefined}
          />
        );
      case ResearchStage.COMPLETED:
        return <CompletedStage />;
      default:
        return <IdleStage />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <ResearchSidebar 
        startResearch={startResearch}
        connectionStatus={connectionStatus}
        isRunning={isRunning}
        error={error}
        onClearError={clearError}
      />
      <main className="flex-1 overflow-hidden">
        {renderMainArea()}
      </main>
    </div>
  );
};

export { ResearchContainer };
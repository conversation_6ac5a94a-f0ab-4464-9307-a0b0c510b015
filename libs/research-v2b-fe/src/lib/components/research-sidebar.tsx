'use client';

// 添加缺失的导入
import { useState, useCallback } from 'react';
import { useResearchV2Store, ResearchStage, StageStatus } from '../store/store';
import React from 'react';

// 更新 props 接口
interface ResearchSidebarProps {
  startResearch: (topic: string) => Promise<any>; // 更新返回类型
  connectionStatus: 'connecting' | 'connected' | 'error';
  isRunning: boolean;
  error: string | null;
  onClearError: () => void;
}

export function ResearchSidebar({ 
  startResearch, 
  connectionStatus, 
  isRunning, 
  error, 
  onClearError 
}: ResearchSidebarProps) {
  console.log('=== ResearchSidebar 渲染开始 ===');
  console.log('接收到的 props:', {
    startResearch: typeof startResearch === 'function' ? '函数存在' : '函数不存在', // 修复条件判断
    connectionStatus,
    isRunning,
    error
  });
  
  const { 
    currentStage, 
    factVerification,
    impactSimulation,
    thesisRecommendation,
    finalReport,
    reset // 添加 reset 方法
  } = useResearchV2Store();
  
  const [topic, setTopic] = useState('');
  
  console.log('当前状态:', { currentStage, topic });

  const handleStartResearch = useCallback(async (topic: string) => {
    if (!topic.trim()) return;
    
    try {
      reset(); // 现在已定义
      
      const result = await startResearch(topic);
      
      if (result) { // 现在 result 有返回值
        console.log('研究任务启动成功:', result);
      }
    } catch (error) {
      console.error('启动研究失败:', error);
    }
  }, [startResearch, reset]);

  console.log('=== ResearchSidebar 渲染结束 ===');
  
  const getStageStatus = (stage: ResearchStage) => {
    let stageState;
    
    switch (stage) {
      case ResearchStage.FACT_VERIFICATION:
        stageState = factVerification;
        break;
      case ResearchStage.IMPACT_SIMULATION:
        stageState = impactSimulation;
        break;
      case ResearchStage.THESIS_RECOMMENDATION:
        stageState = thesisRecommendation;
        break;
      case ResearchStage.FINAL_REPORT:
        stageState = finalReport;
        break;
      default:
        return 'pending';
    }
    
    if (stageState.status === StageStatus.RUNNING) return 'current';
    if (stageState.status === StageStatus.COMPLETED) return 'completed';
    return 'pending';
  };

  const renderStageIcon = (stage: ResearchStage) => {
    const status = getStageStatus(stage);
    
    if (status === 'completed') {
      return (
        <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      );
    } else if (status === 'current') {
      return (
        <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-full flex items-center justify-center shadow-lg relative">
          <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
          <div className="absolute inset-0 rounded-full border-2 border-blue-300 animate-ping"></div>
        </div>
      );
    }
    
    // 不同阶段的图标
    const icons: Record<ResearchStage, React.JSX.Element> = {
      [ResearchStage.IDLE]: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
        </svg>
      ),
      [ResearchStage.FACT_VERIFICATION]: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      ),
      [ResearchStage.IMPACT_SIMULATION]: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"/>
          <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"/>
        </svg>
      ),
      [ResearchStage.THESIS_RECOMMENDATION]: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
        </svg>
      ),
      [ResearchStage.FINAL_REPORT]: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
        </svg>
      ),
      [ResearchStage.COMPLETED]: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      )
    };
    
    return (
      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-gray-300 to-gray-400 text-gray-600 rounded-full flex items-center justify-center shadow-sm border-2 border-white transition-all duration-300">
        {icons[stage]}
      </div>
    );
  };

  return (
    <div className="w-80 bg-gradient-to-b from-slate-50 via-blue-50/30 to-indigo-50/50 border-r border-gray-200/60 p-6 flex flex-col relative">
      {/* 背景装饰 */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-100/40 to-transparent rounded-bl-full"></div>
      <div className="absolute bottom-20 left-0 w-24 h-24 bg-gradient-to-tr from-indigo-100/30 to-transparent rounded-tr-full"></div>
      
      {/* 研究主题输入区 */}
      <div className="mb-6 relative z-10">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-5 shadow-lg border border-white/60 hover:shadow-xl transition-all duration-300">
          <div className="flex items-center space-x-2 mb-4">
            <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h2 className="text-lg font-semibold text-gray-900">研究主题</h2>
          </div>
          <div className="space-y-4">
            <textarea
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              className="w-full p-4 border border-gray-200/60 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-300 resize-none transition-all duration-200 bg-gray-50/50 hover:bg-white placeholder-gray-400"
              placeholder="请输入要研究的公司或主题..."
              rows={3}
              disabled={currentStage !== ResearchStage.IDLE}
            />
            <button 
              onClick={() => handleStartResearch(topic)}
              disabled={!topic.trim() || currentStage !== ResearchStage.IDLE}
              className="w-full bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white py-3.5 px-4 rounded-xl text-sm font-medium hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:translate-y-0"
            >
              <span className="flex items-center justify-center space-x-2">
                {currentStage === ResearchStage.IDLE ? (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span>开始研究</span>
                  </>
                ) : (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>研究进行中...</span>
                  </>
                )}
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* 分析流程 */}
      <div className="mb-8 relative z-10">
        <div className="flex items-center space-x-2 mb-3">
          <div className="w-6 h-6 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h2 className="text-lg font-semibold text-gray-900">分析流程</h2>
        </div>
        <p className="text-sm text-gray-600 ml-8">AI驱动的多维度投资分析</p>
      </div>

      <div className="flex-1 space-y-5 relative z-10">
        {/* 事实核查验证 */}
        <div className="flex items-start space-x-4 group">
          {renderStageIcon(ResearchStage.FACT_VERIFICATION)}
          <div className="flex-1 pt-1">
            <h3 className={`font-semibold text-base transition-colors ${getStageStatus(ResearchStage.FACT_VERIFICATION) === 'completed' ? 'text-green-700' : getStageStatus(ResearchStage.FACT_VERIFICATION) === 'current' ? 'text-blue-700' : 'text-gray-700 group-hover:text-gray-800'}`}>
              事实核查验证
            </h3>
            <p className="text-sm text-gray-500 mt-1 leading-relaxed">基础事实验证与数据收集</p>
            {/* 始终显示连接线以保持间距一致 */}
            <div className={`w-0.5 h-8 ml-5 mt-3 rounded-full transition-colors ${
              getStageStatus(ResearchStage.FACT_VERIFICATION) === 'completed' 
                ? 'bg-gradient-to-b from-blue-300 to-indigo-300' 
                : 'bg-gray-200'
            }`}></div>
          </div>
        </div>

        {/* 影响模拟分析 */}
        <div className="flex items-start space-x-4 group">
          {renderStageIcon(ResearchStage.IMPACT_SIMULATION)}
          <div className="flex-1 pt-1">
            <h3 className={`font-semibold text-base transition-colors ${getStageStatus(ResearchStage.IMPACT_SIMULATION) === 'completed' ? 'text-green-700' : getStageStatus(ResearchStage.IMPACT_SIMULATION) === 'current' ? 'text-blue-700' : 'text-gray-700 group-hover:text-gray-800'}`}>
              影响模拟分析  
            </h3>
            <p className="text-sm text-gray-500 mt-1 leading-relaxed">多维度影响评估与风险识别</p>
            {/* 始终显示连接线以保持间距一致 */}
            <div className={`w-0.5 h-8 ml-5 mt-3 rounded-full transition-colors ${
              getStageStatus(ResearchStage.IMPACT_SIMULATION) === 'completed' 
                ? 'bg-gradient-to-b from-blue-300 to-indigo-300' 
                : 'bg-gray-200'
            }`}></div>
          </div>
        </div>

        {/* 投资策略建议 */}
        <div className="flex items-start space-x-4 group">
          {renderStageIcon(ResearchStage.THESIS_RECOMMENDATION)}
          <div className="flex-1 pt-1">
            <h3 className={`font-semibold text-base transition-colors ${getStageStatus(ResearchStage.THESIS_RECOMMENDATION) === 'completed' ? 'text-green-700' : getStageStatus(ResearchStage.THESIS_RECOMMENDATION) === 'current' ? 'text-blue-700' : 'text-gray-700 group-hover:text-gray-800'}`}>
              投资策略建议
            </h3>
            <p className="text-sm text-gray-500 mt-1 leading-relaxed">专业投资策略制定</p>
            {/* 始终显示连接线以保持间距一致 */}
            <div className={`w-0.5 h-8 ml-5 mt-3 rounded-full transition-colors ${
              getStageStatus(ResearchStage.THESIS_RECOMMENDATION) === 'completed' 
                ? 'bg-gradient-to-b from-green-300 to-emerald-300' 
                : 'bg-gray-200'
            }`}></div>
          </div>
        </div>

        {/* 最终报告 */}
        <div className="flex items-start space-x-4 group">
          {renderStageIcon(ResearchStage.FINAL_REPORT)}
          <div className="pt-1">
            <h3 className={`font-semibold text-base transition-colors ${getStageStatus(ResearchStage.FINAL_REPORT) === 'completed' ? 'text-green-700' : getStageStatus(ResearchStage.FINAL_REPORT) === 'current' ? 'text-blue-700' : 'text-gray-700 group-hover:text-gray-800'}`}>
              最终报告
            </h3>
            <p className="text-sm text-gray-500 mt-1 leading-relaxed">完整研究报告生成</p>
          </div>
        </div>
      </div>
    </div>
  );
}
/**
 * Research V2 业务逻辑和 Actions
 */
import { StateCreator } from 'zustand';
import { 
  ResearchV2Store, 
  ResearchStage, 
  StageStatus,
  ExecutionStep,
  StageState
} from './types';
import {
  ResearchStartedEvent,
  StageStartedEvent,
  StageProgressEvent,
  StageCompletedEvent,
  ResearchCompletedEvent,
  ResearchFailedEvent,
  StepStartedEvent,    // 添加这个导入
  StepFinishedEvent,   // 添加这个导入
  ResearchStepStatus
} from '../hooks/event';
import { initialState } from './initial-state';
import { 
  getStageSteps, 
  getStageActions, 
  generateMockResults 
} from './mock-data';

export const createResearchActions: StateCreator<
  ResearchV2Store,
  [],
  [],
  ResearchV2Store
> = (set, get) => ({
  ...initialState,
  
  // 生成模拟结果
  _generateMockResults: generateMockResults,
  
  // 事件处理方法
  // 在 createResearchActions 中添加以下方法
  
  // 新增 currentTaskId 管理方法
  setCurrentTaskId: (taskId: string | null) => {
    set({ currentTaskId: taskId });
  },
  
  getCurrentTaskId: () => {
    return get().currentTaskId;
  },
  
  // 更新 handleResearchStarted 方法
  handleResearchStarted: (event: ResearchStartedEvent) => {
    console.log('处理研究开始事件:', event);
    
    set({
      taskId: event.task_id,
      runId: event.run_id,
      threadId: event.thread_id,
      currentTaskId: event.task_id, // 添加这行
      researchTopic: event.data.topic,
      currentStage: ResearchStage.FACT_VERIFICATION,
      isRunning: true,
      totalProgress: 0,
      error: null,
      errorCode: undefined,
      retryPossible: undefined
    });
  },
  
  // 更新 reset 方法
  // 修复 reset 方法
  reset: () => {
    set({
      ...initialState,
      currentTaskId: null, // 确保重置时清空 currentTaskId
      threadId: undefined  // 同时清空 threadId
    });
  },
  
  // 新增 threadId 管理方法
  setThreadId: (threadId: string) => {
    set((state) => ({
      ...state,
      threadId
    }));
  },
  
getThreadId: (): string | null => {
  // 确保返回类型为 string | null,避免返回 undefined
  return get().threadId || null;
},
  
  // 新增恢复任务的方法
  setResumeTaskInfo: (taskId: string, threadId: string) => {
    set((state) => ({
      ...state,
      currentTaskId: taskId,
      threadId: threadId,
      isRunning: true
    }));
  },
  
  handleStageStarted: (event: StageStartedEvent) => {
    console.log('处理阶段开始事件:', event);
    
    const stageKey = getStageKey(event.data.stage);
    
    set(state => {
      const currentStageState = state[stageKey as keyof typeof state] as StageState;
      
      // 只有当前阶段没有步骤时，才使用后端提供的初始步骤
      // 否则保留现有的步骤（通过 handleStepStarted 添加的）
      const existingSteps = currentStageState?.steps || [];
      const shouldUseBackendSteps = existingSteps.length === 0;
      
      const steps: ExecutionStep[] = shouldUseBackendSteps 
        ? event.data.steps.map((step, index) => ({
            ...step,
            status: step.status as ResearchStepStatus,
            displayOrder: index
          }))
        : existingSteps;
      
      return {
        currentStage: event.data.stage,
        [stageKey]: {
          status: StageStatus.RUNNING,
          stageName: event.data.stage_name,
          description: event.data.description,
          estimatedDuration: event.data.estimated_duration,
          steps: steps,
          progress: 0,
          startedAt: new Date().toISOString(),
          currentAction: '准备开始...'
        } as StageState
      };
    });
  },
  
  handleStageProgress: (event: StageProgressEvent) => {
    console.log('处理阶段进度事件:', event);
    
    const stageKey = getStageKey(event.data.stage);
    
    set(state => {
      const currentStageState = state[stageKey as keyof typeof state] as StageState;
      
      return {
        [stageKey]: {
          ...currentStageState,
          progress: event.data.progress,
          currentAction: event.data.current_action || currentStageState.currentAction,
          steps: event.data.steps ? event.data.steps.map((step, index) => ({
            ...step,
            status: step.status as ResearchStepStatus,
            displayOrder: index
          })) : currentStageState.steps,
          partialResult: event.data.partial_result
        } as StageState,
        totalProgress: calculateTotalProgress(state.currentStage, event.data.progress)
      };
    });
  },
  
  handleStageCompleted: (event: StageCompletedEvent) => {
    console.log('处理阶段完成事件:', event);
    
    const stageKey = getStageKey(event.data.stage);
    
    set(state => {
      const currentStageState = state[stageKey as keyof typeof state] as StageState;
      
      return {
        [stageKey]: {
          ...currentStageState,
          status: StageStatus.COMPLETED,
          progress: 100,
          completedAt: new Date().toISOString(),
          duration: event.data.duration,
          result: event.data.result.summary,
          confidenceScore: event.data.result.confidence_score,
          keyFindings: event.data.result.key_findings,
          currentAction: undefined,
          steps: currentStageState.steps?.map(step => ({
            ...step,
            status: ResearchStepStatus.COMPLETED
          }))
        } as StageState,
        // 如果有下一个阶段，更新当前阶段
        currentStage: event.data.next_stage || state.currentStage
      };
    });
  },
  
  handleResearchCompleted: (event: ResearchCompletedEvent) => {
    console.log('处理研究完成事件:', event);
    
    set({
      currentStage: ResearchStage.COMPLETED,
      isRunning: false,
      totalProgress: 100,
      finalReportData: {
        executiveSummary: event.data.final_report.executive_summary,
        factVerificationResult: event.data.final_report.fact_verification_result,
        impactSimulationResult: event.data.final_report.impact_simulation_result,
        thesisRecommendationResult: event.data.final_report.thesis_recommendation_result,
        finalReportContent: event.data.final_report.final_report_content,
        confidenceScore: event.data.final_report.confidence_score,
        recommendations: event.data.final_report.recommendations,
        riskFactors: event.data.final_report.risk_factors,
        chartsData: event.data.final_report.charts_data
      }
    });
  },
  
  handleResearchFailed: (event: ResearchFailedEvent) => {
    console.log('处理研究失败事件:', event);
    
    set({
      isRunning: false,
      error: event.data.error_message,
      errorCode: event.data.error_code,
      retryPossible: event.data.retry_possible,
      currentStage: event.data.failed_stage || ResearchStage.IDLE
    });
    
    // 如果有失败的阶段，更新该阶段状态
    if (event.data.failed_stage) {
      const stageKey = getStageKey(event.data.failed_stage);
      set(state => ({
        [stageKey]: {
          ...state[stageKey as keyof typeof state] as StageState,
          status: StageStatus.FAILED,
          error: event.data.error_message
        }
      }));
    }
  },
  handleStepStarted: (event: StepStartedEvent) => {
    console.log('处理步骤开始事件:', event);
    
    const stageKey = getStageKey(event.data.stage);
    
    set(state => {
      const currentStageState = state[stageKey as keyof typeof state] as StageState;
      
      // 创建新的步骤对象
      const newStep: ExecutionStep = {
        name: event.data.step_name,
        description: event.data.description,
        status: ResearchStepStatus.RUNNING,
        started_at: new Date().toISOString()
      };
      
      // 检查是否已存在相同名称的步骤，如果存在则更新，否则追加
      const existingStepIndex = currentStageState.steps?.findIndex(
        step => step.name === event.data.step_name
      ) ?? -1;
      
      let updatedSteps: ExecutionStep[];
      if (existingStepIndex >= 0) {
        // 更新现有步骤
        updatedSteps = currentStageState.steps?.map((step, index) => 
          index === existingStepIndex ? newStep : step
        ) || [newStep];
      } else {
        // 追加新步骤
        updatedSteps = [...(currentStageState.steps || []), newStep];
      }
      
      return {
        [stageKey]: {
          ...currentStageState,
          currentAction: event.data.description || event.data.step_name,
          steps: updatedSteps
        } as StageState
      };
    });
  },

  handleStepFinished: (event: StepFinishedEvent) => {
    console.log('处理步骤完成事件:', event);
    
    const stageKey = getStageKey(event.data.stage);
    
    set(state => {
      const currentStageState = state[stageKey as keyof typeof state] as StageState;
      
      // 查找对应的步骤并更新状态
      const updatedSteps = currentStageState.steps?.map(step =>
        step.name === event.data.step_name
          ? {
              ...step,
              status: event.data.success ? ResearchStepStatus.COMPLETED : ResearchStepStatus.FAILED,
              completed_at: new Date().toISOString(),
              result: event.data.result_summary,
              error_message: event.data.error,
              description: event.data.description || step.description
            }
          : step
      ) || [];
      
      return {
        [stageKey]: {
          ...currentStageState,
          steps: updatedSteps,
          currentAction: event.data.success ? undefined : currentStageState.currentAction
        } as StageState
      };
    });
  },

  // 辅助方法
  updateStageState: (stage: ResearchStage, updates: Partial<StageState>) => {
    const stageKey = getStageKey(stage);
    set(state => ({
      [stageKey]: {
        ...state[stageKey as keyof typeof state] as StageState,
        ...updates
      }
    }));
  },
  
  updateStageSteps: (stage: ResearchStage, steps: ExecutionStep[]) => {
    const stageKey = getStageKey(stage);
    set(state => ({
      [stageKey]: {
        ...state[stageKey as keyof typeof state] as StageState,
        steps
      }
    }));
  },
  
  setTaskInfo: (taskId: string, runId?: string, threadId?: string) => {
    set({ taskId, runId, threadId });
  },
  
  // 原有方法保持不变
  startResearch: async (topic: string) => {
    set({ 
      researchTopic: topic, 
      currentStage: ResearchStage.FACT_VERIFICATION,
      isRunning: true,
      error: null 
    });
  },
  
  // 添加缺失的 completeStage 方法
  completeStage: async (stage: ResearchStage, userInput?: string) => {
    const stageKey = getStageKey(stage);
    const nextStage = getNextStage(stage);
    
    set(state => ({
      [stageKey]: {
        ...state[stageKey as keyof typeof state] as StageState,
        status: StageStatus.COMPLETED,
        progress: 100,
        completedAt: new Date().toISOString()
      },
      currentStage: nextStage || ResearchStage.COMPLETED,
      currentInput: userInput || ''
    }));
  },
  
  setCurrentStage: (stage: ResearchStage) => {
    set({ currentStage: stage });
  },
  
  updateInput: (input: string) => {
    set({ currentInput: input });
  },
  
  clearError: () => {
    set({ error: null, errorCode: undefined, retryPossible: undefined });
  },
  
  // 添加缺失的 _simulateResearch 方法
  _simulateResearch: async (stage: ResearchStage, duration: number) => {
    const stageKey = getStageKey(stage);
    const steps = getStageSteps(stage);
    
    // 设置阶段开始状态
    set(state => ({
      [stageKey]: {
        ...state[stageKey as keyof typeof state] as StageState,
        status: StageStatus.RUNNING,
        progress: 0,
        steps: steps,
        startedAt: new Date().toISOString()
      }
    }));
    
    // 模拟进度更新
    const stepDuration = duration / steps.length;
    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, stepDuration));
      
      const progress = ((i + 1) / steps.length) * 100;
      const currentAction = getStageActions(stage, i); // 修复：传递 stepIndex 参数
      
      set(state => ({
        [stageKey]: {
          ...state[stageKey as keyof typeof state] as StageState,
          progress,
          currentAction
        }
      }));
    }
    
    // 完成阶段
    const mockResults = generateMockResults(get().researchTopic);
    const result = getStageResult(stage, mockResults);
    
    set(state => ({
      [stageKey]: {
        ...state[stageKey as keyof typeof state] as StageState,
        status: StageStatus.COMPLETED,
        progress: 100,
        result,
        completedAt: new Date().toISOString(),
        duration
      }
    }));
  }
});

// 辅助函数
function getStageKey(stage: ResearchStage): string {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return 'factVerification';
    case ResearchStage.IMPACT_SIMULATION:
      return 'impactSimulation';
    case ResearchStage.THESIS_RECOMMENDATION:
      return 'thesisRecommendation';
    case ResearchStage.FINAL_REPORT:
      return 'finalReport';
    default:
      return 'factVerification';
  }
}

function calculateTotalProgress(currentStage: ResearchStage, stageProgress: number): number {
  const stageWeights: Record<ResearchStage, number> = {
    [ResearchStage.IDLE]: 0,
    [ResearchStage.FACT_VERIFICATION]: 0.25,
    [ResearchStage.IMPACT_SIMULATION]: 0.25,
    [ResearchStage.THESIS_RECOMMENDATION]: 0.25,
    [ResearchStage.FINAL_REPORT]: 0.25,
    [ResearchStage.COMPLETED]: 1
  };
  
  const stages = [ResearchStage.FACT_VERIFICATION, ResearchStage.IMPACT_SIMULATION, ResearchStage.THESIS_RECOMMENDATION, ResearchStage.FINAL_REPORT];
  const currentIndex = stages.indexOf(currentStage);
  
  let totalProgress = 0;
  
  // 已完成阶段的进度
  for (let i = 0; i < currentIndex; i++) {
    totalProgress += stageWeights[stages[i]] * 100;
  }
  
  // 当前阶段的进度
  if (currentIndex >= 0) {
    totalProgress += stageWeights[currentStage] * stageProgress;
  }
  
  return Math.round(totalProgress);
}

function getStageResult(stage: ResearchStage, mockResults: ReturnType<typeof generateMockResults>): string {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return mockResults.factVerification;
    case ResearchStage.IMPACT_SIMULATION:
      return mockResults.impactSimulation;
    case ResearchStage.THESIS_RECOMMENDATION:
      return mockResults.thesisRecommendation;
    case ResearchStage.FINAL_REPORT:
      return mockResults.finalReport;
    default:
      return '';
  }
}

function getFeedbackKey(stage: ResearchStage): string | null {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return 'factVerification';
    case ResearchStage.IMPACT_SIMULATION:
      return 'impactSimulation';
    default:
      return null;
  }
}

function getNextStage(stage: ResearchStage): ResearchStage | null {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return ResearchStage.IMPACT_SIMULATION;
    case ResearchStage.IMPACT_SIMULATION:
      return ResearchStage.THESIS_RECOMMENDATION;
    case ResearchStage.THESIS_RECOMMENDATION:
      return ResearchStage.FINAL_REPORT;
    case ResearchStage.FINAL_REPORT:
      return null;
    default:
      return null;
  }
}

function getStageDuration(stage: ResearchStage): number {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return 8000;
    case ResearchStage.IMPACT_SIMULATION:
      return 10000;
    case ResearchStage.THESIS_RECOMMENDATION:
      return 8000;
    case ResearchStage.FINAL_REPORT:
      return 6000;
    default:
      return 5000;
  }
}

/**
 * Research V2 状态管理类型定义
 */
import {
  ResearchStage,
  ResearchStepStatus,
  ResearchStep,
  ResearchEventType
} from '../hooks/event';

// 阶段状态枚举
export enum StageStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// 执行步骤状态（映射到共享类型）
export type StepStatus = ResearchStepStatus;

// 执行步骤接口（扩展共享类型）
export interface ExecutionStep extends ResearchStep {
  // 可以添加前端特有的属性
  displayOrder?: number;
  isVisible?: boolean;
}

// 阶段状态接口（增强版）
export interface StageState {
  status: StageStatus;
  result?: string;
  currentAction?: string;
  progress?: number;
  steps?: ExecutionStep[];
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  error?: string;
  // 新增字段
  stageName?: string;
  description?: string;
  estimatedDuration?: number;
  partialResult?: unknown;
  confidenceScore?: number;
  keyFindings?: string[];
}

// 研究状态接口
export interface ResearchV2State {
  // 当前状态
  currentStage: ResearchStage;
  researchTopic: string;
  currentInput: string;
  
  // 任务信息
  taskId?: string;
  runId?: string;
  threadId?: string;
  currentTaskId: string | null;
  // 各阶段状态
  factVerification: StageState;
  impactSimulation: StageState;
  thesisRecommendation: StageState;
  finalReport: StageState;
  
  // 用户反馈
  userFeedback: {
    factVerification?: string;
    impactSimulation?: string;
  };
  
  // 最终报告
  finalReportData?: {
    executiveSummary: string;
    factVerificationResult: unknown;
    impactSimulationResult: unknown;
    thesisRecommendationResult: unknown;
    finalReportContent: string;
    confidenceScore: number;
    recommendations: string[];
    riskFactors: string[];
    chartsData?: unknown[];
  };
  
  // 实时状态
  isRunning: boolean;
  totalProgress: number;
  estimatedTimeRemaining?: number;
  
  // 错误处理
  error: string | null;
  errorCode?: string;
  retryPossible?: boolean;
}

// 研究操作接口
export interface ResearchV2Actions {
  // 基础操作
  startResearch: (topic: string) => Promise<void>;
  completeStage: (stage: ResearchStage, userInput?: string) => Promise<void>;
  updateInput: (input: string) => void;
  setCurrentStage: (stage: ResearchStage) => void;
  reset: () => void;
  clearError: () => void;
  
  // 新增事件处理方法
  handleResearchStarted: (event: any) => void;
  handleStageStarted: (event: any) => void;
  handleStageProgress: (event: any) => void;
  handleStageCompleted: (event: any) => void;
  handleResearchCompleted: (event: any) => void;
  handleResearchFailed: (event: any) => void;
  handleStepStarted: (event: any) => void;
  handleStepFinished: (event: any) => void;
  
  // 状态更新方法
  updateStageState: (stage: ResearchStage, updates: Partial<StageState>) => void;
  updateStageSteps: (stage: ResearchStage, steps: ExecutionStep[]) => void;
  setTaskInfo: (taskId: string, runId?: string, threadId?: string) => void;
  setThreadId: (threadId: string) => void;
  getThreadId: () => string | null;
  setResumeTaskInfo: (taskId: string, threadId: string) => void;
  setCurrentTaskId: (taskId: string | null) => void;
  getCurrentTaskId: () => string | null;
  
  // 内部方法
  _simulateResearch: (stage: ResearchStage, duration: number) => Promise<void>;
  _generateMockResults: (topic: string) => {
    factVerification: string;
    impactSimulation: string;
    thesisRecommendation: string;
    finalReport: string;
  };
}

// 完整的 Store 类型
export type ResearchV2Store = ResearchV2State & ResearchV2Actions;

// 导出共享类型
export { ResearchStage, ResearchStepStatus };
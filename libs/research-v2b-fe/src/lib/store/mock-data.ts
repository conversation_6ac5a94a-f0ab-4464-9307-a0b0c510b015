/**
 * Research V2 模拟数据生成和步骤定义
 */
import { ResearchStage, ExecutionStep, ResearchStepStatus } from './types';

// 各阶段的执行步骤定义
export const getStageSteps = (stage: ResearchStage): ExecutionStep[] => {
  switch (stage) {
    case ResearchStage.FACT_VERIFICATION:
      return [
        { name: "数据收集", status: ResearchStepStatus.PENDING, description: "收集相关财务和市场数据" },
        { name: "信息验证", status: ResearchStepStatus.PENDING, description: "核查信息来源可靠性" },
        { name: "交叉验证", status: ResearchStepStatus.PENDING, description: "多源数据交叉对比" },
        { name: "结论整理", status: ResearchStepStatus.PENDING, description: "整理核查结果" }
      ];
    
    case ResearchStage.IMPACT_SIMULATION:
      return [
        { name: "市场分析", status: ResearchStepStatus.PENDING, description: "分析市场环境和竞争态势" },
        { name: "财务建模", status: ResearchStepStatus.PENDING, description: "构建财务影响模型" },
        { name: "风险评估", status: ResearchStepStatus.PENDING, description: "识别主要风险因素" },
        { name: "机会识别", status: ResearchStepStatus.PENDING, description: "发现潜在投资机会" },
        { name: "影响量化", status: ResearchStepStatus.PENDING, description: "量化分析预期影响" }
      ];
    
    case ResearchStage.THESIS_RECOMMENDATION:
      return [
        { name: "基本面分析", status: ResearchStepStatus.PENDING, description: "深度分析基本面指标" },
        { name: "估值分析", status: ResearchStepStatus.PENDING, description: "评估当前估值水平" },
        { name: "策略制定", status: ResearchStepStatus.PENDING, description: "制定投资策略建议" },
        { name: "风险控制", status: ResearchStepStatus.PENDING, description: "设定风险控制措施" }
      ];
    
    case ResearchStage.FINAL_REPORT:
      return [
        { name: "数据整合", status: ResearchStepStatus.PENDING, description: "整合所有分析结果" },
        { name: "报告撰写", status: ResearchStepStatus.PENDING, description: "撰写完整投资报告" },
        { name: "质量检查", status: ResearchStepStatus.PENDING, description: "检查报告完整性和准确性" }
      ];
    
    default:
      return [];
  }
};

// 各阶段的动作描述
export const getStageActions = (stage: ResearchStage, stepIndex: number): string => {
  const steps = getStageSteps(stage);
  if (stepIndex >= 0 && stepIndex < steps.length) {
    return `正在${steps[stepIndex].description}...`;
  }
  return '正在处理...';
};

// 生成模拟结果
export const generateMockResults = (topic: string) => ({
  factVerification: `经过深入事实核查验证，关于"${topic}"的相关信息已得到验证。通过多维度数据源分析，包括公开财报、行业报告、新闻资讯等，确认了关键事实的准确性。主要发现包括：市场地位分析、财务状况评估、管理层变动情况、以及最新业务发展动态。这些信息为后续分析提供了可靠的基础数据支撑。`,
  
  impactSimulation: `基于对"${topic}"的事实核查结果，深入模拟分析其对相关市场和投资环境的影响：

1. 市场影响：预计将对行业格局产生中长期影响，可能改变竞争态势
2. 财务影响：对公司估值、收入结构、盈利能力等关键指标的潜在影响
3. 风险评估：识别出的主要风险因素和不确定性来源
4. 机会识别：分析中发现的潜在投资机会和增长点
5. 时间维度：短期、中期、长期影响的差异化分析`,
  
  thesisRecommendation: `综合事实核查和影响模拟结果，针对"${topic}"给出以下论文级投资建议：

📊 投资建议：谨慎乐观

核心观点：
• 基本面分析显示具备长期投资价值
• 当前估值水平相对合理，存在上升空间
• 建议采用分批建仓策略，控制仓位风险
• 重点关注关键节点和催化剂事件

风险提示：
• 市场波动风险需要持续关注
• 政策环境变化可能带来不确定性
• 建议设置合理的止损和止盈点位

建议持仓周期：6-12个月，根据基本面变化调整策略。`,

  finalReport: `关于"${topic}"的完整投资研究报告

【研究摘要】
基于严格的事实核查验证、深度影响模拟分析以及专业论文级建议，本报告对${topic}进行了全面的投资价值评估。

【核心结论】
1. 事实核查：基础数据可靠，财务状况健康
2. 影响分析：市场前景积极，风险可控
3. 投资建议：具备投资价值，建议谨慎乐观

【风险提示】
请投资者充分理解市场风险，根据自身情况做出投资决策。

【报告完成时间】
${new Date().toLocaleString()}`
});
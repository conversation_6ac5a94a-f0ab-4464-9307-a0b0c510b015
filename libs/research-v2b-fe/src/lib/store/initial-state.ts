/**
 * Research V2 初始状态定义
 */
import { ResearchStage, StageStatus, ResearchV2State } from './types';

export const initialState: ResearchV2State = {
  currentStage: ResearchStage.IDLE,
  researchTopic: '',
  currentInput: '',
  
  // 任务信息
  taskId: undefined,
  runId: undefined,
  threadId: undefined,
  currentTaskId: null, // 添加这行
  
  // 各阶段状态
  factVerification: { status: StageStatus.IDLE },
  impactSimulation: { status: StageStatus.IDLE },
  thesisRecommendation: { status: StageStatus.IDLE },
  finalReport: { status: StageStatus.IDLE },
  
  // 用户反馈
  userFeedback: {},
  
  // 运行状态
  isRunning: false,
  totalProgress: 0,
  
  error: null,
};
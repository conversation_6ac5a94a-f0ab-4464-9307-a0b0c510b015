'use client';

import { useCallback } from 'react';
import { AGUIEvent } from './useAGUI';

// 导入共享类型
import {
  ResearchEventType,
  ResearchStage,
  ResearchStepStatus,
  ResearchStep,
  ResearchEvent,
  ResearchStartedEvent,
  StageStartedEvent,
  StageProgressEvent,
  StageCompletedEvent,
  ResearchCompletedEvent,
  ResearchFailedEvent,
  HeartbeatEvent,
  ErrorEvent,
  StepStartedEvent,    // 新增
  StepFinishedEvent,   // 新增
} from '../hooks/event';

// 研究自定义事件接口
export interface ResearchCustomEvent extends AGUIEvent {
  custom: {
    type: ResearchEventType;
    data: ResearchEvent;
  };
}

interface UseResearchEventsOptions {
  onResearchStarted?: (event: ResearchStartedEvent) => void;
  onStageStarted?: (event: StageStartedEvent) => void;
  onStageProgress?: (event: StageProgressEvent) => void;
  onStageCompleted?: (event: StageCompletedEvent) => void;
  onResearchCompleted?: (event: ResearchCompletedEvent) => void;
  onResearchFailed?: (event: ResearchFailedEvent) => void;
  onStepStarted?: (event: StepStartedEvent) => void;  // 新增
  onStepFinished?: (event: StepFinishedEvent) => void; // 新增
  onHeartbeat?: (event: HeartbeatEvent) => void;
  onError?: (event: ErrorEvent) => void;
}

export function useResearchEventHandler(options: UseResearchEventsOptions) {
  return useCallback((event: AGUIEvent) => {
    // 检查是否是研究相关事件
    console.log('收到AGUI事件:', JSON.stringify(event));
    if (event.type === 'CUSTOM' && 
        event.value &&                    // 检查 value 字段
        event.value.type &&               // 检查 value.type
        Object.values(ResearchEventType).includes(event.value.type as ResearchEventType)) {
      
      // 正确的类型断言
      const researchEventType = event.value.type as ResearchEventType;
      const eventData = event.value as ResearchEvent;  // 整个 value 就是 ResearchEvent
      
      console.log('收到研究AGUI事件 eventData:', JSON.stringify(eventData));
      // 根据事件类型分发处理
      switch (researchEventType) {
        case ResearchEventType.RESEARCH_STARTED:
          options.onResearchStarted?.(eventData as ResearchStartedEvent);
          break;

        case ResearchEventType.STAGE_STARTED:
          options.onStageStarted?.(eventData as StageStartedEvent);
          break;

        case ResearchEventType.STAGE_PROGRESS:
          options.onStageProgress?.(eventData as StageProgressEvent);
          break;

        case ResearchEventType.STAGE_COMPLETED:
          options.onStageCompleted?.(eventData as StageCompletedEvent);
          break;

        case ResearchEventType.RESEARCH_COMPLETED:
          options.onResearchCompleted?.(eventData as ResearchCompletedEvent);
          break;

        case ResearchEventType.RESEARCH_FAILED:
          options.onResearchFailed?.(eventData as ResearchFailedEvent);
          break;

        case ResearchEventType.HEARTBEAT:
          options.onHeartbeat?.(eventData as HeartbeatEvent);
          break;

        case ResearchEventType.ERROR:
          options.onError?.(eventData as ErrorEvent);
          break;

        // 新增的事件类型处理
        // 在 switch 语句中添加
        case ResearchEventType.STEP_STARTED:
          options.onStepStarted?.(eventData as StepStartedEvent);
          break;
        
        case ResearchEventType.STEP_FINISHED:
          options.onStepFinished?.(eventData as StepFinishedEvent);
          break;
        default:
          console.log('未处理的研究事件类型:', researchEventType, eventData);
      }
    }
  }, [options]);
}

// 导出类型供其他组件使用
export {
  ResearchEventType,
  ResearchStage,
  ResearchStepStatus,
  type ResearchStep,
  type ResearchEvent,
  type ResearchStartedEvent,
  type StageStartedEvent,
  type StageProgressEvent,
  type StageCompletedEvent,
  type ResearchCompletedEvent,
  type ResearchFailedEvent,
  type HeartbeatEvent,
  type ErrorEvent
};
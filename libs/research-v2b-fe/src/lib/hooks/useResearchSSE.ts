'use client';

import { useEffect, useCallback, useRef } from 'react';
import { useResearchV2Store } from '../store/store';

export interface ResearchSSEEvent {
  type: 'research_started' | 'research_progress' | 'research_completed' | 'research_failed' | 'heartbeat';
  data: any;
  timestamp: string;
}

export interface UseResearchSSEOptions {
  url?: string;
  onEvent?: (event: ResearchSSEEvent) => void;
  onError?: (error: Error) => void;
  autoConnect?: boolean;
}

export const useResearchSSE = (options: UseResearchSSEOptions = {}) => {
  const { 
    url = '/api/v1/research-v2/events',
    onEvent,
    onError,
    autoConnect = true 
  } = options;
  
  const eventSourceRef = useRef<EventSource | null>(null);
  const isConnectedRef = useRef(false);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 1000;
  
  const { startResearch: storeStartResearch } = useResearchV2Store();

  // 处理研究开始事件
  const handleResearchStarted = useCallback((event: ResearchSSEEvent) => {
    if (event.type === 'research_started' && event.data) {
      console.log('Research started:', event.data);
      onEvent?.(event);
    }
  }, [onEvent]);

  // 处理研究进度事件
  const handleResearchProgress = useCallback((event: ResearchSSEEvent) => {
    if (event.type === 'research_progress' && event.data) {
      console.log('Research progress:', event.data);
      onEvent?.(event);
    }
  }, [onEvent]);

  // 处理研究完成事件
  const handleResearchCompleted = useCallback((event: ResearchSSEEvent) => {
    if (event.type === 'research_completed' && event.data) {
      console.log('Research completed:', event.data);
      onEvent?.(event);
    }
  }, [onEvent]);

  // 处理研究失败事件
  const handleResearchFailed = useCallback((event: ResearchSSEEvent) => {
    if (event.type === 'research_failed' && event.data) {
      console.error('Research failed:', event.data);
      onEvent?.(event);
    }
  }, [onEvent]);

  // 处理心跳事件
  const handleHeartbeat = useCallback((event: ResearchSSEEvent) => {
    console.log('SSE heartbeat received:', event.timestamp);
  }, []);

  // 连接SSE
  const connect = useCallback(() => {
    if (isConnectedRef.current || eventSourceRef.current) {
      return;
    }

    try {
      eventSourceRef.current = new EventSource(url);
      
      eventSourceRef.current.onopen = () => {
        console.log('Research SSE connected');
        isConnectedRef.current = true;
        reconnectAttemptsRef.current = 0;
      };

      eventSourceRef.current.onmessage = (event) => {
        try {
          const sseEvent: ResearchSSEEvent = JSON.parse(event.data);
          
          // 根据事件类型分发处理
          switch (sseEvent.type) {
            case 'research_started':
              handleResearchStarted(sseEvent);
              break;
            case 'research_progress':
              handleResearchProgress(sseEvent);
              break;
            case 'research_completed':
              handleResearchCompleted(sseEvent);
              break;
            case 'research_failed':
              handleResearchFailed(sseEvent);
              break;
            case 'heartbeat':
              handleHeartbeat(sseEvent);
              break;
            default:
              console.warn('Unknown SSE event type:', sseEvent.type);
          }
        } catch (error) {
          console.error('Failed to parse SSE event:', error);
          onError?.(error as Error);
        }
      };

      eventSourceRef.current.onerror = () => {
        console.error('Research SSE connection error');
        isConnectedRef.current = false;
        handleReconnect();
      };

    } catch (error) {
      console.error('Failed to create SSE connection:', error);
      onError?.(error as Error);
    }
  }, [url, handleResearchStarted, handleResearchProgress, handleResearchCompleted, handleResearchFailed, handleHeartbeat, onError]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    isConnectedRef.current = false;
  }, []);

  // 处理重连
  const handleReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current < maxReconnectAttempts) {
      reconnectAttemptsRef.current++;
      setTimeout(() => {
        console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);
        disconnect();
        connect();
      }, reconnectDelay * reconnectAttemptsRef.current);
    } else {
      console.error('Max reconnection attempts reached');
      onError?.(new Error('SSE connection failed after maximum retry attempts'));
    }
  }, [connect, disconnect, onError]);

  // 自动连接
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // 页面可见性变化处理
    const handleVisibilityChange = () => {
      if (document.hidden) {
        disconnect();
      } else if (autoConnect) {
        connect();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      disconnect();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [autoConnect, connect, disconnect]);

  return {
    isConnected: isConnectedRef.current,
    connect,
    disconnect,
    reconnectAttempts: reconnectAttemptsRef.current
  };
};
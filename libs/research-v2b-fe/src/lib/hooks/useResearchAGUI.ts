'use client';

import { useCallback } from 'react';
import { useAGUI } from './useAGUI';
import { useResearchEventHandler } from './useResearchEvents';
import {
  ResearchStartedEvent,
  StageStartedEvent,
  StageProgressEvent,
  StageCompletedEvent,
  ResearchCompletedEvent,
  ResearchFailedEvent,
  StepStartedEvent,    // 添加这个导入
  StepFinishedEvent,   // 添加这个导入
  HeartbeatEvent,
  ErrorEvent,
  ResearchEventType
} from '../hooks/event';

import { useResearchV2Store } from '../store/store'; // 添加这个导入

interface UseResearchAGUIOptions {
  onResearchStarted?: (event: ResearchStartedEvent) => void;
  onStageStarted?: (event: StageStartedEvent) => void;
  onStageProgress?: (event: StageProgressEvent) => void;
  onStageCompleted?: (event: StageCompletedEvent) => void;
  onResearchCompleted?: (event: ResearchCompletedEvent) => void;
  onResearchFailed?: (event: ResearchFailedEvent) => void;
  onStepStarted?: (event: StepStartedEvent) => void;      // 新增
  onStepFinished?: (event: StepFinishedEvent) => void;   // 新增
  onHeartbeat?: (event: HeartbeatEvent) => void;
  onError?: (event: ErrorEvent) => void;
}

export function useResearchAGUI(options: UseResearchAGUIOptions) {
  const handleAGUIEvent = useResearchEventHandler(options);
  // 添加缺失的 store 方法
  const { getCurrentTaskId, setCurrentTaskId, getThreadId, setThreadId } = useResearchV2Store();

  const { connectionStatus, isRunning, sendMessage, agent } = useAGUI({
    url: "/api/research/chat",
    onEvent: handleAGUIEvent,
    onError: (error) => {
      console.error('研究AGUI连接错误:', error);
      options.onError?.({
        type: 'research_error',
        task_id: '',
        timestamp: new Date().toISOString(),
        data: {
          error_code: 'CONNECTION_ERROR',
          error_message: error.toString(),
          recoverable: true
        }
      } as ErrorEvent);
    },
    onComplete: () => {
      console.log('研究AGUI事件流完成');
    }
  });

  const startResearch = useCallback(async (topic: string, userFeedback?: string) => {
    if (!agent || isRunning) {
      console.warn('AGUI客户端未准备好或正在运行中');
      return;
    }

    let taskId: string;
    let threadId: string;
    
    if (userFeedback) {
      const lastTaskId = getCurrentTaskId();
      const lastThreadId = getThreadId(); // 现在已定义
      
      if (lastTaskId && lastThreadId) {
        taskId = lastTaskId;
        threadId = lastThreadId;
        console.log('恢复模式，使用现有 taskId 和 threadId:', { taskId, threadId });
      } else {
        console.error('恢复模式但缺少 taskId 或 threadId');
        return;
      }
    } else {
      taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      threadId = `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      setCurrentTaskId(taskId);
      setThreadId(threadId); // 现在已定义
      console.log('新任务模式，生成新的 taskId 和 threadId:', { taskId, threadId });
    }

    try {
      const message = {
        action: 'start_research',
        taskId,
        topic,
        user_feedback: userFeedback // 传递用户反馈
      };

      console.log('发送研究请求:', message);
      
      // 使用正确的 threadId 和 runId
      const unsubscribe = await sendMessage(message, threadId, `run_${taskId}`);
      return { taskId, threadId, unsubscribe };
    } catch (error) {
      console.error('启动研究失败:', error);
      options.onError?.({
        type: 'research_error',
        task_id: taskId,
        timestamp: new Date().toISOString(),
        data: {
          error_code: 'START_ERROR',
          error_message: `启动研究失败: ${error}`,
          recoverable: true
        }
      } as ErrorEvent);
      throw error;
    }
  }, [agent, isRunning, sendMessage, getCurrentTaskId, getThreadId, setCurrentTaskId, setThreadId, options]);

  return {
    connectionStatus,
    isRunning,
    startResearch,
    agent
  };
}
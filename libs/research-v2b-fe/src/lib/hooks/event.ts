// 图表数据接口
export interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'candlestick';
  title: string;
  data: unknown[];
  config?: Record<string, unknown>;
}

// API 响应接口
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

// 分页接口
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// SSE 事件接口
export interface SSEEvent {
  type: 'task_update' | 'task_created' | 'task_completed' | 'task_failed' | 'heartbeat';
  data: unknown;
  timestamp: string;
}

// 研究事件类型枚举
export enum ResearchEventType {
  // 研究生命周期
  RESEARCH_STARTED = 'research_started',
  RESEARCH_COMPLETED = 'research_completed',
  RESEARCH_FAILED = 'research_failed',
  
  // 阶段生命周期（与后端保持一致）
  STAGE_STARTED = 'stage_started',
  STAGE_PROGRESS = 'stage_progress', 
  STAGE_COMPLETED = 'stage_completed',
  
  // 步骤生命周期（核心）
  STEP_STARTED = 'step_started',
  STEP_FINISHED = 'step_finished',
  
  // 系统事件
  HEARTBEAT = 'heartbeat',
  ERROR = 'research_error'
}

// 研究阶段枚举
export enum ResearchStage {
  IDLE = 'idle',
  FACT_VERIFICATION = 'fact-verification',
  IMPACT_SIMULATION = 'impact-simulation',
  THESIS_RECOMMENDATION = 'thesis-recommendation',
  FINAL_REPORT = 'final-report',
  COMPLETED = 'completed'
}

// 研究步骤状态
export enum ResearchStepStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// 研究步骤接口
export interface ResearchStep {
  name: string;
  status: ResearchStepStatus;
  description?: string;
  started_at?: string;
  completed_at?: string;
  result?: unknown;
  error_message?: string;
}

// 基础研究事件接口
export interface BaseResearchEvent {
  type: ResearchEventType;
  task_id: string;
  timestamp: string;
  run_id?: string;
  thread_id?: string;
}

// 研究开始事件
export interface ResearchStartedEvent extends BaseResearchEvent {
  type: ResearchEventType.RESEARCH_STARTED;
  data: {
    topic: string;
    estimated_duration?: number;
    stages: ResearchStage[];
  };
}

// 阶段开始事件
export interface StageStartedEvent extends BaseResearchEvent {
  type: ResearchEventType.STAGE_STARTED;
  data: {
    stage: ResearchStage;
    stage_name: string;
    description: string;
    estimated_duration?: number;
    steps: ResearchStep[];
  };
}

// 阶段进度事件
export interface StageProgressEvent extends BaseResearchEvent {
  type: ResearchEventType.STAGE_PROGRESS;
  data: {
    stage: ResearchStage;
    progress: number;
    current_step?: string;
    current_action?: string;
    content?: string;
    steps?: ResearchStep[];
    partial_result?: unknown;
  };
}

// 阶段完成事件
export interface StageCompletedEvent extends BaseResearchEvent {
  type: ResearchEventType.STAGE_COMPLETED;
  data: {
    stage: ResearchStage;
    result: {
      summary: string;
      details: Record<string, unknown>;
      confidence_score?: number;
      key_findings?: string[];
      charts_data?: ChartData[];
    };
    duration: number;
    next_stage?: ResearchStage;
  };
}

// 研究完成事件
export interface ResearchCompletedEvent extends BaseResearchEvent {
  type: ResearchEventType.RESEARCH_COMPLETED;
  data: {
    final_report: {
      executive_summary: string;
      fact_verification_result: unknown;
      impact_simulation_result: unknown;
      thesis_recommendation_result: unknown;
      final_report_content: string;
      confidence_score: number;
      recommendations: string[];
      risk_factors: string[];
      charts_data?: ChartData[];
    };
    total_duration: number;
    completed_at: string;
  };
}

// 研究失败事件
export interface ResearchFailedEvent extends BaseResearchEvent {
  type: ResearchEventType.RESEARCH_FAILED;
  data: {
    error_code: string;
    error_message: string;
    failed_stage?: ResearchStage;
    failed_step?: string;
    retry_possible: boolean;
    partial_results?: Record<ResearchStage, unknown>;
  };
}

// 心跳事件
export interface HeartbeatEvent extends BaseResearchEvent {
  type: ResearchEventType.HEARTBEAT;
  data: {
    status: 'alive';
    current_stage?: ResearchStage;
    progress?: number;
  };
}

// 错误事件
export interface ErrorEvent extends BaseResearchEvent {
  type: ResearchEventType.ERROR;
  data: {
    error_code: string;
    error_message: string;
    error_details?: unknown;
    recoverable: boolean;
  };
}

// 联合类型
export type ResearchEvent =
  | ResearchStartedEvent
  | StageStartedEvent
  | StageProgressEvent
  | StageCompletedEvent
  | ResearchCompletedEvent
  | ResearchFailedEvent
  | StepStartedEvent
  | StepFinishedEvent
  | HeartbeatEvent
  | ErrorEvent;

// 步骤开始事件
export interface StepStartedEvent extends BaseResearchEvent {
  type: ResearchEventType.STEP_STARTED;
  data: {
    step_name: string;
    description: string;
    stage: ResearchStage;
  };
}

// 步骤完成事件
export interface StepFinishedEvent extends BaseResearchEvent {
  type: ResearchEventType.STEP_FINISHED;
  data: {
    step_name: string;
    description: string;
    result_summary?: string;
    error?: string;
    stage: ResearchStage;
    success: boolean;
  };
}


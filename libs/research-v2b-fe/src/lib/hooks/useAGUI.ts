import { useRef, useEffect, useState } from 'react';
import { HttpAgent } from '@ag-ui/client';

export interface AGUIEvent {
  type: string;
  messageId?: string;
  threadId?: string;
  runId?: string;
  delta?: string;
  content?: string;
  data?: any;
  rawEvent?: any;
  custom?: any;
  state?: any;
  tool_call_id?: string;
  tool_call_name?: string;
  step_name?: string;
  error?: string;
  value?: any;
}

export interface UseAGUIOptions {
  url: string;
  onEvent?: (event: AGUIEvent) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
}

export function useAGUI(options: UseAGUIOptions) {
  const agentRef = useRef<HttpAgent | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error'>('connecting');
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    try {
      agentRef.current = new HttpAgent({
        url: options.url
      });
      setConnectionStatus('connected');
      console.log('AG-UI HttpAgent 初始化成功');
    } catch (error) {
      console.error('AG-UI 客户端初始化失败:', error);
      setConnectionStatus('error');
      options.onError?.(error as Error);
    }
  }, [options.url]);

  const sendMessage = async (
      message: any,
      threadId?: string,
      runId?: string
  ) => {
    if (!agentRef.current || isRunning) {
      console.warn('AG-UI 客户端未准备好或正在运行中');
      return;
    }
  
    const finalThreadId = threadId || `thread_${Date.now()}`;
    const finalRunId = runId || `run_${Date.now()}`;
  
    setIsRunning(true);
  
    try {
      const request = {
        threadId: finalThreadId,
        runId: finalRunId,
        messages: [
          {
            id: `msg_${Date.now()}`,
            role: "user" as const,
            content: typeof message === 'string' ? message : message.topic || JSON.stringify(message)
          }
        ],
        user_feedback: typeof message === 'object' ? message.user_feedback : undefined,
        tools: [],
        context: [],
        state: {},
        forwardedProps: {}
      };
  
      console.log('发送 AG-UI 消息:', { message, request });
  
      const stream = agentRef.current.run(request);
  
      const subscription = stream.subscribe({
        next: (event: any) => {
          console.log('收到 AG-UI 事件:', event);
          options.onEvent?.(event as AGUIEvent);
        },
        error: (error: any) => {
          console.error('AG-UI 事件流错误:', error);
          setIsRunning(false);
          options.onError?.(error);
        },
        complete: () => {
          console.log('AG-UI 事件流完成');
          setIsRunning(false);
          options.onComplete?.();
        }
      });
  
      return () => {
        subscription?.unsubscribe?.();
      };
  
    } catch (error) {
      console.error('发送消息失败:', error);
      setIsRunning(false);
      options.onError?.(error as Error);
      return;
    }
  };

  return {
    connectionStatus,
    isRunning,
    sendMessage,
    agent: agentRef.current
  };
}
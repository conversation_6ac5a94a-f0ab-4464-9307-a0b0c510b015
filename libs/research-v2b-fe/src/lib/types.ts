/**
 * Research V2 前端类型定义
 */

export interface ResearchRequest {
  query: string;
  analystType?: string;
  config?: Record<string, any>;
}

export interface ResearchResult {
  taskId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  result?: Record<string, any>;
  createdAt: string;
  completedAt?: string;
}

export interface AnalystTemplate {
  name: string;
  description: string;
  methodology: Record<string, any>;
  config?: Record<string, any>;
}

export interface ResearchEvent {
  type: string;
  data: Record<string, any>;
  timestamp: string;
}

export interface ResearchV2PageProps {
  className?: string;
}
# Research V2 Frontend Plugin

## 概述

插件化的研究演示 V2 前端组件库，提供灵活的研究分析界面。

## 架构

```
src/lib/
├── components/       # 可复用组件
├── hooks/           # 自定义React Hooks
├── pages/           # 页面组件
└── types.ts         # 类型定义
```

## 使用方式

```tsx
import { ResearchV2Page } from 'research-v2-fe';

// 在页面中使用
<ResearchV2Page />
```

## 开发说明

- 所有组件保持留白状态，便于后续定制开发
- 遵循现有的React组件设计模式
- 支持主题定制和样式扩展
- 提供完整的TypeScript类型支持
{"name": "research-v2b-fe", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/research-v2b-fe/src", "projectType": "library", "tags": ["scope:shared", "type:ui"], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit", "cwd": "libs/research-v2b-fe"}}, "build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/research-v2b-fe", "main": "libs/research-v2b-fe/src/index.ts", "tsConfig": "libs/research-v2b-fe/tsconfig.lib.json", "assets": ["libs/research-v2b-fe/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/research-v2b-fe/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/research-v2b-fe/jest.config.ts"}}}}
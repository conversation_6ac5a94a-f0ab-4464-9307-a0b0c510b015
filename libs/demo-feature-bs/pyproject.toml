[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "demo-feature-bs"
version = "0.1.0"
description = "Demo feature backend service for YAI Investor Insight"
authors = [
    {name = "YAI Team", email = "<EMAIL>"},
]
dependencies = [
    "fastapi>=0.104.0",
    "pydantic>=2.0.0",
]
requires-python = ">=3.11"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "ruff>=0.1.0",
    "mypy>=1.6.0",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.ruff]
target-version = "py311"
line-length = 88
select = ["E", "W", "F", "I", "N", "B", "UP"]
ignore = ["E501", "B008"]

[tool.ruff.isort]
known-first-party = ["demo_feature_bs", "shared_bs_core","research_v2_bs"]

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
'use client';

import React from 'react';

/**
 * 用户会话信息类型
 */
export interface UserSession {
  userId: string;
  phone: string;
  userName?: string;
}

/**
 * 用户状态卡片组件属性
 */
export interface UserStatusCardProps {
  isLoggedIn: boolean;
  session?: UserSession;
}

/**
 * 用户状态卡片组件
 */
export function UserStatusCard({ isLoggedIn, session }: UserStatusCardProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6 mb-8">
      <h2 className="text-xl font-semibold mb-4">当前用户状态</h2>
      
      {isLoggedIn ? (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <span className="block text-sm text-gray-500">登录状态</span>
            <span className="text-green-600 font-medium">✅ 已登录</span>
          </div>
          <div className="text-center">
            <span className="block text-sm text-gray-500">用户ID</span>
            <span className="text-blue-600 font-medium">{session?.userId}</span>
          </div>
          <div className="text-center">
            <span className="block text-sm text-gray-500">手机号</span>
            <span className="text-gray-600 font-medium">{session?.phone}</span>
          </div>
          <div className="text-center">
            <span className="block text-sm text-gray-500">用户名</span>
            <span className="text-gray-600 font-medium">{session?.userName || '未设置'}</span>
          </div>
        </div>
      ) : (
        <div className="text-center py-4">
          <span className="text-red-600 text-lg">❌ 未登录</span>
          <p className="text-sm text-gray-500 mt-2">
            请先到 <a href="/login" className="text-blue-600 hover:underline">登录页面</a> 登录
          </p>
        </div>
      )}
    </div>
  );
}
'use client';

import React from 'react';
import { TestScenarioCard } from './TestScenarioCard';
import { TestResult } from './TestResult';

/**
 * 用户详情测试卡片组件属性
 */
export interface UserDetailTestCardProps {
  onTest: () => void;
  isLoading: boolean;
  result: string;
  isLoggedIn: boolean;
}

/**
 * 用户详情测试卡片组件
 */
export function UserDetailTestCard({ 
  onTest, 
  isLoading, 
  result, 
  isLoggedIn 
}: UserDetailTestCardProps) {
  return (
    <TestScenarioCard
      title="👤 测试场景3: 获取个人信息"
      description="测试获取用户个人详细信息功能"
      endpoint="POST /account/user/detail"
      isPublic={false}
      color="purple"
    >
      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
        <p className="text-sm text-yellow-800">
          ⚠️ 此接口需要用户登录状态，请先完成登录
        </p>
      </div>
      
      <button
        onClick={onTest}
        disabled={isLoading || !isLoggedIn}
        className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        {isLoading ? '获取中...' : '获取个人信息'}
      </button>
      
      <TestResult result={result} />
    </TestScenarioCard>
  );
}
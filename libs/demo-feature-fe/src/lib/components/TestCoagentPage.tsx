'use client';

import React from 'react';
import { UserStatusCard, type UserSession } from './UserStatusCard';
import { SendOtpTestCard } from './SendOtpTestCard';
import { MobileLoginTestCard } from './MobileLoginTestCard';
import { UserDetailTestCard } from './UserDetailTestCard';
import { CoagentTestCard } from './CoagentTestCard';
import { TechnicalDescription } from './TechnicalDescription';
import { useTestScenarios } from '../hooks/useTestScenarios';

/**
 * 测试页面组件属性
 */
export interface TestCoagentPageProps {
  isLoading: boolean;
  isLoggedIn: boolean;
  session?: UserSession;
}

/**
 * 加载状态组件
 */
function LoadingState({ isLoading, initError }: { isLoading: boolean; initError: string }) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">
          {isLoading ? '加载用户信息中...' : '初始化 Coagent 中...'}
        </p>
        {initError && (
          <p className="mt-2 text-red-600 text-sm">初始化错误: {initError}</p>
        )}
      </div>
    </div>
  );
}

/**
 * 测试 Coagent 页面主组件
 */
export function TestCoagentPage({
  isLoading,
  isLoggedIn,
  session
}: TestCoagentPageProps) {
  // 移除 coagent 初始化，直接使用 Server Actions
  const coagentInitialized = true;
  const initError = '';
  const {
    // 表单状态
    mobile,
    setMobile,
    code,
    setCode,
    
    // 测试结果
    sendOtpResult,
    loginResult,
    userDetailResult,
    testResult,
    
    // 加载状态
    isSendOtpLoading,
    isLoginLoading,
    isUserDetailLoading,
    isTestLoading,
    
    // 测试函数
    testSendOtpCode,
    testAuthWithMobile,
    testUserDetail,
    testCoagentAuth
  } = useTestScenarios();

  if (isLoading || !coagentInitialized) {
    return <LoadingState isLoading={isLoading} initError={initError} />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Account API 测试页面
          </h1>
          <p className="text-gray-600">
            测试发送验证码和手机号验证码登录功能
          </p>
        </div>

        {/* 用户状态 */}
        <UserStatusCard isLoggedIn={isLoggedIn} session={session} />

        {/* 测试场景区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 测试场景1: 发送验证码 */}
          <SendOtpTestCard
            mobile={mobile}
            setMobile={setMobile}
            onTest={testSendOtpCode}
            isLoading={isSendOtpLoading}
            result={sendOtpResult}
          />

          {/* 测试场景2: 手机号验证码登录 */}
          <MobileLoginTestCard
            mobile={mobile}
            setMobile={setMobile}
            code={code}
            setCode={setCode}
            onTest={testAuthWithMobile}
            isLoading={isLoginLoading}
            result={loginResult}
          />

          {/* 测试场景3: 获取个人信息 */}
          <UserDetailTestCard
            onTest={testUserDetail}
            isLoading={isUserDetailLoading}
            result={userDetailResult}
            isLoggedIn={isLoggedIn}
          />
        </div>

        {/* 技术说明 */}
        <TechnicalDescription />
        
        {/* Coagent 功能测试 */}
        <CoagentTestCard
          mobile={mobile}
          code={code}
          onTest={testCoagentAuth}
          isLoading={isTestLoading}
          result={testResult}
        />
      </div>
    </div>
  );
}
'use client';

import React from 'react';
import { TestResult } from './TestResult';

/**
 * Coagent功能测试卡片组件属性
 */
export interface CoagentTestCardProps {
  mobile: string;
  code: string;
  onTest: () => void;
  isLoading: boolean;
  result: string;
}

/**
 * Coagent功能测试组件
 */
export function CoagentTestCard({ 
  mobile, 
  code, 
  onTest, 
  isLoading, 
  result 
}: CoagentTestCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        🤖 测试场景4: Coagent 功能测试
      </h2>
      <p className="text-gray-600 mb-4">
        测试 coagent 自动注入 lucas-uniq-userid 请求头的功能
      </p>
      
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <button
            onClick={onTest}
            disabled={isLoading || !mobile || !code}
            className="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '测试中...' : '测试 Coagent Mutation (POST)'}
          </button>
        </div>
        
        <TestResult result={result} />
        
        <div className="mt-6 p-4 bg-blue-50 rounded-md">
          <h3 className="font-medium text-blue-900 mb-2">🔍 技术说明</h3>
          <div className="text-sm text-blue-800 space-y-2">
            <p><strong>Coagent 认证机制:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>自动解析 cookies 获取用户身份信息</li>
              <li>自动注入 lucas-uniq-userid 请求头</li>
              <li>提供统一的认证和日志记录</li>
              <li>支持自动错误处理和重试机制</li>
            </ul>
            <p className="mt-3"><strong>📋 接口分类说明:</strong></p>
            <div className="ml-4 space-y-2">
              <div>
                <p className="font-medium">公开接口 (不注入用户身份信息):</p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>/account/login/authWithMobile - 手机号验证码登录</li>
                  <li>/account/login/sendSms - 发送短信验证码</li>
                  <li>/account/login/sendOtpCode - 发送OTP验证码</li>
                </ul>
              </div>
              <div>
                <p className="font-medium">私有接口 (自动注入用户身份信息):</p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>POST /account/user/detail - 获取个人详细信息</li>
                  <li>GET /account/user/profile - 获取用户档案</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
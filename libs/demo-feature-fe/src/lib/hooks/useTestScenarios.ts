'use client';

import { useState } from 'react';
import { sendOtpCode, loginWithMobile, getUserProfile } from '@yai-investor-insight/user-account-fe';
import { useCoagentMutation } from '@yai-investor-insight/shared-fe-core';

/**
 * 测试场景的状态管理和业务逻辑
 */
export function useTestScenarios() {
  // 表单状态
  const [mobile, setMobile] = useState<string>('***********');
  const [code, setCode] = useState<string>('123456');
  
  // 测试结果状态
  const [sendOtpResult, setSendOtpResult] = useState<string>('');
  const [loginResult, setLoginResult] = useState<string>('');
  const [userDetailResult, setUserDetailResult] = useState<string>('');
  const [testResult, setTestResult] = useState<string>('');
  
  // 加载状态
  const [isSendOtpLoading, setIsSendOtpLoading] = useState(false);
  const [isLoginLoading, setIsLoginLoading] = useState(false);
  const [isUserDetailLoading, setIsUserDetailLoading] = useState(false);
  const [isTestLoading, setIsTestLoading] = useState(false);
  
  // Coagent hooks
  const coagentMutation = useCoagentMutation<any, { mobile: string; code: string; productLine: string }>({
    endpoint: '/account/login/authWithMobile',
    method: 'POST'
  });

  // 测试发送验证码接口
  const testSendOtpCode = async () => {
    if (!mobile) {
      setSendOtpResult('❌ 请输入手机号');
      return;
    }
    
    setIsSendOtpLoading(true);
    setSendOtpResult('');
    
    try {
      await sendOtpCode(mobile);
      
      setSendOtpResult(`✅ 发送验证码成功！\n手机号: ${mobile}\n时间: ${new Date().toLocaleString()}`);
    } catch (error: any) {
      setSendOtpResult(`❌ 发送验证码失败\n错误信息: ${error.message}`);
    } finally {
      setIsSendOtpLoading(false);
    }
  };

  // 测试手机号验证码登录接口
  const testAuthWithMobile = async () => {
    if (!mobile || !code) {
      setLoginResult('❌ 请输入手机号和验证码');
      return;
    }
    
    setIsLoginLoading(true);
    setLoginResult('');
    
    try {
      const response = await loginWithMobile(mobile, code);
      
      setLoginResult(`✅ 手机号验证码登录成功！\n用户数据: ${JSON.stringify(response.userData, null, 2)}\nToken: ${response.token}`);
    } catch (error: any) {
      setLoginResult(`❌ 手机号验证码登录失败\n错误信息: ${error.message}`);
    } finally {
      setIsLoginLoading(false);
    }
  };

  // 测试获取个人信息接口
  const testUserDetail = async () => {
    setIsUserDetailLoading(true);
    setUserDetailResult('');
    
    try {
      const userData = await getUserProfile();
      
      setUserDetailResult(`✅ 获取个人信息成功！\n用户数据: ${JSON.stringify(userData, null, 2)}`);
    } catch (error: any) {
      setUserDetailResult(`❌ 获取个人信息失败\n错误信息: ${error.message}`);
    } finally {
      setIsUserDetailLoading(false);
    }
  };

  // Coagent 测试函数
  const testCoagentAuth = async () => {
    if (!mobile || !code) {
      setTestResult('❌ 请输入手机号和验证码');
      return;
    }
    
    setIsTestLoading(true);
    setTestResult('');
    
    try {
      const response = await coagentMutation.mutateAsync({
        mobile,
        code,
        productLine: 'investor_insight'
      });
      
      setTestResult(`✅ Coagent Mutation 调用成功！\n响应数据: ${JSON.stringify(response, null, 2)}`);
    } catch (error: any) {
      setTestResult(`❌ Coagent Mutation 调用失败\n错误信息: ${error.message}`);
    } finally {
      setIsTestLoading(false);
    }
  };

  return {
    // 表单状态
    mobile,
    setMobile,
    code,
    setCode,
    
    // 测试结果
    sendOtpResult,
    loginResult,
    userDetailResult,
    testResult,
    
    // 加载状态
    isSendOtpLoading,
    isLoginLoading,
    isUserDetailLoading,
    isTestLoading,
    
    // 测试函数
    testSendOtpCode,
    testAuthWithMobile,
    testUserDetail,
    testCoagentAuth
  };
}
// Pages
export { DemoPage } from './lib/pages/demo-page';
export { LoggingDemoPage } from './lib/pages/logging-demo-page';

// Components  
export { MarketOverview } from './lib/components/market-overview';

// Test Coagent Components
export { TestCoagentPage } from './lib/components/TestCoagentPage';
export type { TestCoagentPageProps } from './lib/components/TestCoagentPage';

export { UserStatusCard } from './lib/components/UserStatusCard';
export type { UserStatusCardProps, UserSession } from './lib/components/UserStatusCard';

export { TestScenarioCard } from './lib/components/TestScenarioCard';
export type { TestScenarioCardProps } from './lib/components/TestScenarioCard';

export { TestResult } from './lib/components/TestResult';
export type { TestResultProps } from './lib/components/TestResult';

export { SendOtpTestCard } from './lib/components/SendOtpTestCard';
export type { SendOtpTestCardProps } from './lib/components/SendOtpTestCard';

export { MobileLoginTestCard } from './lib/components/MobileLoginTestCard';
export type { MobileLoginTestCardProps } from './lib/components/MobileLoginTestCard';

export { UserDetailTestCard } from './lib/components/UserDetailTestCard';
export type { UserDetailTestCardProps } from './lib/components/UserDetailTestCard';

export { CoagentTestCard } from './lib/components/CoagentTestCard';
export type { CoagentTestCardProps } from './lib/components/CoagentTestCard';

export { TechnicalDescription } from './lib/components/TechnicalDescription';

// Hooks
export { useTestScenarios } from './lib/hooks/useTestScenarios';

// 配置 - 已移除Coagent相关配置，专注于核心功能

// Actions
export { getMarketDataAction, getMarketDataBySymbolAction } from './lib/actions/marketDataActions';

// Types
export type { MarketData } from './lib/types';

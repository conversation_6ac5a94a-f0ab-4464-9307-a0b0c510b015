# demo-feature-fe

这是一个演示功能的前端组件库，现在已经整合了 test-coagent-fe 的所有功能。

## 功能特性

### 🎯 演示功能
- **市场数据展示**: 实时市场数据概览
- **响应式布局**: 支持多种屏幕尺寸
- **模块化架构**: 展示插件式架构的能力

### 🧪 Coagent 测试功能
- **发送验证码测试**: 测试 `/account/login/sendOtpCode` 接口
- **手机号验证码登录测试**: 测试 `/account/login/authWithMobile` 接口
- **获取个人信息测试**: 测试 `POST /account/user/detail` 接口
- **Coagent 功能测试**: 测试 Coagent 自动注入用户身份信息的功能

### 🎨 UI 组件
- **用户状态卡片**: 显示当前用户登录状态和基本信息
- **测试场景卡片**: 统一的测试场景 UI 容器
- **测试结果显示**: 格式化显示测试结果
- **技术说明**: Coagent 认证流程和接口分类说明

## 基本用法

### 演示页面
```typescript
import { DemoPage } from '@yai-investor-insight/demo-feature-fe';

export default function Demo() {
  return <DemoPage />;
}
```

### Coagent 测试页面
```typescript
import { TestCoagentPage } from '@yai-investor-insight/demo-feature-fe';
import { useSession } from '@/hooks/useSession';
import { getCoagentAuthConfig } from '@/lib/coagent-auth';

export default function TestPage() {
  const { session, isLoading, isLoggedIn } = useSession();

  return (
    <TestCoagentPage
      isLoading={isLoading}
      isLoggedIn={isLoggedIn}
      session={session}
      getCoagentAuthConfig={getCoagentAuthConfig}
    />
  );
}
```

## 依赖关系

- `@yai-investor-insight/shared-fe-core`: Coagent 核心功能
- `@yai-investor-insight/user-account-fe`: 用户账户相关功能
- `@yai-investor-insight/shared-fe-kit`: 通用 UI 组件
- `@tanstack/react-query`: 数据请求管理
- `react`: React 框架

## 运行测试

Run `nx test demo-feature-fe` to execute the unit tests via [Jest](https://jestjs.io).

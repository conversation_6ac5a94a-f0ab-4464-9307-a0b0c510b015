{"name": "shared-bs-llm", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/shared-bs-llm/src", "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "cd libs/shared-bs-llm && pip install -e ."}}, "test": {"executor": "nx:run-commands", "options": {"command": "cd libs/shared-bs-llm && pytest"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "cd libs/shared-bs-llm && python -m flake8 src/"}}, "format": {"executor": "nx:run-commands", "options": {"command": "cd libs/shared-bs-llm && python -m black src/"}}}, "tags": ["type:library", "scope:shared", "platform:python"]}
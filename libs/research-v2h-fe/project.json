{"name": "research-v2h-fe", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/research-v2h-fe/src", "projectType": "library", "tags": ["scope:shared", "type:ui"], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit", "cwd": "libs/research-v2h-fe"}}, "build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "web", "outputPath": "dist/libs/research-v2h-fe", "main": "libs/research-v2h-fe/src/index.ts", "tsConfig": "libs/research-v2h-fe/tsconfig.lib.json", "webpackConfig": "libs/research-v2h-fe/webpack.config.cjs"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/research-v2h-fe/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/research-v2h-fe/jest.config.ts"}}}}
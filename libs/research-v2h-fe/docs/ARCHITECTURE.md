# Research V2H Frontend Architecture

## Server/Client Components 边界设计

### Next.js 15 + React 19 架构优化

基于 Next.js App Router 的 Server/Client Components 边界优化，实现最佳性能和用户体验。

## 组件分层架构

### 🔹 Server Components（服务端渲染）
**优势**: 减少客户端 JavaScript 包大小，提升首屏加载性能

#### 页面层
- `ResearchV2Page` - 主页面组件，纯展示布局
- `research-v2h/page.tsx` - Next.js 页面路由

#### 展示组件层
- `IdleStage` - 空闲状态展示
- `StageIcon` - 阶段图标
- `ProgressRing` - 进度环
- `StageStatusIndicator` - 状态指示器
- `StageHeader` - 阶段标题
- `StageContent` - 内容容器
- `StageActions` - 操作容器
- `StageContainer` - 基础容器

### 🔸 Client Components（客户端交互）
**特征**: 包含状态管理、用户交互、浏览器 API

#### 容器层（边界组件）
- `ResearchContainer` - **主要边界**，管理状态和服务初始化
- `ResearchSidebar` - 侧边栏交互

#### 交互组件层
- `FactVerificationStage` - 事实验证阶段（交互版）
- `ImpactSimulationStage` - 影响模拟阶段（交互版）
- `ThesisRecommendationStage` - 投资建议阶段（交互版）
- `FinalReportStage` - 最终报告阶段（交互版）
- `ViewModeIndicator` - 查看模式指示器（交互）
- `StageDrawer` - 执行步骤抽屉（交互）

#### 服务层
- `ResearchService` - **客户端专用**，处理流式数据
- `useResearchV2Store` - Zustand 状态管理

## 关键设计决策

### 1. 懒初始化模式
```typescript
// ❌ 原问题：服务端实例化客户端服务
const researchService = new ResearchService(); // 在 Store 创建时执行

// ✅ 解决方案：延迟到客户端运行时
initializeService: () => {
  if (typeof window !== 'undefined' && !state.researchService) {
    const researchService = new ResearchService();
    set({ researchService });
  }
}
```

### 2. 边界组件策略
```typescript
// ResearchContainer 作为主要 Client/Server 边界
'use client';

export function ResearchContainer() {
  // 在组件挂载时初始化服务
  useEffect(() => {
    initializeService();
  }, [initializeService]);
  
  // 渲染 Server Components 和 Client Components
}
```

### 3. 双模式组件设计
```typescript
// Stage 组件支持执行和历史查看模式
interface BaseStageProps {
  mode?: 'execution' | 'history'; // 决定组件行为
}

// 在 ResearchContainer 中根据状态切换模式
const componentMode = viewMode === 'history' ? 'history' : 'execution';
```

## 性能优化效果

### 📈 客户端 JavaScript 减少
- 纯展示组件转为 Server Components
- 减少不必要的客户端水合
- 提升首屏加载速度

### ⚡ 服务端渲染优化
- 静态内容在服务端预渲染
- 交互功能按需客户端激活
- 更好的 SEO 和 Core Web Vitals

### 🎯 边界清晰化
- `ResearchContainer` 作为唯一的状态边界
- 服务初始化集中管理
- 组件职责明确分离

## 最佳实践

1. **组件设计原则**
   - 纯展示 → Server Component
   - 有状态/交互 → Client Component
   - 在边界处理初始化

2. **状态管理策略**
   - Store 支持懒初始化
   - 服务实例在客户端创建
   - 状态同步在边界组件处理

3. **性能监控**
   - 监控客户端包大小
   - 测量首屏渲染时间
   - 验证 Server/Client 边界效果
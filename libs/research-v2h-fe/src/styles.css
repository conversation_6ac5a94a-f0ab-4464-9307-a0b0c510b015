/* @import "tailwindcss"; */

/* Research V2H Frontend Library Styles */
/* 针对 research-v2h-fe 库的 Tailwind CSS v4 配置 */

@theme inline {
  /* 继承主应用的主题配置 */
  --color-background: var(--color-background, #ffffff);
  --color-foreground: var(--color-foreground, #171717);
  
  /* 研究界面专用颜色扩展 */
  --color-research-primary: #3b82f6;
  --color-research-secondary: #6366f1;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  
  /* 阶段状态颜色 */
  --color-stage-idle: #6b7280;
  --color-stage-running: #3b82f6;
  --color-stage-completed: #10b981;
  --color-stage-error: #ef4444;
}

/* 自定义动画 */
@keyframes research-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes research-slide-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义动画类 */
.research-pulse {
  animation: research-pulse 2s ease-in-out infinite;
}

.research-slide-in {
  animation: research-slide-in 0.3s ease-out;
}

/* 研究界面专用样式 - Use Tailwind classes directly in components */
/*
.research-container {
  Classes: min-h-screen bg-gray-50
}

.research-card {
  Classes: bg-white rounded-xl shadow-sm border border-gray-100 p-6
}

.research-stage-header {
  Classes: flex items-center space-x-3 mb-4
}

.research-status-running {
  Classes: text-blue-600 bg-blue-50
}

.research-status-completed {
  Classes: text-green-600 bg-green-50
}

.research-status-error {
  Classes: text-red-600 bg-red-50
}
*/
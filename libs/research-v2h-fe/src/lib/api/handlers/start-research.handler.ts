/**
 * Start Research API Handler
 * 核心业务逻辑：处理研究启动的流式响应
 */

import { startResearchAction } from '../../actions';
import { StartResearchRequest, ApiErrorResponse } from '../types/api.types';

/**
 * 启动研究的 API Handler
 * 在服务端调用 startResearchAction 并流式传输事件到客户端
 */
export async function startResearchHandler(request: Request): Promise<Response> {
  try {
    // 解析请求参数
    const body: StartResearchRequest = await request.json();
    const { topic } = body;

    if (!topic || typeof topic !== 'string' || topic.trim().length === 0) {
      const errorResponse: ApiErrorResponse = {
        error: 'Topic is required and must be a non-empty string',
        details: 'Please provide a valid research topic'
      };
      return new Response(JSON.stringify(errorResponse), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 在服务端调用 async generator
    const streamGenerator = startResearchAction({ topic: topic.trim() });

    // 创建可读流 (ReadableStream) 用于流式传输
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();

        try {
          // 遍历生成器产生的事件
          for await (const event of streamGenerator) {
            // 将每个事件序列化为 JSON
            const chunk = JSON.stringify(event);
            
            // 使用 Server-Sent Events (SSE) 格式
            const sseData = `data: ${chunk}\n\n`;
            
            // 将数据编码并推入流中
            controller.enqueue(encoder.encode(sseData));
          }
          
          // 流结束
          controller.close();
        } catch (generatorError) {
          console.error('Error in start research generator:', generatorError);
          
          // 发送错误事件
          const errorEvent = {
            type: 'error_occurred',
            sessionId: 'unknown',
            timestamp: new Date().toISOString(),
            error: generatorError instanceof Error ? generatorError.message : 'Generator execution failed'
          };
          
          const errorChunk = `data: ${JSON.stringify(errorEvent)}\n\n`;
          controller.enqueue(encoder.encode(errorChunk));
          controller.close();
        }
      },
    });

    // 返回流式响应
    return new Response(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (requestError) {
    console.error('Error in start research API handler:', requestError);
    
    const errorMessage = requestError instanceof Error ? requestError.message : 'An unknown error occurred';
    const errorResponse: ApiErrorResponse = {
      error: errorMessage,
      details: 'Failed to process start research request'
    };

    return new Response(JSON.stringify(errorResponse), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
/**
 * Complete Stage API Handler
 * 核心业务逻辑：处理阶段完成和用户反馈的流式响应
 */

import { completeStageAction } from '../../actions';
import { CompleteStageRequest, ApiErrorResponse } from '../types/api.types';
import { ResearchStageType } from '../../domain/entities';

/**
 * 完成阶段的 API Handler
 * 在服务端调用 completeStageAction 并流式传输后续阶段执行事件到客户端
 */
export async function completeStageHandler(request: Request): Promise<Response> {
  try {
    // 解析请求参数
    const body: CompleteStageRequest = await request.json();
    const { sessionId, stageType, userFeedback } = body;

    // 参数验证
    if (!sessionId || typeof sessionId !== 'string' || sessionId.trim().length === 0) {
      const errorResponse: ApiErrorResponse = {
        error: 'SessionId is required and must be a non-empty string',
        details: 'Please provide a valid session ID'
      };
      return new Response(JSON.stringify(errorResponse), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!stageType || !isValidStageType(stageType)) {
      const errorResponse: ApiErrorResponse = {
        error: 'StageType is required and must be a valid research stage type',
        details: 'Valid stage types: FACT_VERIFICATION, IMPACT_SIMULATION, THESIS_RECOMMENDATION, FINAL_REPORT'
      };
      return new Response(JSON.stringify(errorResponse), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 在服务端调用 async generator
    const streamGenerator = completeStageAction({ 
      sessionId: sessionId.trim(), 
      stageType,
      userFeedback: userFeedback?.trim() || undefined
    });

    // 创建可读流 (ReadableStream) 用于流式传输
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();

        try {
          // 遍历生成器产生的事件
          for await (const event of streamGenerator) {
            // 将每个事件序列化为 JSON
            const chunk = JSON.stringify(event);
            
            // 使用 Server-Sent Events (SSE) 格式
            const sseData = `data: ${chunk}\n\n`;
            
            // 将数据编码并推入流中
            controller.enqueue(encoder.encode(sseData));
          }
          
          // 流结束
          controller.close();
        } catch (generatorError) {
          console.error('Error in complete stage generator:', generatorError);
          
          // 发送错误事件
          const errorEvent = {
            type: 'error_occurred',
            sessionId,
            timestamp: new Date().toISOString(),
            stageType,
            error: generatorError instanceof Error ? generatorError.message : 'Generator execution failed'
          };
          
          const errorChunk = `data: ${JSON.stringify(errorEvent)}\n\n`;
          controller.enqueue(encoder.encode(errorChunk));
          controller.close();
        }
      },
    });

    // 返回流式响应
    return new Response(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (requestError) {
    console.error('Error in complete stage API handler:', requestError);
    
    const errorMessage = requestError instanceof Error ? requestError.message : 'An unknown error occurred';
    const errorResponse: ApiErrorResponse = {
      error: errorMessage,
      details: 'Failed to process complete stage request'
    };

    return new Response(JSON.stringify(errorResponse), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 验证阶段类型是否有效
 */
function isValidStageType(stageType: any): stageType is ResearchStageType {
  const validStageTypes: ResearchStageType[] = [
    'FACT_VERIFICATION',
    'IMPACT_SIMULATION', 
    'THESIS_RECOMMENDATION',
    'FINAL_REPORT'
  ];
  
  return typeof stageType === 'string' && validStageTypes.includes(stageType as ResearchStageType);
}
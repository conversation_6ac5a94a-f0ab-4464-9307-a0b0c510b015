/**
 * API 类型定义
 * 用于 Research V2H 前端库的 API Handler 函数
 */

import { ResearchStageType } from '../../domain/entities';

// ===== 请求类型 =====

export interface StartResearchRequest {
  topic: string;
}

export interface CompleteStageRequest {
  sessionId: string;
  stageType: ResearchStageType;
  userFeedback?: string;
}

// ===== 响应类型 =====

export interface ApiErrorResponse {
  error: string;
  details?: string;
}

// ===== Handler 函数类型 =====

export type ApiHandler<TRequest = any> = (
  request: Request,
  params?: TRequest
) => Promise<Response>;

export type StreamingApiHandler<TRequest = any> = (
  request: Request,
  params?: TRequest
) => Promise<Response>;

// ===== 流式响应工具类型 =====

export interface StreamEventData {
  type: string;
  data: any;
  timestamp: string;
}

export type SSEFormatter = (eventType: string, data: any) => string;
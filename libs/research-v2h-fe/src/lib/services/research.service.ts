'use client';

/**
 * 研究服务 - 适配器模式
 * 消费流式 Server Actions 并转换为原有事件格式
 * 保持 Store 接口完全不变，变更隔离在 Service 层
 */

import { 
  ResearchSession, 
  ResearchSessionFactory,
  ResearchStageType,
  ExecutionStep
} from '../domain/entities';
import { ResearchStreamEvent } from '../actions';

// 保持原有事件接口不变
export type ResearchEventType = 
  | 'session_created'
  | 'stage_started' 
  | 'stage_completed'
  | 'step_started'
  | 'step_completed'
  | 'step_failed'
  | 'error_occurred';

export interface ResearchEvent {
  type: ResearchEventType;
  payload: any;
  sessionId: string;
  timestamp: Date;
}

export type ResearchEventListener = (event: ResearchEvent) => void;

/**
 * 研究服务 - 适配器模式
 * 将流式 Server Actions 适配为原有事件格式
 */
export class ResearchService {
  private currentSession: ResearchSession | null = null;
  private eventListeners: ResearchEventListener[] = [];

  // 事件监听管理 - 保持接口不变
  addEventListener(listener: ResearchEventListener): () => void {
    this.eventListeners.push(listener);
    return () => {
      const index = this.eventListeners.indexOf(listener);
      if (index > -1) {
        this.eventListeners.splice(index, 1);
      }
    };
  }

  private emitEvent(type: ResearchEventType, payload: any): void {
    if (this.currentSession) {
      const event: ResearchEvent = {
        type,
        payload,
        sessionId: this.currentSession.id,
        timestamp: new Date()
      };
      
      this.eventListeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }

  // 会话管理 - 保持接口不变
  async startResearch(topic: string): Promise<ResearchSession> {
    try {
      // 调用新的 API 端点
      const response = await fetch('/api/research/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ topic }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.error || '启动研究时发生未知网络错误';
        this.emitEvent('error_occurred', { error: errorMessage });
        throw new Error(errorMessage);
      }

      if (!response.body) {
        throw new Error('Response body is empty.');
      }

      // 处理流式响应
      await this.processStreamResponse(response.body);
      
      if (!this.currentSession) {
        throw new Error('Session was not created after stream ended.');
      }
      
      return this.currentSession;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '启动研究时发生错误';
      this.emitEvent('error_occurred', { error: errorMessage });
      throw error;
    }
  }

  async completeStageWithFeedback(stageType: ResearchStageType, userFeedback?: string): Promise<void> {
    if (!this.currentSession) {
      throw new Error('没有活跃的研究会话');
    }

    try {
      // 调用新的 API 端点
      const response = await fetch('/api/research/complete-stage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          sessionId: this.currentSession.id, 
          stageType,
          userFeedback 
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.error || '完成阶段时发生未知网络错误';
        this.emitEvent('error_occurred', { stageType, error: errorMessage });
        throw new Error(errorMessage);
      }

      if (!response.body) {
        throw new Error('Response body is empty.');
      }

      // 处理流式响应
      await this.processStreamResponse(response.body);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '完成阶段失败';
      this.emitEvent('error_occurred', { stageType, error: errorMessage });
      throw error;
    }
  }

  // 获取当前会话状态 - 保持接口不变
  getCurrentSession(): ResearchSession | null {
    return this.currentSession;
  }

  // 重置会话 - 保持接口不变
  reset(): void {
    this.currentSession = null;
  }

  /**
   * 核心适配器方法：将流式事件转换为原有事件格式
   */
  private handleStreamEvent(streamEvent: ResearchStreamEvent): void {
    switch (streamEvent.type) {
      case 'session_created':
        // 创建新的 session 对象，避免状态冻结
        this.currentSession = ResearchSessionFactory.create(streamEvent.session.topic);
        this.currentSession.id = streamEvent.sessionId;
        this.currentSession.status = 'RUNNING';
        
        this.emitEvent('session_created', { session: this.currentSession });
        break;

      case 'stage_started':
        if (this.currentSession) {
          // 更新内部状态 - 创建新对象避免冻结
          const stage = this.currentSession.stages.find(s => s.type === streamEvent.stageType);
          if (stage) {
            // 创建新的 steps 数组
            stage.steps = streamEvent.steps.map(step => ({ ...step }));
            stage.status = 'RUNNING';
            stage.progress = 0;
          }
        }
        
        this.emitEvent('stage_started', { 
          stageType: streamEvent.stageType, 
          stage: this.currentSession?.stages.find(s => s.type === streamEvent.stageType) 
        });
        break;

      case 'stage_completed':
        if (this.currentSession) {
          const stage = this.currentSession.stages.find(s => s.type === streamEvent.stageType);
          if (stage) {
            stage.status = 'COMPLETED';
            stage.progress = 100;
            stage.result = streamEvent.result;
          }
        }
        
        this.emitEvent('stage_completed', { 
          stageType: streamEvent.stageType, 
          stage: this.currentSession?.stages.find(s => s.type === streamEvent.stageType) 
        });
        break;

      case 'step_started':
        // 更新内部步骤状态
        this.updateStepStatus(streamEvent.stepId, 'running');
        
        this.emitEvent('step_started', { 
          stepId: streamEvent.stepId,
          stageType: streamEvent.stageType,
          stepName: streamEvent.stepName,
          stepDescription: streamEvent.stepDescription
        });
        break;

      case 'step_completed':
        // 更新内部步骤状态和结果
        this.updateStepResult(streamEvent.stepId, {
          status: streamEvent.success ? 'completed' : 'failed',
          result: streamEvent.result,
          duration: streamEvent.duration,
          aiOutputs: streamEvent.aiOutputs
        });
        
        this.emitEvent('step_completed', { 
          stepId: streamEvent.stepId,
          stageType: streamEvent.stageType,
          success: streamEvent.success,
          result: streamEvent.result,
          duration: streamEvent.duration,
          logs: streamEvent.aiOutputs
        });
        break;

      case 'step_failed':
        this.updateStepStatus(streamEvent.stepId, 'failed');
        
        this.emitEvent('step_failed', { 
          stepId: streamEvent.stepId,
          stageType: streamEvent.stageType, 
          error: streamEvent.error 
        });
        break;

      case 'error_occurred':
        // 检查是否是会话完成的特殊标记
        if (streamEvent.error === 'SESSION_COMPLETED') {
          if (this.currentSession) {
            this.currentSession.status = 'COMPLETED';
            this.currentSession.updatedAt = new Date();
          }
        } else {
          this.emitEvent('error_occurred', { 
            stageType: streamEvent.stageType,
            error: streamEvent.error 
          });
        }
        break;
    }
  }

  /**
   * 辅助方法：更新步骤状态
   */
  private updateStepStatus(stepId: string, status: 'running' | 'completed' | 'failed'): void {
    if (!this.currentSession) return;
    
    for (const stage of this.currentSession.stages) {
      if (stage.steps) {
        const step = stage.steps.find(s => s.id === stepId);
        if (step) {
          step.status = status;
          break;
        }
      }
    }
  }

  /**
   * 处理流式响应的核心方法
   * 使用 ReadableStream 来处理 SSE 格式的数据
   */
  private async processStreamResponse(body: ReadableStream<Uint8Array>): Promise<void> {
    const reader = body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }

        // 解码数据块
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 按照 SSE 格式解析事件
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const eventData = line.slice(6); // 移除 'data: ' 前缀
            if (eventData.trim()) {
              try {
                const streamEvent: ResearchStreamEvent = JSON.parse(eventData);
                this.handleStreamEvent(streamEvent);
              } catch (parseError) {
                console.error('Failed to parse stream event:', eventData, parseError);
              }
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * 辅助方法：更新步骤结果
   */
  private updateStepResult(stepId: string, result: {
    status: 'completed' | 'failed';
    result: any;
    duration: number;
    aiOutputs?: any[];
  }): void {
    if (!this.currentSession) return;
    
    for (const stage of this.currentSession.stages) {
      if (stage.steps) {
        const step = stage.steps.find(s => s.id === stepId);
        if (step) {
          step.status = result.status;
          step.result = result.result;
          step.duration = result.duration;
          if (result.aiOutputs) {
            step.aiOutputs = result.aiOutputs;
          }
          break;
        }
      }
    }
  }
}
/**
 * 执行结果领域实体
 */

export interface ExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
  executionLogs: ExecutionLog[];
}

export interface ExecutionLog {
  timestamp: Date;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  metadata?: Record<string, any>;
}

export class ExecutionResultFactory {
  static success(result: any, duration: number, executionLogs: ExecutionLog[] = []): ExecutionResult {
    return {
      success: true,
      result,
      duration,
      executionLogs
    };
  }

  static failure(error: string, duration: number, executionLogs: ExecutionLog[] = []): ExecutionResult {
    return {
      success: false,
      error,
      duration,
      executionLogs
    };
  }

  static createLog(level: ExecutionLog['level'], message: string, metadata?: Record<string, any>): ExecutionLog {
    return {
      timestamp: new Date(),
      level,
      message,
      metadata
    };
  }
}
/**
 * 研究会话领域实体
 */

export type ResearchSessionStatus = 'IDLE' | 'RUNNING' | 'COMPLETED' | 'FAILED';

export interface ResearchSession {
  id: string;
  topic: string;
  status: ResearchSessionStatus;
  stages: ResearchStage[];
  userFeedback: Record<string, string>;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type ResearchStageType = 
  | 'FACT_VERIFICATION' 
  | 'IMPACT_SIMULATION' 
  | 'THESIS_RECOMMENDATION' 
  | 'FINAL_REPORT';

export type StageStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';

export interface ResearchStage {
  id: string;
  type: ResearchStageType;
  name: string;
  status: StageStatus;
  steps: ExecutionStep[];
  result?: string;
  progress: number;
  currentAction?: string;
}

export type StepStatus = 'pending' | 'running' | 'completed' | 'failed';

export type StepType = 'ANALYSIS' | 'SEARCH' | 'SYNTHESIS' | 'VALIDATION';

export interface ExecutionStep {
  id: string;
  name: string;
  type: StepType;
  status: StepStatus;
  description?: string;
  result?: any;
  duration?: number;
  aiOutputs?: AIOutput[];
}

export interface AIOutput {
  id: string;
  timestamp: string;
  type: 'thinking' | 'action' | 'result' | 'error';
  content: string;
}

// 领域工厂方法
export class ResearchSessionFactory {
  static create(topic: string): ResearchSession {
    const sessionId = `session_${Date.now()}`;
    
    return {
      id: sessionId,
      topic,
      status: 'IDLE',
      stages: this.createDefaultStages(sessionId),
      userFeedback: {},
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  private static createDefaultStages(sessionId: string): ResearchStage[] {
    const stageTypes: ResearchStageType[] = [
      'FACT_VERIFICATION',
      'IMPACT_SIMULATION', 
      'THESIS_RECOMMENDATION',
      'FINAL_REPORT'
    ];

    return stageTypes.map((type, index) => ({
      id: `${sessionId}_stage_${index}`,
      type,
      name: this.getStageDisplayName(type),
      status: index === 0 ? 'PENDING' : 'PENDING',
      steps: [],
      progress: 0
    }));
  }

  private static getStageDisplayName(type: ResearchStageType): string {
    const names: Record<ResearchStageType, string> = {
      'FACT_VERIFICATION': '事实验证',
      'IMPACT_SIMULATION': '影响模拟',
      'THESIS_RECOMMENDATION': '投资建议',
      'FINAL_REPORT': '最终报告'
    };
    return names[type];
  }
}
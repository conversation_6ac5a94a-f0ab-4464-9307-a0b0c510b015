/**
 * 演示模式步骤执行器
 * 使用模拟数据和延时来模拟真实的AI执行过程
 */

import { StepExecutor } from './step-executor.interface';
import { 
  ExecutionStep, 
  ExecutionResult, 
  ExecutionResultFactory,
  ResearchStageType,
  AIOutput,
  StepType
} from '../entities';

export class DemoStepExecutor implements StepExecutor {
  async executeStep(step: ExecutionStep): Promise<ExecutionResult> {
    const startTime = Date.now();
    
    try {
      // 模拟执行延时
      const duration = this.getExecutionDuration(step.type);
      await this.delay(duration);
      
      // 生成模拟结果
      const result = this.generateMockStepResult(step);
      const executionLogs = this.generateExecutionLogs(step);
      
      return ExecutionResultFactory.success(result, Date.now() - startTime, executionLogs);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '执行步骤时发生未知错误';
      const executionLogs = [
        ExecutionResultFactory.createLog('error', errorMessage, { stepId: step.id })
      ];
      
      return ExecutionResultFactory.failure(errorMessage, Date.now() - startTime, executionLogs);
    }
  }

  generateSteps(stageType: ResearchStageType): ExecutionStep[] {
    const stageDefinitions = this.getStageDefinitions();
    const stageSteps = stageDefinitions[stageType] || [];
    
    return stageSteps.map((stepDef, index) => ({
      id: `${stageType}_step_${index}`,
      name: stepDef.name,
      type: stepDef.type,
      status: 'pending',
      description: stepDef.description,
      aiOutputs: stepDef.generateAILogs ? this.generateMockAILogs(stepDef.name) : []
    }));
  }

  canExecute(step: ExecutionStep): boolean {
    // Demo执行器支持所有步骤类型
    return ['ANALYSIS', 'SEARCH', 'SYNTHESIS', 'VALIDATION'].includes(step.type);
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getExecutionDuration(stepType: StepType): number {
    const durations: Record<StepType, number> = {
      'SEARCH': 2000,
      'ANALYSIS': 3000,
      'SYNTHESIS': 2500,
      'VALIDATION': 1500
    };
    
    return durations[stepType] || 2000;
  }

  private generateMockStepResult(step: ExecutionStep): any {
    const results: Record<string, any> = {
      '数据收集': {
        dataPoints: 157,
        sources: ['Yahoo Finance', 'Bloomberg', 'SEC Filings'],
        timeRange: '2023.01-2024.12',
        financialMetrics: 23
      },
      '信息验证': {
        verified: true,
        accuracy: 0.995,
        sources: 3,
        conflicts: []
      },
      '市场分析': {
        marketTrend: 'positive',
        competitivePosition: 'strong',
        riskLevel: 'medium'
      },
      '财务建模': {
        model: 'DCF',
        projectedGrowth: 0.125,
        targetPrice: 85.5
      }
    };
    
    return results[step.name] || {
      status: 'completed',
      result: `${step.name}执行完成`,
      confidence: 0.85
    };
  }

  private generateExecutionLogs(step: ExecutionStep): any[] {
    return [
      ExecutionResultFactory.createLog('info', `开始执行步骤: ${step.name}`, { stepId: step.id }),
      ExecutionResultFactory.createLog('info', `${step.description || '处理中...'}`, { stepId: step.id }),
      ExecutionResultFactory.createLog('info', `步骤 ${step.name} 执行完成`, { stepId: step.id })
    ];
  }

  private generateMockAILogs(stepName: string): AIOutput[] {
    const baseTime = new Date();
    
    switch (stepName) {
      case "数据收集":
        return [
          {
            id: `${stepName}_1`,
            timestamp: new Date(baseTime.getTime() - 180000).toISOString(),
            type: 'thinking',
            content: '开始分析数据收集任务。需要收集财务数据、市场数据和行业报告。优先从官方渠道获取最新的财务报表。'
          },
          {
            id: `${stepName}_2`,
            timestamp: new Date(baseTime.getTime() - 150000).toISOString(),
            type: 'action',
            content: '调用Yahoo Finance API获取股价和基本面数据...'
          },
          {
            id: `${stepName}_3`,
            timestamp: new Date(baseTime.getTime() - 120000).toISOString(),
            type: 'result',
            content: '成功获取157条价格数据和23个财务指标。数据时间范围：2023.01-2024.12'
          }
        ];
      
      case "信息验证":
        return [
          {
            id: `${stepName}_1`,
            timestamp: new Date(baseTime.getTime() - 90000).toISOString(),
            type: 'thinking',
            content: '需要验证收集到的数据准确性。将通过多个数据源交叉验证关键财务指标。'
          },
          {
            id: `${stepName}_2`,
            timestamp: new Date(baseTime.getTime() - 60000).toISOString(),
            type: 'action',
            content: '对比Bloomberg、Reuters、Yahoo Finance三个数据源的股价数据...'
          },
          {
            id: `${stepName}_3`,
            timestamp: new Date(baseTime.getTime() - 30000).toISOString(),
            type: 'result',
            content: '数据一致性检查通过：股价数据偏差<0.1%，财务数据完全匹配官方财报。'
          }
        ];
      
      default:
        return [
          {
            id: `${stepName}_default_1`,
            timestamp: new Date(baseTime.getTime() - 60000).toISOString(),
            type: 'thinking',
            content: `开始执行${stepName}任务，分析当前市场环境和数据状况...`
          },
          {
            id: `${stepName}_default_2`,
            timestamp: new Date(baseTime.getTime() - 30000).toISOString(),
            type: 'action',
            content: '执行数据分析和处理流程...'
          },
          {
            id: `${stepName}_default_3`,
            timestamp: new Date(baseTime.getTime()).toISOString(),
            type: 'result',
            content: `${stepName}执行完成，获得预期结果。`
          }
        ];
    }
  }

  private getStageDefinitions(): Record<ResearchStageType, Array<{
    name: string;
    type: StepType;
    description: string;
    generateAILogs?: boolean;
  }>> {
    return {
      'FACT_VERIFICATION': [
        { 
          name: "数据收集", 
          type: 'SEARCH',
          description: "收集相关财务和市场数据",
          generateAILogs: true
        },
        { 
          name: "信息验证", 
          type: 'VALIDATION',
          description: "核查信息来源可靠性",
          generateAILogs: true
        },
        { 
          name: "交叉验证", 
          type: 'ANALYSIS', 
          description: "多源数据交叉对比"
        },
        { 
          name: "结论整理", 
          type: 'SYNTHESIS',
          description: "整理核查结果"
        }
      ],
      
      'IMPACT_SIMULATION': [
        { 
          name: "市场分析", 
          type: 'ANALYSIS',
          description: "分析市场环境和竞争态势"
        },
        { 
          name: "财务建模", 
          type: 'ANALYSIS',
          description: "构建财务影响模型"
        },
        { 
          name: "风险评估", 
          type: 'ANALYSIS',
          description: "识别主要风险因素" 
        },
        { 
          name: "机会识别", 
          type: 'ANALYSIS',
          description: "发现潜在投资机会" 
        },
        { 
          name: "影响量化", 
          type: 'SYNTHESIS',
          description: "量化分析预期影响" 
        }
      ],
      
      'THESIS_RECOMMENDATION': [
        { name: "基本面分析", type: 'ANALYSIS', description: "深度分析基本面指标" },
        { name: "估值分析", type: 'ANALYSIS', description: "评估当前估值水平" },
        { name: "策略制定", type: 'SYNTHESIS', description: "制定投资策略建议" },
        { name: "风险控制", type: 'VALIDATION', description: "设定风险控制措施" }
      ],
      
      'FINAL_REPORT': [
        { name: "数据整合", type: 'SYNTHESIS', description: "整合所有分析结果" },
        { name: "报告撰写", type: 'SYNTHESIS', description: "撰写完整投资报告" },
        { name: "质量检查", type: 'VALIDATION', description: "检查报告完整性和准确性" }
      ]
    };
  }
}
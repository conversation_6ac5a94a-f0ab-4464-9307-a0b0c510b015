/**
 * 步骤执行器接口定义
 */

import { ExecutionStep, ResearchStageType } from '../entities';
import { ExecutionResult } from '../entities';

export interface StepExecutor {
  /**
   * 执行单个步骤
   */
  executeStep(step: ExecutionStep): Promise<ExecutionResult>;

  /**
   * 为指定阶段生成步骤列表
   */
  generateSteps(stageType: ResearchStageType): ExecutionStep[];

  /**
   * 检查是否支持指定的步骤类型
   */
  canExecute(step: ExecutionStep): boolean;
}
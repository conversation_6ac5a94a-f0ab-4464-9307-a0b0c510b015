/**
 * Research V2 Store 操作函数
 * 业务逻辑和状态变更操作
 */

import { StateCreator } from 'zustand';
import { produce } from 'immer';
import { ResearchService, ResearchEvent } from '../services';
import { ResearchStageType } from '../domain/entities';
import { ResearchV2Store, ResearchV2UIState } from './types';
import { INITIAL_STATE } from './constants';
import { updateSessionStep, getAvailableStages } from './utils';
import { getDisplayStageType, getStageDisplayStatus, getAvailableStagesForNavigation, checkStageClickable } from './selectors';

/**
 * 创建基础操作函数
 */
export function createBasicActions(
  set: Parameters<StateCreator<ResearchV2Store>>[0],
  get: Parameters<StateCreator<ResearchV2Store>>[1]
) {
  return {
    // 开始研究 - 使用流式服务
    startResearch: async (topic: string) => {
      const state = get();
      if (!state.researchService) {
        set({ error: '服务未初始化，请稍后重试', isLoading: false });
        return;
      }
      
      try {
        set({ isLoading: true, error: null });
        
        // 调用流式服务，事件将通过 _handleServiceEvent 处理
        await state.researchService.startResearch(topic);
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '启动研究失败';
        set({ 
          error: errorMessage, 
          isLoading: false 
        });
      }
    },

    // 完成阶段 - 使用流式服务
    completeStage: async (stageType: ResearchStageType, userInput?: string) => {
      const state = get();
      if (!state.researchService) {
        set({ error: '服务未初始化，请稍后重试', isLoading: false });
        return;
      }
      
      try {
        set({ isLoading: true, error: null });
        
        // 调用流式服务，事件将通过 _handleServiceEvent 处理
        await state.researchService.completeStageWithFeedback(stageType, userInput);
        
        set({ 
          currentInput: '',
          isLoading: false
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '完成阶段失败';
        set({ 
          error: errorMessage, 
          isLoading: false 
        });
      }
    },

    // 更新输入
    updateInput: (input: string) => {
      set({ currentInput: input });
    },

    // 重置状态
    reset: () => {
      const state = get();
      if (state.researchService) {
        state.researchService.reset();
      }
      set(INITIAL_STATE);
    },

    // 清除错误
    clearError: () => {
      set({ error: null });
    },
  };
}

/**
 * 创建导航操作函数
 */
export function createNavigationActions(
  set: Parameters<StateCreator<ResearchV2Store>>[0],
  get: Parameters<StateCreator<ResearchV2Store>>[1]
) {
  return {
    // 导航到指定阶段（历史查看）
    navigateToStage: (stageType: ResearchStageType) => {
      const state = get() as ResearchV2UIState;
      
      // 检查阶段是否可点击
      if (!state.currentSession) return;
      
      const stage = state.currentSession.stages.find(s => s.type === stageType);
      if (!stage || (stage.status !== 'RUNNING' && stage.status !== 'COMPLETED')) {
        return;
      }
      
      set({
        viewingStageType: stageType,
        viewMode: 'history',
        stageNavigation: {
          ...state.stageNavigation,
          isNavigating: true
        }
      });
    },

    // 退出导航，回到执行模式
    exitNavigation: () => {
      const state = get() as ResearchV2UIState;
      
      set({
        viewMode: 'execution',
        viewingStageType: null,
        stageNavigation: {
          ...state.stageNavigation,
          isNavigating: false
        }
      });
    },
  };
}

/**
 * 创建查询操作函数
 */
export function createQueryActions(
  get: Parameters<StateCreator<ResearchV2Store>>[1]
) {
  return {
    // 获取可导航的阶段列表
    getAvailableStagesForNavigation: (): ResearchStageType[] => {
      const state = get() as ResearchV2UIState;
      return getAvailableStagesForNavigation(state);
    },

    // 获取当前显示的阶段类型
    getDisplayStageType: (): ResearchStageType | null => {
      const state = get() as ResearchV2UIState;
      return getDisplayStageType(state);
    },

    // 判断阶段是否可点击
    isStageClickable: (stageType: ResearchStageType): boolean => {
      const state = get() as ResearchV2UIState;
      return checkStageClickable(state, stageType);
    },

    // 获取阶段显示状态
    getStageDisplayStatus: (stageType: ResearchStageType) => {
      const state = get() as ResearchV2UIState;
      return getStageDisplayStatus(state, stageType);
    },
  };
}

/**
 * 创建内部同步操作函数
 */
export function createSyncActions(
  set: Parameters<StateCreator<ResearchV2Store>>[0],
  _get: Parameters<StateCreator<ResearchV2Store>>[1]
) {
  return {
    // 同步服务状态
    _syncWithService: () => {
      const state = _get() as ResearchV2UIState;
      if (!state.researchService) return;
      
      const currentSession = state.researchService.getCurrentSession();
      
      // 更新可用阶段列表
      const availableStages = getAvailableStages(currentSession);
      
      set({ 
        currentSession,
        stageNavigation: {
          ...state.stageNavigation,
          availableStages
        }
      });
    },

    // 处理服务事件 - 处理适配器转换后的事件，确保执行事件不影响查看状态
    _handleServiceEvent: (event: ResearchEvent) => {
      const { type, payload } = event;
      const state = _get() as ResearchV2UIState;
      
      switch (type) {
        case 'session_created':
          // 创建新的 session 对象，避免状态冻结问题
          const newSession = payload.session;
          
          set({ 
            currentSession: newSession,
            currentStageType: 'FACT_VERIFICATION',
            currentStepIndex: 0,
            isLoading: false,
            viewMode: 'execution',
            viewingStageType: null,
            stageNavigation: {
              isNavigating: false,
              availableStages: ['FACT_VERIFICATION']
            }
          });
          break;
          
        case 'stage_started':
          // 更新执行状态，不影响查看状态
          const newAvailableStages = [...state.stageNavigation.availableStages];
          if (!newAvailableStages.includes(payload.stageType)) {
            newAvailableStages.push(payload.stageType);
          }
          
          // 使用 immer 更新 session 中的 stage 信息
          set(produce((draft: ResearchV2UIState) => {
            if (draft.currentSession) {
              // 找到对应的 stage 并更新
              const stage = draft.currentSession.stages.find(s => s.type === payload.stageType);
              if (stage) {
                stage.status = 'RUNNING';
                stage.progress = 0;
                stage.steps = payload.stage?.steps || [];
              }
            }
            
            // 更新执行状态
            draft.currentStageType = payload.stageType;
            draft.currentStepIndex = 0;
            draft.isLoading = true;
            draft.stageNavigation.availableStages = newAvailableStages;
            // 保持 viewingStageType 和 viewMode 不变
          }));
          break;
          
        case 'stage_completed':
          // 阶段完成时更新stage状态，但不立即切换currentStageType
          set(produce((draft: ResearchV2UIState) => {
            if (draft.currentSession) {
              const stage = draft.currentSession.stages.find(s => s.type === payload.stageType);
              if (stage) {
                stage.status = 'COMPLETED';
                stage.progress = 100;
                stage.result = payload.stage?.result || '';
              }
            }
            draft.isLoading = false;
            // 保持 currentStageType, viewingStageType 和 viewMode 不变
          }));
          break;
          
        case 'step_started':
          // Store负责更新step状态为running，不影响查看状态
          set((state: ResearchV2UIState) => {
            if (!state.currentSession) return state;
            
            const updatedSession = updateSessionStep(state.currentSession, payload.stepId, (step) => {
              step.status = 'running';
            });
            
            return { 
              ...state,
              currentSession: updatedSession,
              isLoading: true
              // 不更新查看相关状态
            };
          });
          break;
          
        case 'step_completed':
          // Store负责更新step状态为completed并设置结果，不影响查看状态
          set((state: ResearchV2UIState) => {
            if (!state.currentSession) return state;
            
            const updatedSession = updateSessionStep(state.currentSession, payload.stepId, (step) => {
              step.status = payload.success ? 'completed' : 'failed';
              step.result = payload.result;
              step.duration = payload.duration;
              if (payload.logs) {
                step.aiOutputs = payload.logs;
              }
            });
            
            return { 
              ...state,
              currentSession: updatedSession 
              // 不更新查看相关状态
            };
          });
          break;
          
        case 'step_failed':
          // Store负责更新step状态为failed，不影响查看状态
          set((state: ResearchV2UIState) => {
            if (!state.currentSession) return state;
            
            const updatedSession = updateSessionStep(state.currentSession, payload.stepId, (step) => {
              step.status = 'failed';
              step.result = payload.error;
            });
            
            return { 
              ...state,
              currentSession: updatedSession
              // 不更新查看相关状态 
            };
          });
          break;
          
        case 'error_occurred':
          set((state: ResearchV2UIState) => ({
            ...state,
            error: payload.error,
            isLoading: false
            // 错误发生时也不影响查看状态
          }));
          break;
      }
    }
  };
}
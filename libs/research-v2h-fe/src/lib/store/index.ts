/**
 * Store 模块导出
 * 统一导出所有类型和函数
 */

// 主要导出
export { useResearchV2Store } from './research-v2.store';

// 类型导出
export type {
  ResearchV2UIState,
  ResearchV2UIActions,
  ResearchV2Store,
  StageDisplayStatus,
  ViewMode,
  StageNavigationState
} from './types';

// 常量导出
export { STAGE_ORDER, INITIAL_STATE } from './constants';

// 工具函数导出
export {
  getNextStageType,
  findStepById,
  updateSessionStep,
  isStageClickable,
  getAvailableStages
} from './utils';

// 选择器导出
export {
  getDisplayStageType,
  getStageDisplayStatus,
  getAvailableStagesForNavigation,
  checkStageClickable
} from './selectors';
/**
 * Research V2 重构后的 Store - DDD-Lite架构
 * 专注于UI状态管理，业务逻辑委托给ResearchService
 * 
 * v2.1: 新增Stage可点击导航功能支持
 * - 执行状态与查看状态分离
 * - 支持历史阶段查看
 * - 智能导航和状态切换
 * 
 * 重构版本: 拆分为多个文件，提升代码质量和可维护性
 */

import { create, StateCreator } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ResearchService, ResearchEvent } from '../services';
import { ResearchV2Store } from './types';
import { INITIAL_STATE, DEVTOOLS_CONFIG } from './constants';
import { createBasicActions, createNavigationActions, createQueryActions, createSyncActions } from './actions';

// 类型安全的 Store 创建器
const createResearchV2Store: StateCreator<ResearchV2Store> = (set, get) => {
  return {
    ...INITIAL_STATE,
    
    // 服务初始化方法
    initializeService: () => {
      const state = get();
      if (!state.researchService) {
        // 只在客户端环境中创建服务实例
        if (typeof window !== 'undefined') {
          const researchService = new ResearchService();
          
          // 监听服务事件
          researchService.addEventListener((event: ResearchEvent) => {
            const { _handleServiceEvent } = get();
            _handleServiceEvent(event);
          });
          
          // 更新 Store 中的服务实例
          set({ researchService });
        }
      }
    },
    
    // 基础操作
    ...createBasicActions(set, get),
    
    // 导航操作
    ...createNavigationActions(set, get),
    
    // 查询操作
    ...createQueryActions(get),
    
    // 同步操作
    ...createSyncActions(set, get),
  };
};

// 创建类型安全的Store
export const useResearchV2Store = create<ResearchV2Store>()(
  devtools(createResearchV2Store, DEVTOOLS_CONFIG)
);
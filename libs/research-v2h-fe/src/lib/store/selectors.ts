/**
 * Research V2 Store 选择器
 * 状态查询和计算逻辑
 */

import { ResearchStageType } from '../domain/entities';
import { ResearchV2UIState, StageDisplayStatus } from './types';
import { isStageClickable, getAvailableStages } from './utils';

/**
 * 获取当前显示的阶段类型
 */
export function getDisplayStageType(state: ResearchV2UIState): ResearchStageType | null {
  // 如果在历史查看模式，显示正在查看的阶段
  if (state.viewMode === 'history' && state.viewingStageType) {
    return state.viewingStageType;
  }
  
  // 否则显示正在执行的阶段
  return state.currentStageType;
}

/**
 * 获取阶段显示状态
 */
export function getStageDisplayStatus(
  state: ResearchV2UIState, 
  stageType: ResearchStageType
): StageDisplayStatus {
  if (!isStageClickable(state.currentSession, stageType)) return 'pending';
  if (state.currentStageType === stageType) return 'current-execution';
  if (state.viewMode === 'history' && state.viewingStageType === stageType) return 'current-viewing';
  return 'available';
}

/**
 * 获取可导航的阶段列表
 */
export function getAvailableStagesForNavigation(state: ResearchV2UIState): ResearchStageType[] {
  return getAvailableStages(state.currentSession);
}

/**
 * 检查阶段是否可点击
 */
export function checkStageClickable(state: ResearchV2UIState, stageType: ResearchStageType): boolean {
  return isStageClickable(state.currentSession, stageType);
}
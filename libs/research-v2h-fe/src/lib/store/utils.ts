/**
 * Research V2 Store 工具函数
 * 纯函数工具，无副作用
 */

import { produce } from 'immer';
import { ResearchSession, ResearchStageType, ExecutionStep } from '../domain/entities';
import { STAGE_ORDER } from './constants';

/**
 * 获取下一个阶段类型
 */
export function getNextStageType(currentStage: ResearchStageType): ResearchStageType | null {
  const currentIndex = STAGE_ORDER.indexOf(currentStage);
  return currentIndex < STAGE_ORDER.length - 1 ? STAGE_ORDER[currentIndex + 1] : null;
}

/**
 * 根据步骤ID查找步骤
 */
export function findStepById(session: ResearchSession | null, stepId: string): ExecutionStep | null {
  if (!session) return null;
  
  for (const stage of session.stages) {
    const step = stage.steps?.find(s => s.id === stepId);
    if (step) return step;
  }
  return null;
}

/**
 * 更新会话中的特定步骤
 * 使用 immer 实现不可变更新，避免手动深拷贝
 */
export function updateSessionStep(
  session: ResearchSession, 
  stepId: string, 
  updateFn: (step: ExecutionStep) => void
): ResearchSession {
  return produce(session, draft => {
    for (const stage of draft.stages) {
      if (stage.steps) {
        const step = stage.steps.find(s => s.id === stepId);
        if (step) {
          updateFn(step);
          break;
        }
      }
    }
  });
}

/**
 * 检查阶段是否可点击（已开始或已完成）
 */
export function isStageClickable(session: ResearchSession | null, stageType: ResearchStageType): boolean {
  if (!session) return false;
  
  const stage = session.stages.find(s => s.type === stageType);
  return stage ? (stage.status === 'RUNNING' || stage.status === 'COMPLETED') : false;
}

/**
 * 获取可导航的阶段列表
 */
export function getAvailableStages(session: ResearchSession | null): ResearchStageType[] {
  if (!session) return [];
  
  return session.stages
    .filter(stage => stage.status === 'RUNNING' || stage.status === 'COMPLETED')
    .map(stage => stage.type);
}
/**
 * Research V2 Store 类型定义
 * DDD-Lite架构 - UI状态类型定义
 */

import { ResearchSession, ResearchStageType } from '../domain/entities';
import { ResearchEvent, ResearchService } from '../services';

// UI专用状态接口 - 扩展支持导航功能
export interface ResearchV2UIState {
  // 核心会话状态
  currentSession: ResearchSession | null;
  
  // 服务实例（懒初始化）
  researchService: ResearchService | null;
  
  // UI状态
  isLoading: boolean;
  currentInput: string;
  error: string | null;
  
  // 执行状态（后台持续，不受用户查看影响）
  currentStageType: ResearchStageType | null;    // 正在执行的阶段
  currentStepIndex: number;                      // 当前执行步骤索引
  
  // 新增：查看状态（用户控制）
  viewingStageType: ResearchStageType | null;    // 正在查看的阶段
  viewMode: ViewMode;                            // 查看模式
  stageNavigation: StageNavigationState;         // 导航状态
}

// UI操作接口 - 扩展导航操作
export interface ResearchV2UIActions {
  // 服务初始化
  initializeService: () => void;
  
  // 基础操作
  startResearch: (topic: string) => Promise<void>;
  completeStage: (stageType: ResearchStageType, userInput?: string) => Promise<void>;
  updateInput: (input: string) => void;
  reset: () => void;
  clearError: () => void;
  
  // 新增：导航操作
  navigateToStage: (stageType: ResearchStageType) => void;
  exitNavigation: () => void;
  getAvailableStagesForNavigation: () => ResearchStageType[];
  
  // 新增：状态查询辅助方法
  getDisplayStageType: () => ResearchStageType | null;
  isStageClickable: (stageType: ResearchStageType) => boolean;
  getStageDisplayStatus: (stageType: ResearchStageType) => StageDisplayStatus;
  
  // 内部状态同步方法
  _syncWithService: () => void;
  _handleServiceEvent: (event: ResearchEvent) => void;
}

// 完整Store类型
export type ResearchV2Store = ResearchV2UIState & ResearchV2UIActions;

// 阶段显示状态类型
export type StageDisplayStatus = 'pending' | 'available' | 'current-execution' | 'current-viewing';

// 视图模式类型
export type ViewMode = 'execution' | 'history';

// 阶段导航状态
export interface StageNavigationState {
  isNavigating: boolean;
  availableStages: ResearchStageType[];
}
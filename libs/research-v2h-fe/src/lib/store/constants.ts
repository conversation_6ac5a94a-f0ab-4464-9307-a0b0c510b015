/**
 * Research V2 Store 常量定义
 * 配置化的常量，便于维护和修改
 */

import { ResearchStageType } from '../domain/entities';
import { ResearchV2UIState } from './types';

// 阶段顺序定义
export const STAGE_ORDER: ResearchStageType[] = [
  'FACT_VERIFICATION',
  'IMPACT_SIMULATION', 
  'THESIS_RECOMMENDATION',
  'FINAL_REPORT'
];

// Store 初始状态
export const INITIAL_STATE: ResearchV2UIState = {
  currentSession: null,
  researchService: null,
  isLoading: false,
  currentInput: '',
  error: null,
  currentStageType: null,
  currentStepIndex: -1,
  // 导航状态
  viewingStageType: null,
  viewMode: 'execution',
  stageNavigation: {
    isNavigating: false,
    availableStages: []
  }
};

// Zustand devtools 配置
export const DEVTOOLS_CONFIG = {
  name: 'research-v2-navigation-store',
} as const;
/**
 * Research Stage Icons - 独立的SVG图标组件库
 * 
 * 目的: 将SVG图标从业务组件中完全解耦
 * 防护: 重构时不会误改动图标资源
 * 维护: 图标修改不影响业务逻辑
 * 
 * 使用规范:
 * - 每个图标都是独立的React组件
 * - 使用currentColor继承父元素颜色
 * - 统一尺寸为w-5 h-5 (20x20px)
 * - 支持className覆盖样式
 */

import React from 'react';

export interface IconProps {
  className?: string;
  'aria-label'?: string;
}

/**
 * 事实核查验证图标 - 盾牌带勾
 * 语义: 安全、验证、保护
 */
export const FactVerificationIcon: React.FC<IconProps> = ({ 
  className = "w-5 h-5", 
  ...props 
}) => (
  <svg 
    className={className} 
    fill="currentColor" 
    viewBox="0 0 20 20"
    aria-label="事实核查验证"
    {...props}
  >
    <path 
      fillRule="evenodd" 
      d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" 
      clipRule="evenodd" 
    />
  </svg>
);

/**
 * 影响模拟分析图标 - 饼图
 * 语义: 分析、数据、统计
 */
export const ImpactSimulationIcon: React.FC<IconProps> = ({ 
  className = "w-5 h-5", 
  ...props 
}) => (
  <svg 
    className={className} 
    fill="currentColor" 
    viewBox="0 0 20 20"
    aria-label="影响模拟分析"
    {...props}
  >
    <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"/>
    <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"/>
  </svg>
);

/**
 * 投资策略建议图标 - 设置齿轮
 * 语义: 策略、配置、优化
 */
export const ThesisRecommendationIcon: React.FC<IconProps> = ({ 
  className = "w-5 h-5", 
  ...props 
}) => (
  <svg 
    className={className} 
    fill="currentColor" 
    viewBox="0 0 20 20"
    aria-label="投资策略建议"
    {...props}
  >
    <path 
      fillRule="evenodd" 
      d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.533 1.533 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" 
      clipRule="evenodd" 
    />
  </svg>
);

/**
 * 最终报告图标 - 文档
 * 语义: 报告、文档、总结
 */
export const FinalReportIcon: React.FC<IconProps> = ({ 
  className = "w-5 h-5", 
  ...props 
}) => (
  <svg 
    className={className} 
    fill="currentColor" 
    viewBox="0 0 20 20"
    aria-label="最终报告"
    {...props}
  >
    <path 
      fillRule="evenodd" 
      d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" 
      clipRule="evenodd" 
    />
  </svg>
);

/**
 * 通用状态图标
 */

/**
 * 完成状态图标 - 勾选
 */
export const CompletedIcon: React.FC<IconProps> = ({ 
  className = "w-5 h-5", 
  ...props 
}) => (
  <svg 
    className={className} 
    fill="currentColor" 
    viewBox="0 0 20 20"
    aria-label="已完成"
    {...props}
  >
    <path 
      fillRule="evenodd" 
      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
      clipRule="evenodd" 
    />
  </svg>
);

/**
 * 运行中状态指示器 - 动画点
 */
export const RunningIndicator: React.FC<IconProps> = ({ 
  className = "w-3 h-3", 
  ...props 
}) => (
  <div 
    className={`${className} bg-white rounded-full animate-pulse`}
    aria-label="进行中"
    {...props}
  />
);

/**
 * 导航和UI图标
 */

/**
 * 返回箭头图标
 */
export const BackArrowIcon: React.FC<IconProps> = ({ 
  className = "w-4 h-4", 
  ...props 
}) => (
  <svg 
    className={className} 
    fill="none" 
    stroke="currentColor" 
    viewBox="0 0 24 24"
    aria-label="返回"
    {...props}
  >
    <path 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      strokeWidth={2} 
      d="M10 19l-7-7m0 0l7-7m-7 7h18" 
    />
  </svg>
);

/**
 * 搜索图标
 */
export const SearchIcon: React.FC<IconProps> = ({ 
  className = "w-4 h-4", 
  ...props 
}) => (
  <svg 
    className={className} 
    fill="none" 
    stroke="currentColor" 
    viewBox="0 0 24 24"
    aria-label="搜索"
    {...props}
  >
    <path 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      strokeWidth={2} 
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
    />
  </svg>
);

/**
 * 闪电图标 - 开始研究
 */
export const LightningIcon: React.FC<IconProps> = ({ 
  className = "w-4 h-4", 
  ...props 
}) => (
  <svg 
    className={className} 
    fill="none" 
    stroke="currentColor" 
    viewBox="0 0 24 24"
    aria-label="开始"
    {...props}
  >
    <path 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      strokeWidth={2} 
      d="M13 10V3L4 14h7v7l9-11h-7z" 
    />
  </svg>
);

/**
 * 图表图标 - 分析流程
 */
export const ChartIcon: React.FC<IconProps> = ({ 
  className = "w-4 h-4", 
  ...props 
}) => (
  <svg 
    className={className} 
    fill="none" 
    stroke="currentColor" 
    viewBox="0 0 24 24"
    aria-label="分析"
    {...props}
  >
    <path 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      strokeWidth={2} 
      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
    />
  </svg>
);

/**
 * 加载中旋转图标
 */
export const SpinnerIcon: React.FC<IconProps> = ({ 
  className = "w-4 h-4", 
  ...props 
}) => (
  <div 
    className={`${className} border-2 border-white/30 border-t-white rounded-full animate-spin`}
    aria-label="加载中"
    {...props}
  />
);

/**
 * 图标导出映射 - 便于统一引用
 */
export const ResearchStageIcons = {
  FACT_VERIFICATION: FactVerificationIcon,
  IMPACT_SIMULATION: ImpactSimulationIcon,
  THESIS_RECOMMENDATION: ThesisRecommendationIcon,
  FINAL_REPORT: FinalReportIcon,
} as const;

export const StatusIcons = {
  COMPLETED: CompletedIcon,
  RUNNING: RunningIndicator,
} as const;

export const UIIcons = {
  BACK_ARROW: BackArrowIcon,
  SEARCH: SearchIcon,
  LIGHTNING: LightningIcon,
  CHART: ChartIcon,
  SPINNER: SpinnerIcon,
} as const;

/**
 * 类型定义
 */
export type ResearchStageIconType = keyof typeof ResearchStageIcons;
export type StatusIconType = keyof typeof StatusIcons;
export type UIIconType = keyof typeof UIIcons;
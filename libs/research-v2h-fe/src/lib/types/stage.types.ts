/**
 * Stage 组件相关类型定义
 * 
 * v2.1: 新增导航功能支持
 */

export type StageMode = 'execution' | 'history';

export interface BaseStageProps {
  /**
   * Stage 组件显示模式
   * - execution: 执行模式，包含完整交互功能
   * - history: 历史查看模式，只读展示，隐藏交互元素
   */
  mode?: StageMode;
}

export interface ModeConfig {
  containerClassName: string;
  showActions: boolean;
  readOnly: boolean;
  headerSuffix: string;
}

/**
 * 获取模式相关配置的工具函数
 */
export function getModeConfig(mode: StageMode): ModeConfig {
  if (mode === 'history') {
    return {
      containerClassName: 'opacity-90', // 略微降低透明度表示历史模式
      showActions: false,
      readOnly: true,
      headerSuffix: ' (历史记录)'
    };
  }
  
  return {
    containerClassName: '',
    showActions: true,
    readOnly: false,
    headerSuffix: ''
  };
}
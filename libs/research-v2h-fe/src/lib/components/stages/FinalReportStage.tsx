'use client';

import { useResearchV2Store } from '../../store/store';
import { StageLayout } from '../shared/StageLayout';
import { BaseStageProps, getModeConfig } from '../../types/stage.types';

/**
 * 最终报告阶段组件
 * 
 * v2.1: 新增模式支持
 * - execution: 执行模式，包含完整交互功能
 * - history: 历史查看模式，只读展示，隐藏交互元素
 */
export function FinalReportStage({ mode = 'execution' }: BaseStageProps) {
  const { currentSession, reset } = useResearchV2Store();

  // 获取最终报告阶段
  const finalReportStage = currentSession?.stages.find(s => s.type === 'FINAL_REPORT');
  const researchTopic = currentSession?.topic || '';

  const isCompleted = finalReportStage?.status === 'COMPLETED';
  const isRunning = finalReportStage?.status === 'RUNNING';
  const isFailed = finalReportStage?.status === 'FAILED';
  const isPending = finalReportStage?.status === 'PENDING';
  const totalSteps = finalReportStage?.steps?.length || 0;

  const modeConfig = getModeConfig(mode);

  const handleNewResearch = () => {
    // 历史模式下禁用交互
    if (mode === 'history') return;
    
    reset();
  };

  const handleCopyReport = () => {
    // 历史模式下仍然允许复制功能
    const reportContent = finalReportStage?.result || '';
    navigator.clipboard.writeText(reportContent);
    alert('报告已复制到剪贴板');
  };

  return (
    <StageLayout
      title={`最终投资研究报告${modeConfig.headerSuffix}`}
      description={`关于 "${researchTopic}" 的完整投资研究报告`}
      stageData={finalReportStage}
      notStartedMessage={`最终报告阶段尚未开始${mode === 'history' ? ' (历史查看模式)' : ''}`}
      renderContent={() => (
        <>
          {/* 模式提示 */}
          {mode === 'history' && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
                <span className="text-sm text-gray-700 font-medium">历史查看模式</span>
              </div>
              <p className="text-xs text-gray-600 mt-1">
                您正在查看此阶段的历史执行记录，仅开始新研究功能已禁用
              </p>
            </div>
          )}

          {/* 运行中状态 */}
          {isRunning && (
            <div className="running-indicator">
              <div className="running-indicator-content">
                <div className="running-spinner"></div>
                <div className="running-text">
                  <h3 className="running-title">
                    {mode === 'history' ? '曾经正在生成最终报告' : '正在生成最终报告'}
                  </h3>
                  <p className="running-description">
                    {mode === 'history'
                      ? '此阶段曾经在整合所有分析结果，生成完整的投资研究报告'
                      : '正在整合所有分析结果，生成完整的投资研究报告，请稍候...'
                    }
                  </p>
                  {finalReportStage?.currentAction && (
                    <p className="running-current-action">
                      {mode === 'history' ? '当时正在: ' : '当前: '}{finalReportStage.currentAction}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 等待状态 */}
          {isPending && (
            <div className="pending-indicator">
              <h3 className="pending-title">准备生成最终研究报告</h3>
              <p className="pending-description">
                系统将整合前面所有阶段的分析结果，生成一份完整、专业的投资研究报告，
                包括执行摘要、详细分析、投资建议和风险提示等关键内容。
              </p>
            </div>
          )}

          {/* 失败状态 */}
          {isFailed && (
            <div className="error-indicator">
              <h3 className="error-title">最终报告生成失败</h3>
              <p className="error-description">
                生成最终报告时遇到问题，请检查网络连接或稍后重试。
              </p>
            </div>
          )}

          {/* 最终报告展示 */}
          {isCompleted && finalReportStage?.result && (
            <div className="final-report-card">
              <h3 className="final-report-title">
                🎉 投资研究报告{mode === 'history' ? '曾经' : ''}完成
              </h3>
              <div className="final-report-content">
                {finalReportStage.result}
              </div>
              
              {/* 研究完成提示 */}
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-green-800 font-medium">
                    {mode === 'history' ? '研究已完成' : '恭喜！完整的投资研究已完成'}
                  </span>
                </div>
                <p className="text-sm text-green-700 mt-2">
                  {mode === 'history' 
                    ? '此次投资研究已成功完成，包含了完整的分析和建议。'
                    : '您已获得了全面的投资分析报告，包括事实核查、影响评估、投资策略和最终建议。'
                  }
                </p>
              </div>
            </div>
          )}
        </>
      )}
      renderActions={() => (
        isCompleted ? (
          <div className="stage-action-section">
            <div className="enhanced-input-group">
              {/* 历史查看提示 */}
              {totalSteps > 0 && (
                <div className="history-access-hint">
                  ℹ️ 点击"执行步骤详情"可查看完整的AI执行历史和分析过程
                </div>
              )}
              
              <div className="action-button-group">
                <button
                  onClick={handleNewResearch}
                  className="gradient-button-primary"
                  disabled={modeConfig.readOnly}
                >
                  <svg className="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>开始新的研究</span>
                </button>
                
                <button
                  onClick={handleCopyReport}
                  className="gradient-button-secondary"
                >
                  <svg className="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <span>复制报告</span>
                </button>
              </div>
              
              <div className="action-description">
                {mode === 'history' 
                  ? '✨ 历史研究记录查看完毕'
                  : '🎉 感谢使用AI投资研究平台！'
                }
              </div>
            </div>
          </div>
        ) : null
      )}
    />
  );
}
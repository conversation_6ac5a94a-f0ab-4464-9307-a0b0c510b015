'use client';

import { useResearchV2Store } from '../../store/store';
import { StageLayout } from '../shared/StageLayout';
import { BaseStageProps, getModeConfig } from '../../types/stage.types';

/**
 * 投资建议阶段组件
 * 
 * v2.1: 新增模式支持
 * - execution: 执行模式，包含完整交互功能
 * - history: 历史查看模式，只读展示，隐藏交互元素
 */
export function ThesisRecommendationStage({ mode = 'execution' }: BaseStageProps) {
  const { currentSession, currentInput, updateInput, completeStage } = useResearchV2Store();

  // 获取投资建议阶段
  const thesisRecommendationStage = currentSession?.stages.find(s => s.type === 'THESIS_RECOMMENDATION');
  const researchTopic = currentSession?.topic || '';

  const isCompleted = thesisRecommendationStage?.status === 'COMPLETED';
  const isRunning = thesisRecommendationStage?.status === 'RUNNING';
  const isFailed = thesisRecommendationStage?.status === 'FAILED';
  const isPending = thesisRecommendationStage?.status === 'PENDING';
  const totalSteps = thesisRecommendationStage?.steps?.length || 0;

  const modeConfig = getModeConfig(mode);

  const handleContinue = () => {
    // 历史模式下禁用交互
    if (mode === 'history') return;
    
    completeStage('THESIS_RECOMMENDATION', currentInput);
  };

  return (
    <StageLayout
      title={`投资策略建议${modeConfig.headerSuffix}`}
      description={`正在为 "${researchTopic}" 制定专业投资策略与执行建议`}
      stageData={thesisRecommendationStage}
      notStartedMessage={`投资建议阶段尚未开始${mode === 'history' ? ' (历史查看模式)' : ''}`}
      renderContent={() => (
        <>
          {/* 模式提示 */}
          {mode === 'history' && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
                <span className="text-sm text-gray-700 font-medium">历史查看模式</span>
              </div>
              <p className="text-xs text-gray-600 mt-1">
                您正在查看此阶段的历史执行记录，所有交互功能已禁用
              </p>
            </div>
          )}

          {/* 运行中状态 */}
          {isRunning && (
            <div className="running-indicator">
              <div className="running-indicator-content">
                <div className="running-spinner"></div>
                <div className="running-text">
                  <h3 className="running-title">
                    {mode === 'history' ? '曾经正在制定投资策略' : '正在制定投资策略'}
                  </h3>
                  <p className="running-description">
                    {mode === 'history'
                      ? '此阶段曾经在制定专业投资策略和执行建议'
                      : '正在基于前期分析结果制定专业投资策略和执行建议，请稍候...'
                    }
                  </p>
                  {thesisRecommendationStage?.currentAction && (
                    <p className="running-current-action">
                      {mode === 'history' ? '当时正在: ' : '当前: '}{thesisRecommendationStage.currentAction}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 等待状态 */}
          {isPending && (
            <div className="pending-indicator">
              <h3 className="pending-title">准备开始投资策略制定</h3>
              <p className="pending-description">
                系统将基于前期的事实验证和影响分析结果，制定专业的投资策略建议，
                包括投资时机、风险控制、资金配置等关键建议。
              </p>
            </div>
          )}

          {/* 失败状态 */}
          {isFailed && (
            <div className="error-indicator">
              <h3 className="error-title">投资策略制定执行失败</h3>
              <p className="error-description">
                执行投资策略制定时遇到问题，请检查网络连接或稍后重试。
              </p>
            </div>
          )}

          {/* 完成状态 */}
          {isCompleted && thesisRecommendationStage?.result && (
            <div className="completed-indicator">
              <h3 className="completed-title">
                ✅ 投资策略建议{mode === 'history' ? '曾经' : ''}完成
              </h3>
              <div className="completed-content">
                {thesisRecommendationStage.result}
              </div>
            </div>
          )}
        </>
      )}
      renderActions={() => 
        modeConfig.showActions && isCompleted ? (
          <div className="enhanced-input-group">
            {/* 历史查看提示 */}
            {totalSteps > 0 && (
              <div className="history-access-hint">
                ℹ️ 点击"执行步骤详情"可查看完整的AI执行历史和分析过程
              </div>
            )}
            
            <div>
              <label className="enhanced-label">
                您对投资策略的反馈 (可选)
              </label>
              <textarea
                value={currentInput}
                onChange={(e) => updateInput(e.target.value)}
                placeholder="请输入您的意见或补充信息..."
                className="enhanced-textarea-large"
                rows={4}
                disabled={modeConfig.readOnly}
              />
            </div>
            
            <button
              onClick={handleContinue}
              className="gradient-button-primary"
              disabled={modeConfig.readOnly}
            >
              <svg className="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
              <span>生成最终报告</span>
            </button>
          </div>
        ) : null
      }
    />
  );
}
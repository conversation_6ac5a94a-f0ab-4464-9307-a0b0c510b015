'use client';

import React, { useMemo, useCallback } from 'react';
import { useResearchV2Store } from '../../store/store';
import { StageContainer } from '../shared/StageContainer';
import { StageHeader } from '../shared/StageHeader';
import { StageStatusIndicator } from '../shared/StageStatusIndicator';
import { StageDrawer } from '../shared/StageDrawer';
import { StageContent } from '../shared/StageContent';
import { StageActions } from '../shared/StageActions';

export type StageMode = 'execution' | 'history';

export interface FactVerificationStageProps {
  mode?: StageMode;
}

/**
 * 事实核查验证阶段组件
 * 
 * v2.1: 新增模式支持
 * - execution: 执行模式，包含完整交互功能
 * - history: 历史查看模式，只读展示，隐藏交互元素
 */
export function FactVerificationStage({ mode = 'execution' }: FactVerificationStageProps) {
  const { currentSession, currentInput, updateInput, completeStage } = useResearchV2Store();
  
  // 获取事实验证阶段
  const factVerificationStage = currentSession?.stages.find(s => s.type === 'FACT_VERIFICATION');
  const researchTopic = currentSession?.topic || '';

  // 使用 useMemo 缓存计算结果，避免每次渲染时重新计算
  const stageInfo = useMemo(() => {
    const status = factVerificationStage?.status || 'PENDING';
    const steps = factVerificationStage?.steps || [];
    const totalSteps = steps.length;
    
    const info = {
      stageId: factVerificationStage?.id,
      status,
      hasSteps: steps.length > 0,
      stepCount: totalSteps,
      hasResult: !!factVerificationStage?.result,
      currentAction: factVerificationStage?.currentAction,
      progress: factVerificationStage?.progress,
      isCompleted: status === 'COMPLETED',
      isRunning: status === 'RUNNING',
      isFailed: status === 'FAILED',
      isPending: status === 'PENDING',
      totalSteps,
      steps
    };

    // 调试日志已移除 - 生产环境优化
    
    return info;
  }, [factVerificationStage, mode]);

  // 使用 useCallback 稳定回调函数引用，避免子组件不必要的重渲染
  const handleContinue = useCallback(() => {
    // 历史模式下禁用交互
    if (mode === 'history') return;
    
    completeStage('FACT_VERIFICATION', currentInput);
  }, [completeStage, currentInput, mode]);

  // 获取模式相关的样式和配置
  const getModeConfig = () => {
    if (mode === 'history') {
      return {
        containerClassName: 'opacity-90', // 略微降低透明度表示历史模式
        showActions: false,
        readOnly: true,
        headerSuffix: ' (历史记录)'
      };
    }
    
    return {
      containerClassName: '',
      showActions: true,
      readOnly: false,
      headerSuffix: ''
    };
  };

  const modeConfig = getModeConfig();

  // 如果阶段未开始，显示未开始状态
  if (!factVerificationStage) {
    return (
      <StageContainer className={modeConfig.containerClassName}>
        <div className="p-6">
          <div className="text-center text-gray-500">
            事实验证阶段尚未开始
            {mode === 'history' && (
              <div className="text-xs text-gray-400 mt-2">
                (历史查看模式)
              </div>
            )}
          </div>
        </div>
      </StageContainer>
    );
  }

  return (
    <StageContainer className={modeConfig.containerClassName}>
      {/* Header区域 */}  
      <StageHeader
        title={`事实核查验证${modeConfig.headerSuffix}`}
        description={`正在对 "${researchTopic}" 进行基础事实验证与数据收集`}
        showProgress={stageInfo.isRunning && mode === 'execution'}
        progress={stageInfo.progress}
      />

      {/* Content区域 */}
      <StageContent>
        {/* 模式提示 */}
        {mode === 'history' && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
              <span className="text-sm text-gray-700 font-medium">历史查看模式</span>
            </div>
            <p className="text-xs text-gray-600 mt-1">
              您正在查看此阶段的历史执行记录，所有交互功能已禁用
            </p>
          </div>
        )}

        {/* 状态指示器 */}
        <StageStatusIndicator
          status={stageInfo.status}
          config={{
            title: stageInfo.isRunning ? (mode === 'history' ? '曾经正在进行事实核查' : '正在进行事实核查') : 
                   stageInfo.isFailed ? '事实核查执行失败' : 
                   stageInfo.isPending ? '准备开始事实核查' : 
                   stageInfo.isCompleted ? '事实核查已完成' : '',
            description: stageInfo.isRunning ? (mode === 'history' ? '此阶段曾经在执行事实核查和数据收集' : '正在进行事实核查和数据收集，请稍候...') :
                        stageInfo.isFailed ? '执行事实核查时遇到问题，请检查网络连接或稍后重试。' :
                        stageInfo.isPending ? '系统将对研究主题进行基础事实验证与数据收集，包括信息来源验证、数据准确性检查等步骤。' : 
                        stageInfo.isCompleted ? '事实核查阶段已成功完成，所有验证工作已完成。' : '',
            currentAction: stageInfo.currentAction
          }}
        />

        {/* 执行步骤抽屉 */}
        {stageInfo.hasSteps && (
          <StageDrawer
            steps={stageInfo.steps}
            stageStatus={stageInfo.status}
          />
        )}

        {/* 结果展示 */}
        {stageInfo.isCompleted && factVerificationStage?.result && (
          <div className="completed-indicator">
            <h3 className="completed-title">
              ✅ 事实核查{mode === 'history' ? '曾经' : ''}完成
            </h3>
            <div className="completed-content">
              {factVerificationStage.result}
            </div>
          </div>
        )}
      </StageContent>

      {/* Actions区域 - 只在执行模式下显示 */}
      {modeConfig.showActions && (
        <StageActions>
          {stageInfo.isCompleted && (
            <div className="enhanced-input-group">
              {/* 历史查看提示 */}
              {stageInfo.totalSteps > 0 && (
                <div className="history-access-hint">
                  ℹ️ 点击"执行步骤详情"可查看完整的AI执行历史和分析过程
                </div>
              )}
              
              <div>
                <label className="enhanced-label">
                  您对事实核查结果的反馈 (可选)
                </label>
                <textarea
                  value={currentInput}
                  onChange={(e) => updateInput(e.target.value)}
                  placeholder="请输入您的意见或补充信息..."
                  className="enhanced-textarea-large"
                  rows={4}
                  disabled={modeConfig.readOnly}
                />
              </div>
              
              <button
                onClick={handleContinue}
                className="gradient-button-primary"
                disabled={modeConfig.readOnly}
              >
                <svg className="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
                <span>开始影响模拟分析</span>
              </button>
            </div>
          )}
        </StageActions>
      )}
    </StageContainer>
  );
}
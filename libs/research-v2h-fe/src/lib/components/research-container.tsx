'use client';

import { useEffect } from 'react';
import { useResearchV2Store } from '../store/store';
import { ResearchStageType } from '../domain/entities';
import { ResearchSidebar } from './research-sidebar';
import { IdleStage } from './stages/IdleStage';
import { FactVerificationStage } from './stages/FactVerificationStage';
import { ImpactSimulationStage } from './stages/ImpactSimulationStage';
import { ThesisRecommendationStage } from './stages/ThesisRecommendationStage';
import { FinalReportStage } from './stages/FinalReportStage';
import { ViewModeIndicator } from './shared/ViewModeIndicator';

/**
 * 研究容器组件 - 组合侧边栏和主区域，根据当前阶段显示对应内容
 * 
 * v2.1: 新增导航功能支持
 * - 根据 getDisplayStageType() 决定显示内容
 * - 支持执行模式和历史查看模式
 * - 添加查看模式指示器
 */
export function ResearchContainer() {
  const { 
    currentSession, 
    currentStageType, 
    viewMode,
    viewingStageType,
    getDisplayStageType,
    initializeService
  } = useResearchV2Store();
  
  // 在组件挂载时初始化服务
  useEffect(() => {
    initializeService();
  }, [initializeService]);

  // 获取当前应该显示的阶段类型
  const displayStageType = getDisplayStageType();

  // 根据显示阶段和模式渲染对应组件
  const renderMainArea = () => {
    if (!currentSession || !displayStageType) {
      return <IdleStage />;
    }

    // 确定组件模式：执行模式还是历史查看模式
    const componentMode = viewMode === 'history' ? 'history' : 'execution';

    switch (displayStageType) {
      case 'FACT_VERIFICATION':
        return <FactVerificationStage mode={componentMode} />;
      case 'IMPACT_SIMULATION':
        return <ImpactSimulationStage mode={componentMode} />;
      case 'THESIS_RECOMMENDATION':
        return <ThesisRecommendationStage mode={componentMode} />;
      case 'FINAL_REPORT':
        return <FinalReportStage mode={componentMode} />;
      default:
        return <IdleStage />;
    }
  };

  return (
    <div className="flex h-full bg-white">
      <ResearchSidebar />
      <main className="flex-1 p-6 overflow-y-auto">
        {/* 查看模式指示器 - 只在历史查看模式下显示 */}
        <ViewModeIndicator />
        
        {/* 主内容区域 */}
        {renderMainArea()}
      </main>
    </div>
  );
}
import React from 'react';
import '../stage-styles.css';

interface StageContentProps {
  /** 子组件内容 */
  children: React.ReactNode;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * Stage 内容区域组件
 * 职责：提供内容区域的容器样式，不包含任何业务逻辑
 */
export const StageContent: React.FC<StageContentProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`stage-content-section ${className}`.trim()}>
      {children}
    </div>
  );
};
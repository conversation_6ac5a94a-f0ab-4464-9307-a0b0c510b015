'use client';

import React from 'react';
import { AIOutput } from '../../domain/entities';

interface StepMonitorPanelProps {
  stepName: string;
  logs: AIOutput[];
}

/**
 * 步骤监控详情面板
 * 显示单个步骤的AI执行日志
 */
export function StepMonitorPanel({ 
  stepName, 
  logs
}: StepMonitorPanelProps) {

  const getLogTypeIcon = (type: AIOutput['type']) => {
    switch (type) {
      case 'thinking':
        return '🧠';
      case 'action':
        return '⚡';
      case 'result':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '💭';
    }
  };

  const getLogTypeColor = (type: AIOutput['type']) => {
    switch (type) {
      case 'thinking':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'action':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'result':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getLogTypeName = (type: AIOutput['type']) => {
    switch (type) {
      case 'thinking':
        return '思考';
      case 'action':
        return '行动';
      case 'result':
        return '结果';
      case 'error':
        return '错误';
      default:
        return '日志';
    }
  };

  if (logs.length === 0) {
    return (
      <div className="text-center py-4 text-gray-500">
        <p>暂无AI执行日志</p>
      </div>
    );
  }

  return (
    <div className="space-y-3 max-h-64 overflow-y-auto">
      {logs.map((log, index) => (
        <div key={log.id || `log-${index}`} className="flex items-start space-x-3">
          {/* 时间轴连接点 */}
          <div className="flex flex-col items-center">
            <div className="w-6 h-6 rounded-full bg-white border border-gray-200 flex items-center justify-center text-xs">
              {getLogTypeIcon(log.type)}
            </div>
            {index < logs.length - 1 && (
              <div className="w-0.5 h-4 bg-gray-200 mt-1"></div>
            )}
          </div>
          
          {/* 日志内容 */}
          <div className="flex-1 min-w-0">
            <div className={`rounded-lg border p-3 ${getLogTypeColor(log.type)}`}>
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs font-medium">
                  {getLogTypeName(log.type)}
                </span>
                <span className="text-xs opacity-75">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div className="text-xs whitespace-pre-wrap break-words">
                {log.content}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
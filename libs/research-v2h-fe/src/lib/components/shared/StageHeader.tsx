import React from 'react';
import '../stage-styles.css';

interface StageHeaderProps {
  /** 阶段标题 */
  title: string;
  /** 阶段描述 */
  description: string;
  /** 是否显示进度条 */
  showProgress?: boolean;
  /** 进度百分比 (0-100) */
  progress?: number;
}

/**
 * Stage 标题区域组件
 * 职责：显示标题、描述和可选的进度条
 */
export const StageHeader: React.FC<StageHeaderProps> = ({ 
  title, 
  description, 
  showProgress = false,
  progress 
}) => {
  // 渲染信息日志已移除 - 生产环境优化

  return (
    <div className="stage-header-section">
      <div className="flex items-center justify-between mb-3">
        <h1 className="stage-title">{title}</h1>
        {showProgress && progress !== undefined && (
          <div className="progress-container">
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${progress}%` }}
              />
            </div>
            <span className="progress-text">{progress}%</span>
          </div>
        )}
      </div>

      <div className="stage-description">
        {description}
      </div>
    </div>
  );
};
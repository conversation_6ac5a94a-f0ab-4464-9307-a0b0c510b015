'use client';

import { useState, useEffect, ReactNode } from 'react';
import { Collapsible } from '@yai-investor-insight/shared-fe-kit';
import { ExecutionStepsDisplay } from './ExecutionStepsDisplay';
import { ExecutionStep } from '../../domain/entities';
import '../stage-styles.css';

interface StageData {
  type: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
  currentAction?: string;
  progress?: number;
  steps?: ExecutionStep[];
  result?: string;
}

interface StageLayoutProps {
  title: string;
  description: string;
  stageData?: StageData;
  notStartedMessage?: string;
  renderContent?: () => ReactNode;
  renderActions?: () => ReactNode;
}

/**
 * 阶段布局通用组件
 * 抽象所有Stage组件的公共逻辑和布局
 */
export function StageLayout({
  title,
  description,
  stageData,
  notStartedMessage = "阶段尚未开始",
  renderContent,
  renderActions
}: StageLayoutProps) {
  // 直接使用status枚举，避免冗余布尔变量
  const status = stageData?.status;
  
  // 统一抽屉状态：RUNNING时展开，COMPLETED时收起
  const [isDrawerExpanded, setIsDrawerExpanded] = useState(() => {
    // 初始化抽屉状态日志已移除
    return status === 'RUNNING';
  });

  // 监听状态变化，统一展开/收起逻辑
  useEffect(() => {
    // 状态变化日志已移除
    
    if (status === 'RUNNING') {
      setIsDrawerExpanded(true);
    } else if (status === 'COMPLETED') {
      setIsDrawerExpanded(false);
    }
    // PENDING/FAILED状态保持当前状态不变
  }, [status, isDrawerExpanded]);

  // 如果阶段未开始，显示未开始状态
  if (!stageData) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">
          {notStartedMessage}
        </div>
      </div>
    );
  }

  // 计算步骤统计
  const totalSteps = stageData.steps?.length || 0;
  const completedSteps = stageData.steps?.filter(step => step.status === 'completed').length || 0;

  return (
    <div className="stage-container">
      <div className="stage-card">
        {/* Header区域 */}
        <div className="stage-header-section">
          <div className="flex items-center justify-between mb-3">
            <h1 className="stage-title">{title}</h1>
            {status === 'RUNNING' && stageData.progress !== undefined && (
              <div className="progress-container">
                <div className="progress-bar">
                  <div 
                    className="progress-fill"
                    style={{ width: `${stageData.progress}%` }}
                  />
                </div>
                <span className="progress-text">{stageData.progress}%</span>
              </div>
            )}
          </div>

          <div className="stage-description">
            {description}
          </div>
        </div>

        {/* Content区域 */}
        <div className="stage-content-section">
          {/* 当前动作显示 */}
          {status === 'RUNNING' && stageData.currentAction && (
            <div className="running-indicator">
              <div className="running-indicator-content">
                <div className="running-spinner"></div>
                <span className="running-text">{stageData.currentAction}</span>
              </div>
            </div>
          )}

          {/* 执行步骤显示 - 抽屉式 */}
          {stageData.steps && stageData.steps.length > 0 && (
            <div className="steps-section">
              <div className="drawer-container">
                <Collapsible 
                  open={isDrawerExpanded} 
                  onOpenChange={setIsDrawerExpanded}
                  trigger={
                    <div className="drawer-trigger" data-state={isDrawerExpanded ? 'open' : 'closed'}>
                      <div className="drawer-trigger-content">
                        <span className="steps-icon">📋</span>
                        <span className="drawer-trigger-text">
                          执行步骤详情 ({completedSteps}/{totalSteps})
                        </span>
                      </div>
                      <svg className="drawer-trigger-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  }
                  contentClassName="drawer-content"
                >
                  <div className="drawer-inner">
                    <ExecutionStepsDisplay 
                      steps={stageData.steps} 
                      stageStatus={status}
                    />
                  </div>
                </Collapsible>
              </div>
            </div>
          )}

          {/* 自定义内容区域 */}
          {renderContent && renderContent()}
        </div>

        {/* Action区域 */}
        {renderActions && renderActions()}
      </div>
    </div>
  );
}
'use client';

import { useResearchV2Store } from '../../store/store';
import { getStageConfig } from '../../configs/research-stages.config';

// 简单的图标组件
const HistoryIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const UpdateIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
);

/**
 * 查看模式指示器组件
 * 
 * 功能：
 * 1. 在历史查看模式下显示提示横幅
 * 2. 显示当前查看的阶段信息
 * 3. 提供"回到当前"快速切换按钮
 * 4. 显示新进展提示（当执行阶段与查看阶段不同时）
 */
export function ViewModeIndicator() {
  const { 
    viewMode, 
    viewingStageType, 
    currentStageType,
    exitNavigation
  } = useResearchV2Store();

  // 只在历史查看模式下显示
  if (viewMode !== 'history' || !viewingStageType) {
    return null;
  }

  const viewingStageConfig = getStageConfig(viewingStageType);
  const hasNewProgress = currentStageType !== viewingStageType;

  return (
    <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6 rounded-r-lg">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <HistoryIcon className="h-5 w-5 text-blue-600" />
          <div>
            <p className="text-sm font-medium text-blue-800">
              正在查看历史记录
            </p>
            <p className="text-xs text-blue-600">
              {viewingStageConfig?.name} - 已完成阶段
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* 新进展提示 */}
          {hasNewProgress && (
            <div className="flex items-center space-x-1 text-orange-600 bg-orange-50 px-2 py-1 rounded">
              <UpdateIcon className="h-4 w-4" />
              <span className="text-xs font-medium">有新进展</span>
            </div>
          )}
          
          {/* 回到当前按钮 */}
          <button
            onClick={() => exitNavigation()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
          >
            回到当前
          </button>
        </div>
      </div>
      
      {/* 额外的进展信息 */}
      {hasNewProgress && (
        <div className="mt-2 pt-2 border-t border-blue-200">
          <p className="text-xs text-blue-700">
            当前执行阶段: {currentStageType ? getStageConfig(currentStageType)?.name : '无'}
          </p>
        </div>
      )}
    </div>
  );
}
import React from 'react';
import '../stage-styles.css';

type StageStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';

interface StatusConfig {
  /** 状态标题 */
  title: string;
  /** 状态描述 */
  description?: string;
  /** 运行中的具体动作描述 */
  currentAction?: string;
}

interface StageStatusIndicatorProps {
  /** 当前阶段状态 */
  status: StageStatus;
  /** 状态配置信息 */
  config: StatusConfig;
}

/**
 * Stage 状态指示器组件
 * 职责：根据状态显示相应的状态指示器（运行中、失败、等待等）
 */
export const StageStatusIndicator: React.FC<StageStatusIndicatorProps> = ({ 
  status, 
  config 
}) => {
  // 状态信息日志已移除 - 生产环境优化

  // 运行中状态
  if (status === 'RUNNING') {
    return (
      <div className="running-indicator">
        <div className="running-indicator-content">
          <div className="running-spinner"></div>
          <div className="running-text">
            {config.currentAction || config.description || '正在处理中，请稍候...'}
          </div>
        </div>
      </div>
    );
  }

  // 失败状态
  if (status === 'FAILED') {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="font-semibold text-red-800">{config.title}</h3>
        </div>
        <p className="text-sm text-red-700 mb-3">
          {config.description || '执行过程中遇到问题，请检查网络连接或稍后重试。'}
        </p>
      </div>
    );
  }

  // 等待状态
  if (status === 'PENDING') {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="font-semibold text-blue-800">{config.title}</h3>
        </div>
        <p className="text-sm text-blue-700">
          {config.description || '准备开始执行，请稍候...'}
        </p>
      </div>
    );
  }

  // COMPLETED 状态或其他状态不显示状态指示器
  return null;
};
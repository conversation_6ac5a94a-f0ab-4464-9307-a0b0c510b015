'use client';

import React, { useState } from 'react';
import { ExecutionStep } from '../../domain/entities';
import { StepMonitorPanel } from './StepMonitorPanel';

interface ExecutionStepsDisplayProps {
  /** 执行步骤列表 */
  steps: ExecutionStep[];
  /** 是否显示进度条 */
  showProgress?: boolean;
  /** 阶段状态，用于统一抽屉展开/收起行为 */
  stageStatus?: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
}

/**
 * 执行步骤展示组件
 * 用于显示当前阶段的执行步骤列表和详细信息
 */
export function ExecutionStepsDisplay({ 
  steps,
  showProgress = true,
  stageStatus
}: ExecutionStepsDisplayProps) {
  // 统一抽屉行为：基于阶段状态和步骤状态决定是否展开
  const getInitialExpandedSteps = () => {
    if (stageStatus === 'RUNNING') {
      // RUNNING状态：展开正在运行的步骤
      return steps.filter(step => step.status === 'running').map(step => step.id);
    } else if (stageStatus === 'COMPLETED') {
      // COMPLETED状态：收起所有步骤
      return [];
    }
    // PENDING/FAILED状态：保持收起
    return [];
  };
  
  const [expandedStepIds, setExpandedStepIds] = useState<string[]>(getInitialExpandedSteps);
  
  // 监听阶段状态变化，统一调整抽屉
  React.useEffect(() => {
    // 阶段状态变化日志已移除 - 生产环境优化
    
    if (stageStatus === 'RUNNING') {
      // 展开正在运行的步骤
      const runningStepIds = steps.filter(step => step.status === 'running').map(step => step.id);
      setExpandedStepIds(runningStepIds);
    } else if (stageStatus === 'COMPLETED') {
      // 收起所有步骤
      setExpandedStepIds([]);
    }
  }, [stageStatus, steps]);

  if (!steps || steps.length === 0) {
    return (
      <div className="text-center text-gray-500 py-4">
        暂无执行步骤
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {steps.map((step, index) => (
        <div key={step.id || `step-${index}`} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {/* 步骤状态图标 */}
              <div className="flex-shrink-0">
                {step.status === 'completed' && (
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                )}
                {step.status === 'running' && (
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                  </div>
                )}
                {step.status === 'pending' && (
                  <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                  </div>
                )}
                {step.status === 'failed' && (
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                )}
              </div>

              {/* 步骤信息 */}
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900">{step.name}</span>
                  <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                    {step.type}
                  </span>
                </div>
                {step.description && (
                  <div className="text-sm text-gray-600 mt-1">
                    {step.description}
                  </div>
                )}
              </div>
            </div>

            {/* 查看详情按钮 */}
            {step.aiOutputs && step.aiOutputs.length > 0 && (
              <button
                onClick={() => {
                  const isExpanded = expandedStepIds.includes(step.id);
                  if (isExpanded) {
                    setExpandedStepIds(prev => prev.filter(id => id !== step.id));
                  } else {
                    setExpandedStepIds(prev => [...prev, step.id]);
                  }
                }}
                className="text-sm text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50"
              >
                {expandedStepIds.includes(step.id) ? '收起' : '详情'}
              </button>
            )}
          </div>

          {/* AI输出日志详情 */}
          {expandedStepIds.includes(step.id) && step.aiOutputs && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <StepMonitorPanel 
                logs={step.aiOutputs}
                stepName={step.name}
              />
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
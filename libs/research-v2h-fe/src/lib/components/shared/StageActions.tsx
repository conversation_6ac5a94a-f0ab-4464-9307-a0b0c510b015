import React from 'react';
import '../stage-styles.css';

interface StageActionsProps {
  /** 子组件内容 */
  children: React.ReactNode;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * Stage 操作区域组件
 * 职责：提供操作区域的容器样式，不包含任何业务逻辑
 */
export const StageActions: React.FC<StageActionsProps> = ({ 
  children, 
  className = '' 
}) => {
  // 如果没有子组件，不渲染任何内容
  if (!children) {
    return null;
  }

  return (
    <div className={`stage-action-section ${className}`.trim()}>
      {children}
    </div>
  );
};
import React from 'react';
import '../stage-styles.css';

interface StageContainerProps {
  /** 子组件内容 */
  children: React.ReactNode;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * Stage 基础容器组件
 * 职责：提供统一的外层容器样式，不包含任何业务逻辑
 */
export const StageContainer: React.FC<StageContainerProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`stage-container ${className}`.trim()}>
      <div className="stage-card">
        {children}
      </div>
    </div>
  );
};
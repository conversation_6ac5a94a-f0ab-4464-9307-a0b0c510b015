'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Collapsible } from '@yai-investor-insight/shared-fe-kit';
import { ExecutionStepsDisplay } from './ExecutionStepsDisplay';
import { ExecutionStep } from '../../domain/entities';
import '../stage-styles.css';

type StageStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';

interface StageDrawerProps {
  /** 执行步骤列表 */
  steps: ExecutionStep[];
  /** 阶段状态，用于控制抽屉展开/收起行为 */
  stageStatus: StageStatus;
  /** 自定义触发器文本 */
  triggerText?: string;
}

/**
 * Stage 抽屉组件
 * 职责：管理抽屉展开/收起状态，显示执行步骤详情
 */
export const StageDrawer = React.memo<StageDrawerProps>(({ 
  steps, 
  stageStatus, 
  triggerText = "执行步骤详情" 
}) => {
  // 使用 useMemo 缓存步骤统计，避免每次渲染时重新计算
  const stepStats = useMemo(() => {
    const totalSteps = steps.length;
    const completedSteps = steps.filter(step => step.status === 'completed').length;
    return { totalSteps, completedSteps };
  }, [steps]);

  // 抽屉状态：RUNNING时展开，COMPLETED时收起
  const [isExpanded, setIsExpanded] = useState(() => {
    const initialState = stageStatus === 'RUNNING';
    // 初始化抽屉状态日志已移除
    return initialState;
  });

  // 监听阶段状态变化，自动调整抽屉状态
  useEffect(() => {
    // 状态变化日志已移除 - 生产环境优化
    
    if (stageStatus === 'RUNNING') {
      setIsExpanded(true);
    } else if (stageStatus === 'COMPLETED') {
      setIsExpanded(false);
    }
    // PENDING/FAILED状态保持当前状态不变
  }, [stageStatus, isExpanded]);

  // 如果没有步骤，不显示抽屉
  if (!steps || steps.length === 0) {
    return null;
  }

  return (
    <div className="steps-section">
      <div className="drawer-container">
        <Collapsible 
          open={isExpanded} 
          onOpenChange={setIsExpanded}
          trigger={
            <div className="drawer-trigger" data-state={isExpanded ? 'open' : 'closed'}>
              <div className="drawer-trigger-content">
                <span className="steps-icon">📋</span>
                <span className="drawer-trigger-text">
                  {triggerText} ({stepStats.completedSteps}/{stepStats.totalSteps})
                </span>
              </div>
              <svg className="drawer-trigger-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          }
          contentClassName="drawer-content"
        >
          <div className="drawer-inner">
            <ExecutionStepsDisplay 
              steps={steps} 
              stageStatus={stageStatus}
            />
          </div>
        </Collapsible>
      </div>
    </div>
  );
});

StageDrawer.displayName = 'StageDrawer';
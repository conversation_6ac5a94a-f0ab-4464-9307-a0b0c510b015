/**
 * Research Sidebar Styles - Tailwind CSS v4
 * 
 * 服务组件：research-sidebar.tsx
 * 
 * 专用于ResearchSidebar组件的复杂样式
 * 防止重构时意外修改视觉效果
 * 
 * 技术特点:
 * - 使用 Tailwind CSS v4 的 @theme 和 @layer 语法
 * - CSS-first 配置方式
 * - 利用现代CSS特性：嵌套、容器查询、backdrop-filter
 */

@import "tailwindcss";

/* 设计系统 Token 定义 */
@theme {
  /* 侧边栏专用颜色 */
  --color-sidebar-bg-from: #f8fafc;  /* slate-50 */
  --color-sidebar-bg-via: rgba(239, 246, 255, 0.3);  /* blue-50/30 */
  --color-sidebar-bg-to: rgba(238, 242, 255, 0.5);   /* indigo-50/50 */
  
  /* 玻璃态效果 */
  --color-glass-bg: rgba(255, 255, 255, 0.8);
  --color-glass-border: rgba(255, 255, 255, 0.6);
  --backdrop-blur-glass: 12px;
  
  /* 渐变按钮颜色 */
  --color-button-from: #3b82f6;    /* blue-500 */
  --color-button-via: #2563eb;     /* blue-600 */  
  --color-button-to: #4f46e5;      /* indigo-600 */
  --color-button-hover-from: #2563eb;  /* blue-600 */
  --color-button-hover-via: #1d4ed8;   /* blue-700 */
  --color-button-hover-to: #4338ca;    /* indigo-700 */
  
  /* 装饰元素颜色 */
  --color-decoration-from: rgba(219, 234, 254, 0.4);  /* blue-100/40 */
  --color-decoration-to: rgba(199, 210, 254, 0.3);    /* indigo-100/30 */
  
  /* 动画时长 */
  --duration-fast: 200ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  
  /* 间距 */
  --spacing-sidebar-width: 20rem;
}

/* 组件样式层 */
@layer components {
  
  /* 侧边栏主容器 - 渐变背景 + 装饰元素 */
  .sidebar-container {
    @apply w-80 border-r p-6 flex flex-col relative;
    width: var(--spacing-sidebar-width);
    border-color: rgba(229, 231, 235, 0.6); /* gray-200/60 */
    background: linear-gradient(to bottom, 
      var(--color-sidebar-bg-from), 
      var(--color-sidebar-bg-via), 
      var(--color-sidebar-bg-to));
  }
  
  /* 背景装饰元素 */
  .sidebar-decoration-top {
    @apply absolute top-0 right-0 w-32 h-32 rounded-bl-full;
    background: linear-gradient(to bottom left, 
      var(--color-decoration-from), 
      transparent);
  }
  
  .sidebar-decoration-bottom {
    @apply absolute bottom-20 left-0 w-24 h-24 rounded-tr-full;
    background: linear-gradient(to top right, 
      var(--color-decoration-to), 
      transparent);
  }
  
  /* 玻璃态卡片效果 */
  .glass-card {
    @apply rounded-2xl p-5 shadow-lg border transition-all relative;
    background: var(--color-glass-bg);
    backdrop-filter: blur(var(--backdrop-blur-glass));
    border-color: var(--color-glass-border);
    transition-duration: var(--duration-normal);
    z-index: 10;
  }
  
  .glass-card:hover {
    @apply shadow-xl;
  }
  
  /* 渐变按钮系统 */
  .gradient-button {
    @apply w-full py-3.5 px-4 rounded-xl text-sm font-medium text-white;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply transition-all shadow-lg;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    background: linear-gradient(to right, 
      var(--color-button-from), 
      var(--color-button-via), 
      var(--color-button-to));
    transition-duration: var(--duration-normal);
    focus-ring-color: rgba(59, 130, 246, 0.5); /* blue-500/50 */
  }
  
  .gradient-button:hover:not(:disabled) {
    @apply shadow-xl transform -translate-y-0.5;
    background: linear-gradient(to right, 
      var(--color-button-hover-from), 
      var(--color-button-hover-via), 
      var(--color-button-hover-to));
  }
  
  .gradient-button:active:not(:disabled) {
    @apply translate-y-0;
  }
  
  /* 阶段图标系统 - 进一步解耦状态样式 */
  .stage-icon {
    @apply w-10 h-10 rounded-full flex items-center justify-center shadow-lg;
    @apply transition-all flex-shrink-0;
    transition-duration: var(--duration-normal);
  }
  
  /* 已完成状态图标 */
  .stage-icon.completed {
    @apply text-white;
    background: linear-gradient(to right, 
      theme(colors.green.500), 
      theme(colors.emerald.500));
  }
  
  /* 运行中状态图标 */
  .stage-icon.running {
    @apply text-white relative;
    background: linear-gradient(to right, 
      theme(colors.blue.500), 
      theme(colors.indigo.500));
  }
  
  .stage-icon.running::after {
    @apply absolute inset-0 rounded-full border-2 animate-ping;
    border-color: theme(colors.blue.300);
    content: '';
  }
  
  /* 失败状态图标 */
  .stage-icon.failed {
    @apply text-white;
    background: linear-gradient(to right, 
      theme(colors.red.500), 
      theme(colors.rose.500));
  }
  
  /* 等待状态图标 */
  .stage-icon.pending {
    @apply text-gray-600 border-2 border-white;
    background: linear-gradient(to right, 
      theme(colors.gray.300), 
      theme(colors.gray.400));
  }
  
  /* 图标hover效果 */
  .stage-icon:hover {
    @apply shadow-xl transform scale-105;
  }
  
  /* 进度环状态样式 */
  .stage-icon .progress-ring {
    @apply text-current;
  }
  
  .stage-icon.completed .progress-ring {
    color: theme(colors.green.400);
  }
  
  .stage-icon.running .progress-ring {
    color: theme(colors.blue.300);
  }
  
  .stage-icon.failed .progress-ring {
    color: theme(colors.red.400);
  }
  
  /* 连接线系统 */
  .stage-connector {
    @apply w-0.5 h-8 ml-5 mt-3 rounded-full transition-colors;
    transition-duration: var(--duration-normal);
  }
  
  .stage-connector.completed {
    background: linear-gradient(to bottom, 
      theme(colors.blue.300), 
      theme(colors.indigo.300));
  }
  
  .stage-connector.active {
    background: linear-gradient(to bottom, 
      theme(colors.green.300), 
      theme(colors.emerald.300));
  }
  
  .stage-connector.pending {
    @apply bg-gray-200;
  }
  
  /* 导航按钮样式 */
  .nav-button {
    @apply inline-flex items-center space-x-2 text-sm transition-colors;
    @apply text-gray-600 hover:text-blue-600 cursor-pointer;
    transition-duration: var(--duration-fast);
  }
  
  .nav-button svg {
    @apply w-4 h-4 transition-transform;
    transition-duration: var(--duration-fast);
  }
  
  .nav-button:hover svg {
    @apply -translate-x-0.5;
  }
  
  /* 标题区域装饰 */
  .section-title-icon {
    @apply w-6 h-6 rounded-lg flex items-center justify-center;
  }
  
  .section-title-icon.primary {
    background: linear-gradient(to right, 
      theme(colors.blue.500), 
      theme(colors.indigo.500));
  }
  
  .section-title-icon.secondary {
    background: linear-gradient(to right, 
      theme(colors.indigo.500), 
      theme(colors.purple.500));
  }
  
  /* 输入区域增强样式 */
  .enhanced-textarea {
    @apply w-full p-4 border rounded-xl text-sm resize-none transition-all;
    @apply focus:outline-none focus:ring-2 focus:border-blue-300;
    @apply bg-gray-50/50 hover:bg-white placeholder-gray-400;
    border-color: rgba(229, 231, 235, 0.6); /* gray-200/60 */
    focus-ring-color: rgba(59, 130, 246, 0.5); /* blue-500/50 */
    transition-duration: var(--duration-fast);
  }
  
}

/* 防止样式被意外修改的保护注释 */
/*
=== 重要提醒 ===
此文件包含ResearchSidebar的核心视觉样式，请勿在架构重构时修改！

如需修改样式，请：
1. 先在 discuss/ 目录创建设计方案文档
2. 与用户确认后再修改
3. 保持Tailwind v4的CSS-first最佳实践

主要样式组件：
- .sidebar-container: 主容器渐变背景
- .glass-card: 玻璃态卡片效果  
- .gradient-button: 渐变按钮动画
- .stage-icon: 状态图标系统
- .stage-connector: 连接线动画
*/
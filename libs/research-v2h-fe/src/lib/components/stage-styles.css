/**
 * Research Stage Components Styles - Tailwind CSS v4
 * 
 * 服务组件：StageLayout.tsx 和所有继承它的Stage组件
 * - FactVerificationStage.tsx
 * - ImpactSimulationStage.tsx
 * - ThesisRecommendationStage.tsx
 * - FinalReportStage.tsx
 * 
 * 专用于Research阶段组件的复杂样式
 * 防止重构时意外修改视觉效果
 * 
 * 技术特点:
 * - 使用 Tailwind CSS v4 的 @theme 和 @layer 语法
 * - CSS-first 配置方式
 * - 利用现代CSS特性：嵌套、容器查询、backdrop-filter
 * - 支持抽屉折叠、渐变动画、专业按钮设计
 */

@import "tailwindcss";

/* 设计系统 Token 定义 */
@theme {
  /* 阶段组件专用颜色 */
  --color-stage-bg: #ffffff;
  --color-stage-border: rgba(229, 231, 235, 0.6); /* gray-200/60 */
  
  /* 抽屉系统颜色 */
  --color-drawer-bg: #ffffff;
  --color-drawer-border: rgba(243, 244, 246, 1); /* gray-100 */
  --color-drawer-shadow: rgba(0, 0, 0, 0.05);
  
  /* 渐变按钮系统 */
  --color-primary-from: #3b82f6;    /* blue-500 */
  --color-primary-via: #2563eb;     /* blue-600 */  
  --color-primary-to: #4f46e5;      /* indigo-600 */
  --color-primary-hover-from: #2563eb;  /* blue-600 */
  --color-primary-hover-via: #1d4ed8;   /* blue-700 */
  --color-primary-hover-to: #4338ca;    /* indigo-700 */
  
  /* 状态指示颜色 */
  --color-running-bg: rgba(59, 130, 246, 0.1);   /* blue-500/10 */
  --color-running-border: rgba(59, 130, 246, 0.2); /* blue-500/20 */
  --color-running-text: #1d4ed8;     /* blue-700 */
  
  --color-completed-bg: rgba(34, 197, 94, 0.1);   /* green-500/10 */
  --color-completed-border: rgba(34, 197, 94, 0.2); /* green-500/20 */
  --color-completed-text: #15803d;    /* green-700 */
  
  /* 二级按钮颜色 */
  --color-secondary-from: #6b7280;   /* gray-500 */
  --color-secondary-to: #4b5563;     /* gray-600 */
  --color-secondary-hover-from: #4b5563; /* gray-600 */
  --color-secondary-hover-to: #374151;   /* gray-700 */
  
  /* 动画时长 */
  --duration-fast: 200ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  
  /* 抽屉动画 */
  --drawer-slide-distance: 8px;
  --drawer-max-height: 400px;
}

/* 组件样式层 */
@layer components {
  
  /* 阶段主容器 - 分段式专业布局 */
  .stage-container {
    @apply mb-7;
  }
  
  .stage-card {
    @apply rounded-xl overflow-hidden transition-all shadow-md;
    border: 1px solid var(--color-stage-border);
    transition-duration: var(--duration-normal);
  }
  
  .stage-card:hover {
    @apply shadow-lg;
  }

  /* 三段式布局 - Header区域 */
  .stage-header-section {
    @apply px-6 py-5;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%); /* gray-800 to gray-700 */
    color: #ffffff;
    border-bottom: 2px solid #4b5563; /* gray-600 */
  }

  .stage-header-section .stage-title {
    @apply text-xl font-bold text-white mb-2;
  }

  .stage-header-section .stage-description {
    @apply text-gray-200 leading-relaxed text-sm;
  }

  .stage-header-section .stage-topic-highlight {
    @apply font-semibold text-blue-200;
  }

  .stage-header-section .progress-container {
    @apply flex items-center space-x-2;
  }

  .stage-header-section .progress-bar {
    @apply w-32 h-2 bg-gray-600 rounded-full overflow-hidden;
  }

  .stage-header-section .progress-text {
    @apply text-sm font-medium text-gray-200;
  }

  /* 三段式布局 - Content区域 */
  .stage-content-section {
    @apply px-6 py-5 bg-white;
    min-height: 120px;
  }

  /* 三段式布局 - Action区域 */
  .stage-action-section {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
  }
  
  /* 原有样式保留作为备用（兼容性） */
  .stage-header {
    @apply p-6 pb-4;
  }
  
  .stage-title {
    @apply text-2xl font-bold text-gray-900 mb-2;
  }
  
  .stage-description {
    @apply text-gray-600 leading-relaxed;
  }
  
  .stage-topic-highlight {
    @apply font-semibold text-gray-900;
  }
  
  /* 运行状态指示器 */
  .running-indicator {
    @apply rounded-md p-3 mb-4;
    background: var(--color-running-bg);
    border: 1px solid var(--color-running-border);
  }
  
  .running-indicator-content {
    @apply flex items-center space-x-2;
  }
  
  .running-spinner {
    @apply w-4 h-4 border-2 rounded-full animate-spin;
    border-color: var(--color-running-text);
    border-top-color: transparent;
  }
  
  .running-text {
    @apply text-sm;
    color: var(--color-running-text);
  }
  
  /* 完成状态指示器 */
  .completed-indicator {
    @apply rounded-md p-4 mb-6;
    background: var(--color-completed-bg);
    border: 1px solid var(--color-completed-border);
  }
  
  .completed-title {
    @apply text-lg font-semibold mb-2;
    color: var(--color-completed-text);
  }
  
  .completed-content {
    @apply text-sm whitespace-pre-wrap leading-relaxed;
    color: var(--color-completed-text);
  }
  
  /* 抽屉系统 */
  .drawer-container {
    @apply rounded-xl border overflow-hidden transition-all;
    background: var(--color-drawer-bg);
    border-color: var(--color-drawer-border);
    box-shadow: 0 1px 3px var(--color-drawer-shadow);
    transition-duration: var(--duration-normal);
  }
  
  .drawer-container:hover {
    @apply shadow-md;
  }
  
  .drawer-trigger {
    @apply w-full p-4 text-left bg-transparent border-none cursor-pointer;
    @apply flex items-center justify-between;
    @apply hover:bg-gray-50 transition-colors;
    transition-duration: var(--duration-fast);
  }
  
  .drawer-trigger-content {
    @apply flex items-center space-x-3;
  }
  
  .drawer-trigger-icon {  
    @apply w-5 h-5 text-gray-400 transition-transform;
    transition-duration: var(--duration-fast);
  }
  
  .drawer-trigger[data-state="open"] .drawer-trigger-icon {
    @apply rotate-180;
  }
  
  .drawer-trigger-text {
    @apply font-medium text-gray-900;
  }
  
  .drawer-content {
    @apply overflow-hidden transition-all;
    transition-duration: var(--duration-normal);
  }
  
  .drawer-content[data-state="closed"] {
    max-height: 0;
    opacity: 0;
    transform: translateY(calc(-1 * var(--drawer-slide-distance)));
  }
  
  .drawer-content[data-state="open"] {
    max-height: var(--drawer-max-height);
    opacity: 1;
    transform: translateY(0);
  }
  
  .drawer-inner {
    @apply p-4 pt-2 border-t border-gray-100;
    animation: drawer-fade-in var(--duration-normal) ease-out;
  }
  
  /* 渐变按钮系统 */
  .gradient-button-primary {
    @apply w-full py-3.5 px-4 rounded-xl text-sm font-medium text-white;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply transition-all shadow-lg;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    @apply flex items-center justify-center space-x-2;
    background: linear-gradient(to right, 
      var(--color-primary-from), 
      var(--color-primary-via), 
      var(--color-primary-to));
    transition-duration: var(--duration-normal);
    focus-ring-color: rgba(59, 130, 246, 0.5); /* blue-500/50 */
  }
  
  .gradient-button-primary:hover:not(:disabled) {
    @apply shadow-xl transform -translate-y-0.5;
    background: linear-gradient(to right, 
      var(--color-primary-hover-from), 
      var(--color-primary-hover-via), 
      var(--color-primary-hover-to));
  }
  
  .gradient-button-primary:active:not(:disabled) {
    @apply translate-y-0;
  }
  
  /* 二级按钮 */
  .gradient-button-secondary {
    @apply px-6 py-2 rounded-md text-white;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply transition-all shadow-md;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    background: linear-gradient(to right, 
      var(--color-secondary-from), 
      var(--color-secondary-to));
    transition-duration: var(--duration-normal);
    focus-ring-color: rgba(107, 114, 128, 0.5); /* gray-500/50 */
  }
  
  .gradient-button-secondary:hover:not(:disabled) {
    @apply shadow-lg;
    background: linear-gradient(to right, 
      var(--color-secondary-hover-from), 
      var(--color-secondary-hover-to));
  }
  
  /* 按钮图标 */
  .button-icon {
    @apply w-4 h-4 transition-transform;
    transition-duration: var(--duration-fast);
  }
  
  .gradient-button-primary:hover .button-icon {
    @apply scale-105;
  }
  
  /* 输入区域增强 */
  .enhanced-input-group {
    @apply space-y-4;
  }
  
  .enhanced-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .enhanced-textarea-large {
    @apply w-full p-4 border rounded-xl text-sm resize-none transition-all;
    @apply focus:outline-none focus:ring-2 focus:border-blue-300;
    @apply bg-gray-50/50 hover:bg-white placeholder-gray-400;
    border-color: rgba(229, 231, 235, 0.6); /* gray-200/60 */
    focus-ring-color: rgba(59, 130, 246, 0.5); /* blue-500/50 */
    transition-duration: var(--duration-fast);
  }
  
  /* 进度条增强 */
  .progress-container {
    @apply flex items-center space-x-2;
  }
  
  .progress-bar {
    @apply w-32 h-2 bg-gray-200 rounded-full overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full transition-all duration-300;
    background: linear-gradient(to right, 
      var(--color-primary-from), 
      var(--color-primary-to));
  }
  
  .progress-text {
    @apply text-sm font-medium text-gray-600;
  }
  
  /* 步骤详情区域 */
  .steps-section {
    @apply mb-6;
  }
  
  .steps-header {
    @apply flex items-center space-x-2 text-sm font-medium text-gray-700 mb-4;
  }
  
  .steps-icon {
    @apply text-base;
  }
  
  /* 操作按钮组 */
  .action-button-group {
    @apply flex items-center justify-center space-x-4;
  }
  
  .action-description {
    @apply text-center text-sm text-gray-500 mt-4;
  }
  
  /* 历史查看提示 */
  .history-access-hint {
    @apply text-sm text-blue-600 bg-blue-50 p-3 rounded-lg mb-4 border border-blue-200;
  }
  
  /* 特殊效果 - 最终报告渐变背景 */
  .final-report-card {
    @apply rounded-lg p-6 mb-6;
    background: linear-gradient(to right, 
      rgba(34, 197, 94, 0.1), 
      rgba(59, 130, 246, 0.1));
    border: 1px solid rgba(34, 197, 94, 0.2);
  }
  
  .final-report-title {
    @apply text-xl font-bold mb-4;
    background: linear-gradient(to right, 
      theme(colors.green.700), 
      theme(colors.blue.700));
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }
  
  .final-report-content {
    @apply text-sm whitespace-pre-wrap leading-relaxed text-gray-700;
  }
  
}

/* 动画定义 */
@keyframes drawer-fade-in {
  from {
    opacity: 0;
    transform: translateY(calc(-1 * var(--drawer-slide-distance)));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 防止样式被意外修改的保护注释 */
/*
=== 重要提醒 ===
此文件包含Research阶段组件的核心视觉样式，请勿在架构重构时修改！

如需修改样式，请：
1. 先在 discuss/ 目录创建设计方案文档
2. 与用户确认后再修改
3. 保持Tailwind v4的CSS-first最佳实践

主要样式组件：
- .stage-container/.stage-card: 阶段容器和卡片
- .drawer-*: 抽屉折叠系统
- .gradient-button-*: 渐变按钮动画
- .running-indicator/.completed-indicator: 状态指示器
- .enhanced-*: 增强输入组件
- .final-report-*: 最终报告特殊效果
*/
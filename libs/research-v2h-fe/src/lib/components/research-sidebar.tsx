'use client';

import { useState } from 'react';
import { useResearchV2Store } from '../store/store';
import { ResearchStageType, StageStatus } from '../domain/entities';
import { getStageConfig, getOrderedStageConfigs } from '../configs/research-stages.config';
import { StageIcon } from './shared/StageIcon';
import { BackArrowIcon, SearchIcon, LightningIcon, ChartIcon, SpinnerIcon } from '../icons/research-stage-icons';
import './sidebar-styles.css';

export function ResearchSidebar() {
  const { 
    currentSession, 
    startResearch, 
    isLoading,
    // 新增导航相关状态和方法
    viewMode,
    viewingStageType,
    currentStageType,
    navigateToStage,
    isStageClickable,
    getStageDisplayStatus
  } = useResearchV2Store();
  
  const [topic, setTopic] = useState('');

  const handleStartResearch = () => {
    if (topic.trim()) {
      startResearch(topic.trim());
    }
  };

  const handleBackToHome = () => {
    window.location.href = '/';
  };

  // 处理阶段点击
  const handleStageClick = (stageType: ResearchStageType) => {
    // 检查阶段是否可点击
    if (!isStageClickable(stageType)) {
      return;
    }
    
    // 如果点击的是当前正在执行的阶段，不需要切换（或者可以切换回执行模式）
    if (currentStageType === stageType && viewMode === 'history') {
      // 如果当前在历史模式查看执行中的阶段，切换回执行模式
      const { exitNavigation } = useResearchV2Store.getState();
      exitNavigation();
      return;
    }
    
    // 导航到指定阶段
    navigateToStage(stageType);
  };

  // 获取阶段状态
  const getStageStatus = (stageType: ResearchStageType): StageStatus => {
    if (!currentSession) return 'PENDING';
    const stage = currentSession.stages.find(s => s.type === stageType);
    return stage?.status || 'PENDING';
  };

  // 使用配置系统获取阶段信息 - 配置驱动
  const getStageInfo = (stageType: ResearchStageType) => {
    return getStageConfig(stageType);
  };

  // 获取阶段进度
  const getStageProgress = (stageType: ResearchStageType): number => {
    if (!currentSession) return 0;
    const stage = currentSession.stages.find(s => s.type === stageType);
    return stage?.progress || 0;
  };

  // 使用配置驱动的图标渲染 - 完全解耦SVG
  const renderStageIcon = (stageType: ResearchStageType) => {
    const status = getStageStatus(stageType);
    const progress = getStageProgress(stageType);
    
    return (
      <StageIcon
        stageType={stageType}
        status={status}
        progress={progress}
        showProgressRing={status === 'RUNNING'}
      />
    );
  };

  // 获取连接线状态
  const getConnectorStatus = (stageType: ResearchStageType, index: number, stages: ResearchStageType[]): string => {
    const currentStageStatus = getStageStatus(stageType);
    if (currentStageStatus === 'COMPLETED') {
      // 如果不是最后一个阶段，检查下一个阶段
      if (index < stages.length - 1) {
        const nextStageStatus = getStageStatus(stages[index + 1]);
        return nextStageStatus === 'COMPLETED' ? 'active' : 'completed';
      }
      return 'active'; // 最后一个阶段完成
    }
    return 'pending';
  };

  // 获取阶段项的CSS类名
  const getStageItemClassName = (stageType: ResearchStageType): string => {
    const displayStatus = getStageDisplayStatus(stageType);
    const baseClasses = 'flex items-start space-x-4 group transition-all duration-200';
    
    switch (displayStatus) {
      case 'pending':
        return `${baseClasses} stage-item-pending opacity-50`;
      case 'available':
        return `${baseClasses} stage-item-available cursor-pointer hover:bg-gray-50 hover:transform hover:translate-x-1 rounded-lg p-2 -ml-2`;
      case 'current-execution':
        return `${baseClasses} stage-item-current-execution bg-blue-50 border-l-4 border-blue-500 rounded-lg p-2 -ml-2`;
      case 'current-viewing':
        return `${baseClasses} stage-item-current-viewing bg-indigo-50 border border-indigo-300 rounded-lg p-2 -ml-2 shadow-md`;
      default:
        return baseClasses;
    }
  };

  // 获取阶段标题的CSS类名
  const getStageTitleClassName = (stageType: ResearchStageType): string => {
    const displayStatus = getStageDisplayStatus(stageType);
    const baseClasses = 'font-semibold text-base transition-colors';
    
    switch (displayStatus) {
      case 'pending':
        return `${baseClasses} text-gray-400`;
      case 'available':
        return `${baseClasses} text-gray-700 group-hover:text-gray-900`;
      case 'current-execution':
        return `${baseClasses} text-blue-800`;
      case 'current-viewing':
        return `${baseClasses} text-indigo-800`;
      default:
        return `${baseClasses} text-gray-700`;
    }
  };

  // 使用配置系统获取排序后的阶段列表 - 配置驱动
  const orderedStageConfigs = getOrderedStageConfigs();

  const currentStage = currentSession?.status || 'IDLE';

  return (
    <div className="sidebar-container">
      {/* 背景装饰 */}
      <div className="sidebar-decoration-top"></div>
      <div className="sidebar-decoration-bottom"></div>
      
      {/* 回到首页按钮 */}
      <div className="mb-4 relative z-10">
        <button onClick={handleBackToHome} className="nav-button">
          <BackArrowIcon />
          <span className="font-medium">回到首页</span>
        </button>
      </div>
      
      {/* 研究主题输入区 */}
      <div className="mb-6 relative z-10">
        <div className="glass-card">
          <div className="flex items-center space-x-2 mb-4">
            <div className="section-title-icon primary">
              <SearchIcon className="w-4 h-4 text-white" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">研究主题</h2>
          </div>
          
          {!currentSession ? (
            <div className="space-y-4">
              <textarea
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                className="enhanced-textarea"
                placeholder="请输入要研究的公司或主题..."
                rows={3}
                disabled={isLoading}
              />
              <button 
                onClick={handleStartResearch}
                disabled={!topic.trim() || isLoading}
                className="gradient-button"
              >
                <span className="flex items-center justify-center space-x-2">
                  {!isLoading ? (
                    <>
                      <LightningIcon />
                      <span>开始研究</span>
                    </>
                  ) : (
                    <>
                      <SpinnerIcon />
                      <span>研究进行中...</span>
                    </>
                  )}
                </span>
              </button>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-sm text-gray-600">当前研究</div>
              <div className="font-medium text-gray-900">{currentSession.topic}</div>
              <div className="text-xs text-gray-500">
                状态: {currentSession.status === 'RUNNING' ? '进行中' : 
                       currentSession.status === 'COMPLETED' ? '已完成' : '等待中'}
              </div>
              {/* 显示当前查看模式 */}
              {viewMode === 'history' && viewingStageType && (
                <div className="text-xs text-indigo-600 font-medium bg-indigo-50 px-2 py-1 rounded">
                  正在查看历史: {getStageConfig(viewingStageType)?.name}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 分析流程 */}
      <div className="mb-8 relative z-10">
        <div className="flex items-center space-x-2 mb-3">
          <div className="section-title-icon secondary">
            <ChartIcon className="w-4 h-4 text-white" />
          </div>
          <h2 className="text-lg font-semibold text-gray-900">分析流程</h2>
        </div>
        <p className="text-sm text-gray-600 ml-8">AI驱动的多维度投资分析</p>
      </div>

      {/* 阶段列表 - 使用配置驱动渲染，新增点击交互 */}
      <div className="flex-1 space-y-5 relative z-10">
        {orderedStageConfigs.map((stageConfig, index) => {
          const status = getStageStatus(stageConfig.type);
          const progress = getStageProgress(stageConfig.type);
          const connectorStatus = getConnectorStatus(stageConfig.type, index, orderedStageConfigs.map(c => c.type));
          const isLast = index === orderedStageConfigs.length - 1;
          const displayStatus = getStageDisplayStatus(stageConfig.type);
          const clickable = isStageClickable(stageConfig.type);
          
          return (
            <div 
              key={stageConfig.type} 
              className={getStageItemClassName(stageConfig.type)}
              onClick={() => handleStageClick(stageConfig.type)}
              style={{ cursor: clickable ? 'pointer' : 'default' }}
            >
              {renderStageIcon(stageConfig.type)}
              <div className="flex-1 pt-1">
                <div className="flex items-center justify-between">
                  <h3 className={getStageTitleClassName(stageConfig.type)}>
                    {stageConfig.name}
                  </h3>
                  {/* 状态指示器 */}
                  {displayStatus === 'current-execution' && (
                    <div className="flex items-center space-x-1 text-blue-600">
                      <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium">执行中</span>
                    </div>
                  )}
                  {displayStatus === 'current-viewing' && (
                    <div className="flex items-center space-x-1 text-indigo-600">
                      <div className="w-2 h-2 bg-indigo-600 rounded-full"></div>
                      <span className="text-xs font-medium">查看中</span>
                    </div>
                  )}
                  {clickable && displayStatus === 'available' && (
                    <div className="text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity">
                      点击查看
                    </div>
                  )}
                </div>
                
                <p className="text-sm text-gray-500 mt-1 leading-relaxed">
                  {stageConfig.description}
                </p>
                
                {status === 'RUNNING' && progress > 0 && (
                  <div className="mt-2">
                    <div className="text-xs text-gray-500 mb-1">{progress}%</div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>
                )}
                
                {/* 连接线 */}
                {!isLast && (
                  <div className={`stage-connector ${connectorStatus}`}></div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
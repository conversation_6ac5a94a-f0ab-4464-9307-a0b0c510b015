/**
 * Research Actions 类型定义
 * 流式 Server Actions 的类型系统
 */

import { ResearchStageType, ExecutionStep, AIOutput } from '../domain/entities';

// 流式事件类型
export type StreamEventType = 
  | 'session_created'
  | 'stage_started' 
  | 'stage_completed'
  | 'step_started'
  | 'step_completed'
  | 'step_failed'
  | 'error_occurred';

// 流式事件基础接口
export interface StreamEvent {
  type: StreamEventType;
  sessionId: string;
  timestamp: string;
}

// 具体事件类型
export interface SessionCreatedEvent extends StreamEvent {
  type: 'session_created';
  session: {
    id: string;
    topic: string;
    status: 'RUNNING';
  };
}

export interface StageStartedEvent extends StreamEvent {
  type: 'stage_started';
  stageType: ResearchStageType;
  steps: ExecutionStep[];
}

export interface StageCompletedEvent extends StreamEvent {
  type: 'stage_completed';
  stageType: ResearchStageType;
  result: string;
  duration: number;
}

export interface StepStartedEvent extends StreamEvent {
  type: 'step_started';
  stepId: string;
  stageType: ResearchStageType;
  stepName: string;
  stepDescription?: string;
}

export interface StepCompletedEvent extends StreamEvent {
  type: 'step_completed';
  stepId: string;
  stageType: ResearchStageType;
  success: boolean;
  result: any;
  duration: number;
  aiOutputs?: AIOutput[];
}

export interface StepFailedEvent extends StreamEvent {
  type: 'step_failed';
  stepId: string;
  stageType: ResearchStageType;
  error: string;
}

export interface ErrorOccurredEvent extends StreamEvent {
  type: 'error_occurred';
  stageType?: ResearchStageType;
  error: string;
}

// 联合类型
export type ResearchStreamEvent = 
  | SessionCreatedEvent
  | StageStartedEvent
  | StageCompletedEvent
  | StepStartedEvent
  | StepCompletedEvent
  | StepFailedEvent
  | ErrorOccurredEvent;

// Server Action 参数类型
export interface StartResearchParams {
  topic: string;
}

export interface ExecuteStageParams {
  sessionId: string;
  stageType: ResearchStageType;
}

export interface CompleteStageParams {
  sessionId: string;
  stageType: ResearchStageType;
  userFeedback?: string;
}

// Server Action 返回类型
export type ResearchActionResult = AsyncGenerator<ResearchStreamEvent, void, unknown>;
'use server';

/**
 * 流式 Research Server Actions
 * 使用 async generator 实现真实的流式体验
 */

import { 
  ResearchStageType, 
  ResearchSessionFactory, 
  ExecutionStep,
  AIOutput,
  StepType,
  StepStatus
} from '../domain/entities';
import { 
  ResearchStreamEvent,
  StartResearchParams,
  ExecuteStageParams,
  CompleteStageParams,
  ResearchActionResult
} from './types';

// 模拟步骤执行器
class MockStepExecutor {
  async executeStep(step: ExecutionStep): Promise<{
    success: boolean;
    result: any;
    duration: number;
    executionLogs: AIOutput[];
  }> {
    // 模拟不同类型步骤的执行时间
    const baseDuration = this.getStepDuration(step.type);
    const actualDuration = baseDuration + Math.random() * 1000; // 添加随机性
    
    await this.delay(actualDuration);
    
    return {
      success: Math.random() > 0.05, // 95% 成功率
      result: this.generateStepResult(step),
      duration: Math.round(actualDuration),
      executionLogs: this.generateMockLogs(step)
    };
  }

  private getStepDuration(stepType: StepType): number {
    const durations: Record<StepType, number> = {
      'ANALYSIS': 2000,
      'SEARCH': 1500,
      'SYNTHESIS': 2500,
      'VALIDATION': 1800
    };
    return durations[stepType] || 2000;
  }

  private generateStepResult(step: ExecutionStep): any {
    const results: Record<StepType, any> = {
      'ANALYSIS': {
        insights: [`关于 ${step.name} 的分析结果`, '发现关键数据点', '识别趋势模式'],
        confidence: 0.85,
        dataPoints: Math.floor(Math.random() * 20) + 10
      },
      'SEARCH': {
        sources: [`来源 1: ${step.name} 相关数据`, `来源 2: 行业报告`, `来源 3: 财务数据`],
        totalResults: Math.floor(Math.random() * 100) + 50,
        relevanceScore: 0.9
      },
      'SYNTHESIS': {
        summary: `${step.name} 的综合分析显示了积极的市场前景`,
        keyFindings: ['发现1', '发现2', '发现3'],
        recommendations: ['建议1', '建议2']
      },
      'VALIDATION': {
        validationStatus: 'PASSED',
        confidence: 0.92,
        checkedCriteria: ['准确性', '完整性', '时效性']
      }
    };
    return results[step.type] || { result: `${step.name} 执行完成` };
  }

  private generateMockLogs(step: ExecutionStep): AIOutput[] {
    const logs: AIOutput[] = [];
    const logCount = Math.floor(Math.random() * 3) + 2; // 2-4 条日志
    
    for (let i = 0; i < logCount; i++) {
      logs.push({
        id: `log_${step.id}_${i}`,
        timestamp: new Date().toISOString(),
        type: i === 0 ? 'thinking' : (i === logCount - 1 ? 'result' : 'action'),
        content: this.generateLogContent(step, i, logCount - 1)
      });
    }
    
    return logs;
  }

  private generateLogContent(step: ExecutionStep, index: number, maxIndex: number): string {
    if (index === 0) {
      return `🤔 开始分析 ${step.name}，准备执行 ${step.type} 类型的任务...`;
    } else if (index === maxIndex) {
      return `✅ ${step.name} 分析完成，获得了有价值的洞察`;
    } else {
      const actions = [
        '🔍 正在搜索相关数据源...',
        '📊 正在处理和分析数据...',
        '🧠 正在应用 AI 模型进行深度分析...',
        '🔗 正在建立数据关联性...',
        '📈 正在生成分析图表...'
      ];
      return actions[Math.floor(Math.random() * actions.length)];
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 全局实例
const mockExecutor = new MockStepExecutor();

/**
 * 启动研究会话
 */
export async function* startResearchAction({ topic }: StartResearchParams): ResearchActionResult {
  const sessionId = `session_${Date.now()}`;
  
  // 创建会话
  const session = ResearchSessionFactory.create(topic);
  session.id = sessionId;
  session.status = 'RUNNING';
  
  yield {
    type: 'session_created',
    sessionId,
    timestamp: new Date().toISOString(),
    session: {
      id: sessionId,
      topic,
      status: 'RUNNING' as const
    }
  };
  
  // 自动开始第一个阶段
  yield* executeStageAction({ sessionId, stageType: 'FACT_VERIFICATION' });
}

/**
 * 执行特定阶段 - 流式返回进度
 */
export async function* executeStageAction({ sessionId, stageType }: ExecuteStageParams): ResearchActionResult {
  const steps = generateStageSteps(stageType);
  const stageStartTime = Date.now();
  
  try {
    // 1. 阶段开始
    yield {
      type: 'stage_started',
      sessionId,
      timestamp: new Date().toISOString(),
      stageType,
      steps
    };
    
    // 2. 逐步执行每个步骤
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      
      // 步骤开始
      yield {
        type: 'step_started',
        sessionId,
        timestamp: new Date().toISOString(),
        stepId: step.id,
        stageType,
        stepName: step.name,
        stepDescription: step.description
      };
      
      try {
        // 执行步骤
        const result = await mockExecutor.executeStep(step);
        
        // 步骤完成
        yield {
          type: 'step_completed',
          sessionId,
          timestamp: new Date().toISOString(),
          stepId: step.id,
          stageType,
          success: result.success,
          result: result.result,
          duration: result.duration,
          aiOutputs: result.executionLogs
        };
        
        if (!result.success) {
          throw new Error(`步骤 ${step.name} 执行失败`);
        }
        
      } catch (stepError) {
        const errorMessage = stepError instanceof Error ? stepError.message : '步骤执行失败';
        
        yield {
          type: 'step_failed',
          sessionId,
          timestamp: new Date().toISOString(),
          stepId: step.id,
          stageType,
          error: errorMessage
        };
        
        throw stepError;
      }
    }
    
    // 3. 阶段完成
    const stageDuration = Date.now() - stageStartTime;
    const stageResult = generateStageResult(stageType);
    
    yield {
      type: 'stage_completed',
      sessionId,
      timestamp: new Date().toISOString(),
      stageType,
      result: stageResult,
      duration: stageDuration
    };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '阶段执行失败';
    
    yield {
      type: 'error_occurred',
      sessionId,
      timestamp: new Date().toISOString(),
      stageType,
      error: errorMessage
    };
    
    throw error;
  }
}

/**
 * 完成阶段并启动下一阶段
 */
export async function* completeStageAction({ sessionId, stageType, userFeedback }: CompleteStageParams): ResearchActionResult {
  // 处理用户反馈（这里是 mock，实际可能需要存储）
  if (userFeedback) {
    console.log(`用户反馈 [${stageType}]:`, userFeedback);
  }
  
  // 获取下一个阶段
  const nextStageType = getNextStageType(stageType);
  
  if (nextStageType) {
    // 启动下一个阶段
    yield* executeStageAction({ sessionId, stageType: nextStageType });
  } else {
    // 所有阶段完成 - 使用 error_occurred 类型来通知会话完成
    yield {
      type: 'error_occurred', // 临时使用这个类型，后续可以添加 session_completed 类型
      sessionId,
      timestamp: new Date().toISOString(),
      error: 'SESSION_COMPLETED' // 特殊标记表示正常完成
    };
  }
}

// 辅助函数
function generateStageSteps(stageType: ResearchStageType): ExecutionStep[] {
  const stepConfigs = {
    'FACT_VERIFICATION': [
      { name: '数据收集', type: 'SEARCH' as StepType, description: '收集相关的基础数据和信息' },
      { name: '事实核查', type: 'VALIDATION' as StepType, description: '验证数据的准确性和可靠性' },
      { name: '信息整理', type: 'SYNTHESIS' as StepType, description: '整理和分类核查后的信息' }
    ],
    'IMPACT_SIMULATION': [
      { name: '影响因子识别', type: 'ANALYSIS' as StepType, description: '识别可能的影响因素' },
      { name: '模拟计算', type: 'ANALYSIS' as StepType, description: '运行影响模拟计算' },
      { name: '结果验证', type: 'VALIDATION' as StepType, description: '验证模拟结果的合理性' },
      { name: '风险评估', type: 'ANALYSIS' as StepType, description: '评估潜在风险和机会' }
    ],
    'THESIS_RECOMMENDATION': [
      { name: '论点构建', type: 'SYNTHESIS' as StepType, description: '基于前期分析构建投资论点' },
      { name: '建议生成', type: 'SYNTHESIS' as StepType, description: '生成具体的投资建议' },
      { name: '风险评估', type: 'ANALYSIS' as StepType, description: '评估建议的风险等级' }
    ],
    'FINAL_REPORT': [
      { name: '报告草拟', type: 'SYNTHESIS' as StepType, description: '整合所有分析结果生成报告' },
      { name: '质量检查', type: 'VALIDATION' as StepType, description: '检查报告的完整性和准确性' }
    ]
  };

  const configs = stepConfigs[stageType] || [];
  
  return configs.map((config, index) => ({
    id: `${stageType.toLowerCase()}_step_${index}`,
    name: config.name,
    type: config.type,
    status: 'pending' as StepStatus,
    description: config.description
  }));
}

function generateStageResult(stageType: ResearchStageType): string {
  const resultTemplates: Record<ResearchStageType, string> = {
    'FACT_VERIFICATION': '事实核查完成，确认了关键信息的准确性，为后续分析提供了可靠的数据基础。',
    'IMPACT_SIMULATION': '影响模拟分析显示积极的市场前景，识别了主要风险因素和投资机会。',
    'THESIS_RECOMMENDATION': '基于全面分析，给出谨慎乐观的投资建议，建议分批建仓并关注关键催化剂。',
    'FINAL_REPORT': '完整的投资研究报告已生成，包含详细的分析结论和风险提示。'
  };
  
  return resultTemplates[stageType] || '阶段执行完成';
}

function getNextStageType(currentStage: ResearchStageType): ResearchStageType | null {
  const stageOrder: ResearchStageType[] = [
    'FACT_VERIFICATION',
    'IMPACT_SIMULATION', 
    'THESIS_RECOMMENDATION',
    'FINAL_REPORT'
  ];

  const currentIndex = stageOrder.indexOf(currentStage);
  return currentIndex < stageOrder.length - 1 ? stageOrder[currentIndex + 1] : null;
}
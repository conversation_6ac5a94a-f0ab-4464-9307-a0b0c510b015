/**
 * 研究阶段配置系统
 * 
 * 目的: 将阶段元数据与业务逻辑和视觉表现完全解耦
 * 优势: 
 * - 配置驱动的阶段管理
 * - 图标和业务逻辑分离
 * - 便于扩展新阶段  
 * - 类型安全的配置管理
 * 
 * 使用场景:
 * - ResearchSidebar 阶段列表渲染
 * - 阶段进度追踪
 * - 图标状态管理
 * - 国际化扩展
 */

import { ResearchStageType } from '../domain/entities';
import { 
  ResearchStageIcons, 
  type ResearchStageIconType 
} from '../icons/research-stage-icons';

/**
 * 单个阶段的完整配置
 */
export interface StageConfig {
  /** 阶段类型 - 与domain实体保持一致 */
  type: ResearchStageType;
  
  /** 显示名称 */
  name: string;
  
  /** 详细描述 */
  description: string;
  
  /** 图标组件引用 */
  icon: ResearchStageIconType;
  
  /** 阶段顺序 - 用于排序和进度计算 */
  order: number;
  
  /** 预估耗时 (秒) - 用于进度预测 */
  estimatedDuration?: number;
  
  /** 阶段颜色主题 - 用于进度指示 */
  theme: {
    primary: string;
    secondary: string;
    accent: string;
  };
  
  /** 是否为关键阶段 - 影响整体进度权重 */
  isCritical: boolean;
}

/**
 * 研究阶段完整配置映射
 * 
 * 维护指南:
 * - 新增阶段: 添加新的配置项，确保order唯一
 * - 修改显示: 只需修改name/description，不影响业务逻辑
 * - 更换图标: 修改icon字段，引用icons库中的组件
 * - 调整顺序: 修改order值重新排序
 */
export const RESEARCH_STAGES_CONFIG: Record<ResearchStageType, StageConfig> = {
  FACT_VERIFICATION: {
    type: 'FACT_VERIFICATION',
    name: '事实核查验证',
    description: '基础事实验证与数据收集',
    icon: 'FACT_VERIFICATION',
    order: 1,
    estimatedDuration: 180, // 3分钟
    theme: {
      primary: '#3b82f6', // blue-500
      secondary: '#1d4ed8', // blue-700  
      accent: '#dbeafe',   // blue-100
    },
    isCritical: true,
  },
  
  IMPACT_SIMULATION: {
    type: 'IMPACT_SIMULATION',
    name: '影响模拟分析',
    description: '多维度影响评估与风险识别',
    icon: 'IMPACT_SIMULATION',
    order: 2,
    estimatedDuration: 240, // 4分钟
    theme: {
      primary: '#6366f1', // indigo-500
      secondary: '#4338ca', // indigo-700
      accent: '#e0e7ff',   // indigo-100
    },
    isCritical: true,
  },
  
  THESIS_RECOMMENDATION: {
    type: 'THESIS_RECOMMENDATION', 
    name: '投资策略建议',
    description: '专业投资策略制定',
    icon: 'THESIS_RECOMMENDATION',
    order: 3,
    estimatedDuration: 300, // 5分钟
    theme: {
      primary: '#8b5cf6', // violet-500
      secondary: '#7c3aed', // violet-600
      accent: '#ede9fe',   // violet-100
    },
    isCritical: true,
  },
  
  FINAL_REPORT: {
    type: 'FINAL_REPORT',
    name: '最终报告',
    description: '完整研究报告生成',
    icon: 'FINAL_REPORT',
    order: 4,
    estimatedDuration: 120, // 2分钟
    theme: {
      primary: '#10b981', // emerald-500
      secondary: '#047857', // emerald-700
      accent: '#d1fae5',   // emerald-100
    },
    isCritical: false, // 最终报告不是关键路径
  },
} as const;

/**
 * 工具函数 - 便于组件使用
 */

/**
 * 根据阶段类型获取配置
 */
export const getStageConfig = (stageType: ResearchStageType): StageConfig => {
  return RESEARCH_STAGES_CONFIG[stageType];
};

/**
 * 获取排序后的阶段配置列表
 */
export const getOrderedStageConfigs = (): StageConfig[] => {
  return Object.values(RESEARCH_STAGES_CONFIG)
    .sort((a, b) => a.order - b.order);
};

/**
 * 获取阶段对应的图标组件
 */
export const getStageIconComponent = (stageType: ResearchStageType) => {
  const config = getStageConfig(stageType);
  return ResearchStageIcons[config.icon];
};

/**
 * 计算总预估时长
 */
export const getTotalEstimatedDuration = (): number => {
  return Object.values(RESEARCH_STAGES_CONFIG)
    .reduce((total, config) => total + (config.estimatedDuration || 0), 0);
};

/**
 * 获取关键阶段列表
 */
export const getCriticalStages = (): StageConfig[] => {
  return Object.values(RESEARCH_STAGES_CONFIG)
    .filter(config => config.isCritical)
    .sort((a, b) => a.order - b.order);
};

/**
 * 根据阶段顺序获取下一个阶段
 */
export const getNextStage = (currentStage: ResearchStageType): StageConfig | null => {
  const currentConfig = getStageConfig(currentStage);
  const orderedConfigs = getOrderedStageConfigs();
  const currentIndex = orderedConfigs.findIndex(config => config.type === currentStage);
  
  return currentIndex < orderedConfigs.length - 1 
    ? orderedConfigs[currentIndex + 1] 
    : null;
};

/**
 * 根据阶段顺序获取上一个阶段
 */
export const getPreviousStage = (currentStage: ResearchStageType): StageConfig | null => {
  const orderedConfigs = getOrderedStageConfigs();
  const currentIndex = orderedConfigs.findIndex(config => config.type === currentStage);
  
  return currentIndex > 0 
    ? orderedConfigs[currentIndex - 1] 
    : null;
};

/**
 * 计算整体进度百分比
 */
export const calculateOverallProgress = (
  stageProgresses: Partial<Record<ResearchStageType, number>>
): number => {
  const configs = getOrderedStageConfigs();
  let totalWeight = 0;
  let completedWeight = 0;
  
  configs.forEach(config => {
    const weight = config.isCritical ? 2 : 1; // 关键阶段权重更高
    const progress = stageProgresses[config.type] || 0;
    
    totalWeight += weight;
    completedWeight += (progress / 100) * weight;
  });
  
  return totalWeight > 0 ? Math.round((completedWeight / totalWeight) * 100) : 0;
};

/**
 * 类型导出
 */
export type StageConfigType = typeof RESEARCH_STAGES_CONFIG;
export type StageConfigKey = keyof StageConfigType;

/**
 * 常量导出
 */
export const STAGE_TYPES = Object.keys(RESEARCH_STAGES_CONFIG) as ResearchStageType[];
export const STAGE_COUNT = STAGE_TYPES.length;

/**
 * 验证配置完整性的类型守卫
 */
export const isValidStageType = (type: string): type is ResearchStageType => {
  return type in RESEARCH_STAGES_CONFIG;
};
/**
 * 统一日志工具
 * 生产环境下可禁用调试日志
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogContext {
  component?: string;
  data?: unknown;
}

class Logger {
  private isDevelopment = process.env['NODE_ENV'] === 'development';

  private formatMessage(level: LogLevel, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const component = context?.component ? `[${context.component}]` : '';
    return `${timestamp} ${level.toUpperCase()} ${component} ${message}`;
  }

  debug(message: string, context?: LogContext) {
    if (this.isDevelopment) {
      console.debug(this.formatMessage('debug', message, context), context?.data);
    }
  }

  info(message: string, context?: LogContext) {
    console.info(this.formatMessage('info', message, context), context?.data);
  }

  warn(message: string, context?: LogContext) {
    console.warn(this.formatMessage('warn', message, context), context?.data);
  }

  error(message: string, context?: LogContext) {
    console.error(this.formatMessage('error', message, context), context?.data);
  }
}

export const logger = new Logger();
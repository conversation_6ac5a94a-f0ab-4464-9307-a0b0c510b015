/**
 * Research V2 Frontend Plugin 导出入口
 * 
 * 参考 demo-feature 模式，专注服务端组件和类型导出
 */

// 页面组件
export { ResearchV2Page } from './lib/pages/research-v2-page';

// 功能组件  
export { ResearchContainer } from './lib/components/research-container';
export { ResearchSidebar } from './lib/components/research-sidebar';

// 阶段组件
export * from './lib/components/stages';

// 共享组件 - 包含新增的图标系统
export { StageIcon } from './lib/components/shared/StageIcon';
export { StageIconList, useStageIcons } from './lib/components/shared/StageIconList';
export { ProgressRing } from './lib/components/shared/ProgressRing';
export { StageLayout } from './lib/components/shared/StageLayout';

// 状态管理
export { useResearchV2Store } from './lib/store/store';

// 配置系统 - SVG图标解耦后的配置驱动
export * from './lib/configs/research-stages.config';

// 图标系统 - 完全解耦的SVG图标库
export * from './lib/icons/research-stage-icons';

// DDD 架构类型和实体
export type {
  // 领域实体
  ResearchSession,
  ResearchStage as ResearchStageEntity,
  ExecutionStep,
  ResearchStageType,
  StageStatus,
  StepStatus,
  AIOutput
} from './lib/domain/entities';

// Store 类型
export type {
  ResearchV2Store,
  ResearchV2UIState,
  ResearchV2UIActions
} from './lib/store';

// 服务类型
export type {
  ResearchService,
  ResearchEvent
} from './lib/services';

// 页面级类型定义
export type {
  ResearchRequest,
  ResearchResult,
  AnalystTemplate,
  ResearchV2PageProps,
} from './lib/types';

// API Handlers - 供 Next.js 应用使用
export { startResearchHandler } from './lib/api/handlers/start-research.handler';
export { completeStageHandler } from './lib/api/handlers/complete-stage.handler';

// API 类型定义
export type {
  StartResearchRequest,
  CompleteStageRequest,
  ApiErrorResponse,
  ApiHandler,
  StreamingApiHandler
} from './lib/api/types/api.types';
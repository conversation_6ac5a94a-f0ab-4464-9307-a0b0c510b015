{"name": "@yai-investor-insight/shared-fe-kit", "version": "1.0.0", "description": "Shared frontend UI kit for YAI Investor Insight platform", "type": "module", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}}, "scripts": {"build": "tsc", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "keywords": ["typescript", "react", "ui-kit", "components", "investor-insight"], "author": "YAI Team", "license": "MIT", "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "tslib": "^2.6.0"}, "devDependencies": {"@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "publishConfig": {"access": "restricted"}}
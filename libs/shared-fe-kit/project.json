{"name": "shared-fe-kit", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared-fe-kit/src", "projectType": "library", "tags": ["scope:shared", "type:fe"], "// targets": "to see all targets run: nx show project shared-fe-kit --web", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared-fe-kit", "main": "libs/shared-fe-kit/src/index.ts", "tsConfig": "libs/shared-fe-kit/tsconfig.json", "assets": ["libs/shared-fe-kit/*.md"]}}}}
import { HTMLAttributes, forwardRef } from 'react';
import { cn } from '../utils/cn';

export interface ProgressProps extends HTMLAttributes<HTMLDivElement> {
  /** 进度值 (0-100) */
  value?: number;
  /** 最大值，默认 100 */
  max?: number;
  /** 进度条大小 */
  size?: 'sm' | 'md' | 'lg';
  /** 进度条颜色 */
  variant?: 'default' | 'success' | 'warning' | 'danger';
  /** 是否显示进度文本 */
  showValue?: boolean;
  /** 自定义进度文本 */
  valueText?: string;
}

export const Progress = forwardRef<HTMLDivElement, ProgressProps>(
  ({ 
    value = 0, 
    max = 100, 
    size = 'md',
    variant = 'default',
    showValue = false,
    valueText,
    className,
    ...props 
  }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
    
    const sizeClasses = {
      sm: 'h-1',
      md: 'h-2',
      lg: 'h-3'
    };
    
    const variantClasses = {
      default: 'bg-gradient-to-r from-blue-500 to-blue-600',
      success: 'bg-gradient-to-r from-green-500 to-green-600',
      warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
      danger: 'bg-gradient-to-r from-red-500 to-red-600'
    };

    return (
      <div
        ref={ref}
        className={cn('w-full space-y-2', className)}
        {...props}
      >
        {showValue && (
          <div className="flex justify-between text-sm text-gray-600">
            <span>进度</span>
            <span>{valueText || `${Math.round(percentage)}%`}</span>
          </div>
        )}
        
        <div
          className={cn(
            'w-full bg-gray-200 rounded-full overflow-hidden',
            sizeClasses[size]
          )}
          role="progressbar"
          aria-valuenow={value}
          aria-valuemax={max}
          aria-valuemin={0}
        >
          <div
            className={cn(
              'h-full rounded-full transition-all duration-500 ease-out',
              variantClasses[variant]
            )}
            style={{ width: `${percentage}%` }}
          />
        </div>
      </div>
    );
  }
);

export interface ProgressWithLabelProps extends ProgressProps {
  /** 进度标签 */
  label?: string;
}

export const ProgressWithLabel = forwardRef<HTMLDivElement, ProgressWithLabelProps>(
  ({ label, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('space-y-3', className)}
      >
        {label && (
          <div className="text-sm font-medium text-gray-900">
            {label}
          </div>
        )}
        <Progress showValue {...props} />
      </div>
    );
  }
);

Progress.displayName = 'Progress';
ProgressWithLabel.displayName = 'ProgressWithLabel';
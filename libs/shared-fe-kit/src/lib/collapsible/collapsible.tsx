'use client';

import { useState, ReactNode, HTMLAttributes, forwardRef, useEffect } from 'react';
import { cn } from '../utils/cn';

export interface CollapsibleProps extends HTMLAttributes<HTMLDivElement> {
  /** 触发器内容 */
  trigger: ReactNode;
  /** 折叠内容 */
  children: ReactNode;
  /** 默认是否展开 */
  defaultOpen?: boolean;
  /** 受控的展开状态 */
  open?: boolean;
  /** 展开状态变化回调 */
  onOpenChange?: (open: boolean) => void;
  /** 自定义触发器样式 */
  triggerClassName?: string;
  /** 自定义内容区域样式 */
  contentClassName?: string;
}

export const Collapsible = forwardRef<HTMLDivElement, CollapsibleProps>(
  ({ 
    trigger, 
    children, 
    defaultOpen = false, 
    open, 
    onOpenChange,
    triggerClassName,
    contentClassName,
    className,
    ...props 
  }, ref) => {
    const [internalOpen, setInternalOpen] = useState(defaultOpen);
    
    // 确定当前的展开状态
    const isControlled = open !== undefined;
    const isOpen = isControlled ? open : internalOpen;
    
    // 当 defaultOpen 改变时，更新内部状态（仅非受控模式）
    useEffect(() => {
      if (!isControlled) {
        setInternalOpen(defaultOpen);
      }
    }, [defaultOpen, isControlled]);
    
    const handleToggle = () => {
      const newOpen = !isOpen;
      if (isControlled) {
        onOpenChange?.(newOpen);
      } else {
        setInternalOpen(newOpen);
        onOpenChange?.(newOpen);
      }
    };

    return (
      <div
        ref={ref}
        className={cn('w-full', className)}
        data-state={isOpen ? 'open' : 'closed'}
        {...props}
      >
        <div
          role="button"
          tabIndex={0}
          onClick={handleToggle}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleToggle();
            }
          }}
          className={cn(
            'w-full cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md',
            triggerClassName
          )}
        >
          {trigger}
        </div>
        
        <div
          className={cn(
            'overflow-hidden transition-all duration-300 ease-in-out',
            isOpen ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0',
            contentClassName
          )}
        >
          <div>
            {children}
          </div>
        </div>
      </div>
    );
  }
);

export interface CollapsibleTriggerProps extends HTMLAttributes<HTMLDivElement> {
  /** 子元素 */
  children: ReactNode;
  /** 是否显示箭头指示器 */
  showArrow?: boolean;
  /** 是否展开（用于控制箭头方向） */
  isOpen?: boolean;
}

export const CollapsibleTrigger = forwardRef<HTMLDivElement, CollapsibleTriggerProps>(
  ({ children, showArrow = true, isOpen = false, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center justify-between p-4 hover:bg-gray-50 rounded-md transition-colors',
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-3">
          {showArrow && (
            <div className={cn(
              'transform transition-transform duration-200',
              isOpen ? 'rotate-90' : ''
            )}>
              <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          )}
          <div className="flex-1">
            {children}
          </div>
        </div>
        
        <div className="text-sm text-gray-500">
          {isOpen ? '收起' : '展开'}
        </div>
      </div>
    );
  }
);

export interface CollapsibleContentProps extends HTMLAttributes<HTMLDivElement> {
  /** 子元素 */
  children: ReactNode;
}

export const CollapsibleContent = forwardRef<HTMLDivElement, CollapsibleContentProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('px-4 pb-4', className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Collapsible.displayName = 'Collapsible';
CollapsibleTrigger.displayName = 'CollapsibleTrigger';
CollapsibleContent.displayName = 'CollapsibleContent';
# 更优雅的路由方案探讨 v2.0

## 当前问题

目前的解决方案需要在 `web-app` 中手动创建薄薄的 `route.ts` 文件：

```typescript
// apps/web-app/src/app/api/research/start/route.ts
import { startResearchHandler } from '@yai-investor-insight/research-v2h-fe';

export async function POST(request: Request): Promise<Response> {
  return startResearchHandler(request);
}
```

这种方式的缺点：

- 需要手动维护路由文件
- 每个新的 API 端点都需要在 web-app 中添加文件
- 路由结构分散在两个地方（libs 定义逻辑，web-app 定义路由）
- **依赖关系混乱**：web-app 需要直接依赖所有业务插件库

## 方案一：插件自动注册系统 ⭐⭐⭐⭐⭐

### 设计思路

创建一个插件系统，让 libs 可以声明自己的路由，通过 `shared-fe-core` 基础设施自动发现并注册。

### 架构分层

```
┌─────────────────┐
│    web-app      │ ← 只依赖基础设施
└─────────────────┘
         ↓
┌─────────────────┐
│ shared-fe-core  │ ← 通用路由处理器
└─────────────────┘
         ↑         ↑
┌─────────────┐ ┌──────────────┐
│research-v2h │ │user-account  │ ← 插件自注册
│    -fe      │ │    -fe       │
└─────────────┘ └──────────────┘
```

### 实现方案

#### 1. 在 shared-fe-core 中创建插件路由基础设施

```typescript
// libs/shared-fe-core/src/lib/plugin-router/types.ts
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
export type RouteHandler = (request: Request) => Promise<Response>;

export interface PluginRouteConfig {
  [path: string]: {
    [method in HttpMethod]?: RouteHandler;
  };
}

export interface PluginMetadata {
  name: string;
  version: string;
  routes: PluginRouteConfig;
}
```

```typescript
// libs/shared-fe-core/src/lib/plugin-router/registry.ts
import { PluginMetadata, RouteHandler } from './types';

export class PluginRegistry {
  private static plugins: Map<string, PluginMetadata> = new Map();
  private static routes: Map<string, Map<string, RouteHandler>> = new Map();

  /**
   * 插件自动注册路由
   */
  static register(plugin: PluginMetadata): void {
    console.log(`🔌 Registering plugin: ${plugin.name}@${plugin.version}`);
    
    // 注册插件信息
    this.plugins.set(plugin.name, plugin);
    
    // 注册路由处理器
    Object.entries(plugin.routes).forEach(([path, methods]) => {
      if (!this.routes.has(path)) {
        this.routes.set(path, new Map());
      }
      
      const routeHandlers = this.routes.get(path)!;
      Object.entries(methods).forEach(([method, handler]) => {
        if (routeHandlers.has(method)) {
          console.warn(`⚠️  Route conflict: ${method} ${path} already registered`);
        }
        routeHandlers.set(method, handler);
      });
    });
  }

  /**
   * 获取路由处理器
   */
  static getHandler(path: string, method: string): RouteHandler | null {
    return this.routes.get(path)?.get(method) || null;
  }

  /**
   * 获取所有注册的插件
   */
  static getPlugins(): PluginMetadata[] {
    return Array.from(this.plugins.values());
  }

  /**
   * 调试信息：打印所有路由
   */
  static debugRoutes(): void {
    console.log('📋 Registered routes:');
    this.routes.forEach((methods, path) => {
      methods.forEach((handler, method) => {
        console.log(`  ${method} ${path}`);
      });
    });
  }
}
```

```typescript
// libs/shared-fe-core/src/lib/plugin-router/handler.ts
import { PluginRegistry } from './registry';

/**
 * 通用插件路由处理器
 * 在 web-app 的动态路由中使用
 */
export function createPluginRouteHandler(method: string) {
  return async (
    request: Request,
    { params }: { params: { slug: string[] } }
  ): Promise<Response> => {
    const path = `/api/${params.slug.join('/')}`;
    const handler = PluginRegistry.getHandler(path, method);
    
    if (!handler) {
      return new Response(
        JSON.stringify({ 
          error: 'Not Found', 
          message: `No handler registered for ${method} ${path}` 
        }), 
        { 
          status: 404, 
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    try {
      return await handler(request);
    } catch (error) {
      console.error(`Error in plugin route ${method} ${path}:`, error);
      return new Response(
        JSON.stringify({ 
          error: 'Internal Server Error',
          message: error instanceof Error ? error.message : 'Unknown error'
        }), 
        { 
          status: 500, 
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  };
}
```

```typescript
// libs/shared-fe-core/src/lib/plugin-router/index.ts
export * from './types';
export * from './registry';
export * from './handler';
```

#### 2. 在业务插件中定义路由配置和自动注册

```typescript
// libs/research-v2h-fe/src/lib/api/routes.config.ts
import { PluginRouteConfig } from '@yai-investor-insight/shared-fe-core';
import { startResearchHandler, completeStageHandler } from './handlers';

export const researchV2HRoutes: PluginRouteConfig = {
  '/api/research/start': {
    POST: startResearchHandler,
  },
  '/api/research/complete-stage': {
    POST: completeStageHandler,
  },
};
```

```typescript
// libs/research-v2h-fe/src/lib/api/auto-register.ts
import { PluginRegistry } from '@yai-investor-insight/shared-fe-core';
import { researchV2HRoutes } from './routes.config';

// 插件自动注册
PluginRegistry.register({
  name: 'research-v2h-fe',
  version: '1.0.0',
  routes: researchV2HRoutes,
});

// 开发环境下打印路由信息
if (process.env.NODE_ENV === 'development') {
  PluginRegistry.debugRoutes();
}
```

```typescript
// libs/research-v2h-fe/src/index.ts
// ... 其他导出

// 重要：导入自动注册模块，触发插件注册
import './lib/api/auto-register';
```

#### 3. 在 web-app 中使用通用路由处理器

```typescript
// apps/web-app/src/app/api/[...slug]/route.ts
import { createPluginRouteHandler } from '@yai-investor-insight/shared-fe-core';

// 导入所有插件，触发自动注册
import '@yai-investor-insight/research-v2h-fe';
// import '@yai-investor-insight/user-account-fe'; // 未来的插件

// 创建各种 HTTP 方法的处理器
export const POST = createPluginRouteHandler('POST');
export const GET = createPluginRouteHandler('GET');
export const PUT = createPluginRouteHandler('PUT');
export const DELETE = createPluginRouteHandler('DELETE');
export const PATCH = createPluginRouteHandler('PATCH');
```

### 优势

- ✅ **完全解耦**：web-app 只依赖 shared-fe-core，不直接依赖业务插件
- ✅ **插件自包含**：每个插件库完全自包含，定义自己的路由
- ✅ **零维护成本**：新增插件无需修改 web-app 任何代码
- ✅ **类型安全**：TypeScript 编译时检查路由配置
- ✅ **支持多插件**：天然支持多个插件库同时工作
- ✅ **路由冲突检测**：自动检测并警告路由冲突
- ✅ **调试友好**：开发环境自动打印所有注册的路由
- ✅ **错误处理**：统一的错误处理和响应格式

### 劣势

- ❌ **运行时解析**：路由查找有轻微的运行时开销
- ❌ **导入顺序依赖**：需要确保插件在使用前被导入
- ❌ **调试复杂度**：相比直接的 route.ts 文件，调试路径稍复杂

---

## 方案二：构建时代码生成 ⭐⭐⭐⭐

### 设计思路

使用构建工具自动生成 `route.ts` 文件，开发者无需手动维护。

### 实现方案

#### 1. 在 libs 中定义路由元数据

```typescript
// libs/research-v2h-fe/src/lib/api/routes.meta.ts
export const routesMetadata = [
  {
    path: '/api/research/start',
    methods: ['POST'],
    handler: 'startResearchHandler',
  },
  {
    path: '/api/research/complete-stage', 
    methods: ['POST'],
    handler: 'completeStageHandler',
  },
] as const;
```

#### 2. 创建代码生成脚本

```typescript
// tools/route-generator.ts
import { routesMetadata } from '@yai-investor-insight/research-v2h-fe/routes.meta';

function generateRouteFile(route: typeof routesMetadata[0]) {
  return `
// 自动生成的文件，请勿手动修改
import { ${route.handler} } from '@yai-investor-insight/research-v2h-fe';

${route.methods.map(method => `
export async function ${method}(request: Request): Promise<Response> {
  return ${route.handler}(request);
}
`).join('')}
  `.trim();
}

// 为每个路由生成对应的 route.ts 文件
```

#### 3. 集成到构建流程

```json
// package.json
{
  "scripts": {
    "prebuild": "node tools/route-generator.ts",
    "dev": "npm run prebuild && next dev"
  }
}
```

### 优势

- ✅ **开发者友好**：无需手动维护路由文件
- ✅ **调试清晰**：生成真实的 route.ts 文件，路径明确
- ✅ **构建时检查**：错误在构建阶段发现，而非运行时
- ✅ **Next.js 原生**：完全符合 Next.js 最佳实践
- ✅ **性能最优**：没有运行时路由查找开销

### 劣势

- ❌ **构建复杂性**：需要额外的构建步骤和工具
- ❌ **版本控制**：生成的文件是否纳入版本控制需要考虑
- ❌ **依赖问题**：仍然存在 web-app 直接依赖业务插件的问题

---

## 方案三：Next.js Middleware 拦截 ⭐⭐⭐

### 设计思路

使用 Next.js middleware 拦截 API 请求，直接调用 libs 中的 handler。

### 实现方案

```typescript
// apps/web-app/src/middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { researchV2HRoutes } from '@yai-investor-insight/research-v2h-fe';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 检查是否是插件路由
  const handler = researchV2HRoutes[pathname]?.[request.method];
  
  if (handler) {
    // 直接调用 handler
    return handler(request);
  }
  
  // 继续正常处理
  return NextResponse.next();
}

export const config = {
  matcher: '/api/:path*',
};
```

### 优势

- ✅ **极简方案**：无需任何 route.ts 文件
- ✅ **集中管理**：所有路由在 middleware 中统一处理
- ✅ **高性能**：中间件级别的请求拦截和处理

### 劣势

- ❌ **运行时限制**：Middleware 只能在 Edge Runtime 中运行
- ❌ **API 限制**：无法使用某些 Node.js API
- ❌ **调试困难**：错误追踪和调试相对复杂
- ❌ **流式响应限制**：不支持 ReadableStream（致命缺陷！）
- ❌ **依赖问题**：仍然需要在 middleware 中导入所有插件

---

## 方案四：自定义服务器 + Express ⭐⭐

### 设计思路

不使用 Next.js API Routes，而是使用自定义 Express 服务器。

### 实现方案

```typescript
// apps/web-app/server.js
import express from 'express';
import next from 'next';
import { researchV2HRoutes } from '@yai-investor-insight/research-v2h-fe';

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  const server = express();
  
  // 自动注册插件路由
  Object.entries(researchV2HRoutes).forEach(([path, methods]) => {
    Object.entries(methods).forEach(([method, handler]) => {
      server[method.toLowerCase()](path, handler);
    });
  });
  
  // 其他请求交给 Next.js
  server.all('*', (req, res) => handle(req, res));
  
  server.listen(3000);
});
```

### 优势

- ✅ **完全灵活**：无任何 Next.js 限制，完全控制路由
- ✅ **生态丰富**：支持所有 Express 生态的中间件
- ✅ **功能完整**：无 API Routes 的各种限制

### 劣势

- ❌ **架构偏离**：不再是标准的 Next.js 应用
- ❌ **部署复杂**：需要自定义服务器部署配置
- ❌ **性能损失**：失去 Next.js API Routes 的优化
- ❌ **维护成本**：需要维护额外的服务器代码

---

## 推荐方案

### 🥇 首选：方案一（插件自动注册系统）

**关键优势**：

1. **依赖关系清晰**：
   ```
   web-app → shared-fe-core （只依赖基础设施）
   business-libs → shared-fe-core （插件依赖基础设施）
   ```

2. **真正的插件化**：每个业务库完全自包含，自动注册路由

3. **零维护成本**：新增插件无需修改 web-app 任何代码

4. **架构可扩展**：支持无限数量的插件库

**适用场景**：

- ✅ 大型 Monorepo 项目
- ✅ 多团队协作开发
- ✅ 频繁新增功能模块
- ✅ 需要插件化架构

### 🥈 备选：方案二（构建时代码生成）

**关键优势**：

1. **性能最优**：零运行时开销
2. **调试友好**：生成真实的 route.ts 文件
3. **Next.js 原生**：完全符合框架最佳实践

**适用场景**：

- ✅ 中小型项目
- ✅ 路由相对稳定
- ✅ 对性能要求极高
- ✅ 团队偏好传统 Next.js 开发方式

### ❌ 不推荐：方案三（Middleware）

**致命缺陷**：不支持流式响应，与我们当前的 SSE 需求冲突

### ❌ 不推荐：方案四（自定义服务器）

**主要问题**：偏离 Next.js 生态，增加维护复杂性

## 实施建议

**强烈推荐采用方案一**，原因：

1. **解决了依赖关系问题**：通过 shared-fe-core 作为中介，避免 web-app 直接依赖业务插件
2. **架构演进友好**：为未来的微前端、插件生态做好准备
3. **开发体验优秀**：插件开发者只需关注自己的业务逻辑
4. **运维成本低**：插件的增减不影响主应用

**下一步行动**：

1. 在 `shared-fe-core` 中实现插件路由基础设施
2. 重构现有的 `research-v2h-fe` 为插件模式
3. 验证方案可行性
4. 制定插件开发规范

## 技术细节补充

### 导入顺序问题解决方案

为了确保插件在使用前被注册，可以在 `shared-fe-core` 中提供延迟加载机制：

```typescript
// libs/shared-fe-core/src/lib/plugin-router/lazy-registry.ts
export class LazyPluginRegistry {
  private static pendingRoutes: Array<() => Promise<void>> = [];
  private static initialized = false;

  static registerLazy(loader: () => Promise<PluginMetadata>) {
    this.pendingRoutes.push(async () => {
      const plugin = await loader();
      PluginRegistry.register(plugin);
    });
  }

  static async initialize() {
    if (this.initialized) return;
    await Promise.all(this.pendingRoutes.map(loader => loader()));
    this.initialized = true;
  }
}
```

### 路由冲突处理策略

1. **警告模式**：检测冲突但允许覆盖（默认）
2. **严格模式**：检测到冲突时抛出错误
3. **命名空间模式**：自动为插件路由添加前缀

### 性能优化考虑

1. **路由缓存**：将路由映射缓存在 Map 中，O(1) 查找
2. **懒加载**：只在需要时加载和注册插件
3. **预编译**：开发模式下可以预编译路由表

这个完善后的方案解决了你提到的依赖关系问题，你觉得如何？
# 更优雅的路由方案探讨

## 当前问题

目前的解决方案需要在 `web-app` 中手动创建薄薄的 `route.ts` 文件：

```typescript
// apps/web-app/src/app/api/research/start/route.ts
import { startResearchHandler } from '@yai-investor-insight/research-v2h-fe';

export async function POST(request: Request): Promise<Response> {
  return startResearchHandler(request);
}
```

这种方式的缺点：
- 需要手动维护路由文件
- 每个新的 API 端点都需要在 web-app 中添加文件
- 路由结构分散在两个地方（libs 定义逻辑，web-app 定义路由）

## 方案一：插件自动注册系统 ⭐⭐⭐⭐⭐

### 设计思路
创建一个插件系统，让 libs 可以声明自己的路由，web-app 自动发现并注册。

### 实现方案

#### 1. 在 libs 中定义路由配置

```typescript
// libs/research-v2h-fe/src/lib/api/routes.config.ts
import { startResearchHandler, completeStageHandler } from './handlers';

export const researchV2HRoutes = {
  '/api/research/start': {
    POST: startResearchHandler,
  },
  '/api/research/complete-stage': {
    POST: completeStageHandler,
  },
} as const;

export type ResearchV2HRoutes = typeof researchV2HRoutes;
```

#### 2. 在 web-app 中创建通用路由处理器

```typescript
// apps/web-app/src/lib/plugin-router.ts
import { researchV2HRoutes } from '@yai-investor-insight/research-v2h-fe';
// 未来可以导入更多插件路由
// import { userAccountRoutes } from '@yai-investor-insight/user-account-fe';

const allRoutes = {
  ...researchV2HRoutes,
  // ...userAccountRoutes,
};

export function createPluginRoute(path: string) {
  return {
    POST: async (request: Request) => {
      const handler = allRoutes[path]?.POST;
      if (!handler) {
        return new Response('Not Found', { status: 404 });
      }
      return handler(request);
    },
    // 可以支持其他 HTTP 方法
  };
}
```

#### 3. 使用动态路由

```typescript
// apps/web-app/src/app/api/[...slug]/route.ts
import { createPluginRoute } from '../../../lib/plugin-router';

export async function POST(
  request: Request,
  { params }: { params: { slug: string[] } }
): Promise<Response> {
  const path = `/api/${params.slug.join('/')}`;
  const handler = createPluginRoute(path);
  return handler.POST(request);
}

// 支持其他 HTTP 方法
export const GET = /* 类似实现 */;
export const PUT = /* 类似实现 */;
```

### 优势
- ✅ libs 完全自包含，定义自己的路由
- ✅ web-app 只需要一个通用的动态路由文件
- ✅ 新增 API 无需修改 web-app
- ✅ 类型安全，编译时检查路由配置
- ✅ 支持多个插件库

### 劣势
- ❌ 需要设计路由冲突处理机制
- ❌ 调试时路由路径不如直接文件明确

---

## 方案二：构建时代码生成 ⭐⭐⭐⭐

### 设计思路
使用构建工具自动生成 `route.ts` 文件，开发者无需手动维护。

### 实现方案

#### 1. 在 libs 中定义路由元数据

```typescript
// libs/research-v2h-fe/src/lib/api/routes.meta.ts
export const routesMetadata = [
  {
    path: '/api/research/start',
    methods: ['POST'],
    handler: 'startResearchHandler',
  },
  {
    path: '/api/research/complete-stage', 
    methods: ['POST'],
    handler: 'completeStageHandler',
  },
] as const;
```

#### 2. 创建代码生成脚本

```typescript
// tools/route-generator.ts
import { routesMetadata } from '@yai-investor-insight/research-v2h-fe/routes.meta';

function generateRouteFile(route: typeof routesMetadata[0]) {
  return `
// 自动生成的文件，请勿手动修改
import { ${route.handler} } from '@yai-investor-insight/research-v2h-fe';

${route.methods.map(method => `
export async function ${method}(request: Request): Promise<Response> {
  return ${route.handler}(request);
}
`).join('')}
  `.trim();
}

// 为每个路由生成对应的 route.ts 文件
```

#### 3. 集成到构建流程

```json
// package.json
{
  "scripts": {
    "prebuild": "node tools/route-generator.ts",
    "dev": "npm run prebuild && next dev"
  }
}
```

### 优势
- ✅ 开发者无需手动维护路由文件
- ✅ 路由结构清晰，调试友好
- ✅ 构建时检查，错误早期发现
- ✅ 完全符合 Next.js 最佳实践

### 劣势
- ❌ 需要额外的构建步骤
- ❌ 生成的文件需要版本控制考虑

---

## 方案三：Next.js Middleware 拦截 ⭐⭐⭐

### 设计思路
使用 Next.js middleware 拦截 API 请求，直接调用 libs 中的 handler。

### 实现方案

```typescript
// apps/web-app/src/middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { researchV2HRoutes } from '@yai-investor-insight/research-v2h-fe';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 检查是否是插件路由
  const handler = researchV2HRoutes[pathname]?.[request.method];
  
  if (handler) {
    // 直接调用 handler
    return handler(request);
  }
  
  // 继续正常处理
  return NextResponse.next();
}

export const config = {
  matcher: '/api/:path*',
};
```

### 优势
- ✅ 无需任何 route.ts 文件
- ✅ 集中的路由管理
- ✅ 高性能，中间件级别处理

### 劣势
- ❌ Middleware 运行环境限制（Edge Runtime）
- ❌ 无法使用某些 Node.js API
- ❌ 调试和错误追踪较复杂
- ❌ 不支持流式响应（重要限制！）

---

## 方案四：自定义服务器 + Express ⭐⭐

### 设计思路
不使用 Next.js API Routes，而是使用自定义 Express 服务器。

### 实现方案

```typescript
// apps/web-app/server.js
import express from 'express';
import next from 'next';
import { researchV2HRoutes } from '@yai-investor-insight/research-v2h-fe';

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  const server = express();
  
  // 自动注册插件路由
  Object.entries(researchV2HRoutes).forEach(([path, methods]) => {
    Object.entries(methods).forEach(([method, handler]) => {
      server[method.toLowerCase()](path, handler);
    });
  });
  
  // 其他请求交给 Next.js
  server.all('*', (req, res) => handle(req, res));
  
  server.listen(3000);
});
```

### 优势
- ✅ 完全灵活的路由控制
- ✅ 支持所有 Express 中间件
- ✅ 无 Next.js API Routes 限制

### 劣势
- ❌ 偏离 Next.js 标准架构
- ❌ 增加部署复杂性
- ❌ 失去 Next.js API Routes 的优化

---

## 推荐方案

### 🥇 首选：方案一（插件自动注册系统）

**理由**：
1. **架构优雅**：libs 完全自包含，路由配置就在业务逻辑旁边
2. **扩展性强**：支持多个插件库，未来可以轻松添加新的功能模块
3. **类型安全**：TypeScript 编译时检查，减少运行时错误
4. **维护性好**：新增 API 只需在 libs 中配置，无需修改 web-app

### 🥈 备选：方案二（构建时代码生成）

**理由**：
1. **Next.js 原生**：完全符合 Next.js 最佳实践
2. **调试友好**：生成真实的 route.ts 文件，便于调试
3. **性能最优**：没有运行时路由查找开销

## 实施建议

建议采用 **方案一**，因为它在优雅性、可维护性和扩展性之间达到了最佳平衡。

你觉得哪个方案更符合你的期望？我们可以进一步讨论实施细节。
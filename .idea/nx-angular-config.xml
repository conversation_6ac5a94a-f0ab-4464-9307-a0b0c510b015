<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="NxAngularConfigService" workspaceLocation="file://$PROJECT_DIR$/nx.json">
    <projects>
      <project name=".vercel" file="file://$PROJECT_DIR$/.vercel/project.json" />
      <project name="api-client" file="file://$PROJECT_DIR$/libs/api-client/project.json" />
      <project name="api-server" file="file://$PROJECT_DIR$/apps/api-server/project.json" />
      <project name="demo-feature-fe" file="file://$PROJECT_DIR$/libs/demo-feature-fe/project.json" />
      <project name="research-v2-fe" file="file://$PROJECT_DIR$/libs/research-v2-fe/project.json" />
      <project name="research-v2b-fe" file="file://$PROJECT_DIR$/libs/research-v2b-fe/project.json" />
      <project name="research-v2h-fe" file="file://$PROJECT_DIR$/libs/research-v2h-fe/project.json" />
      <project name="shared-bs-core" file="file://$PROJECT_DIR$/libs/shared-bs-core/project.json" />
      <project name="shared-bs-llm" file="file://$PROJECT_DIR$/libs/shared-bs-llm/project.json" />
      <project name="shared-fe-api-client" file="file://$PROJECT_DIR$/libs/shared-fe-api-client/project.json" />
      <project name="shared-fe-core" file="file://$PROJECT_DIR$/libs/shared-fe-core/project.json" />
      <project name="shared-fe-kit" file="file://$PROJECT_DIR$/libs/shared-fe-kit/project.json" />
      <project name="shared-fe-types" file="file://$PROJECT_DIR$/libs/shared-fe-types/project.json" />
      <project name="shared-types" file="file://$PROJECT_DIR$/libs/shared-types/project.json" />
      <project name="user-account-fe" file="file://$PROJECT_DIR$/libs/user-account-fe/project.json" />
      <project name="web-app" file="file://$PROJECT_DIR$/apps/web-app/project.json" />
      <project name="yai-investor-insight" file="file://$PROJECT_DIR$/project.json" />
    </projects>
  </component>
</project>
#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录的项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 切换到项目根目录
cd "$PROJECT_ROOT"

log_info "开始构建 AI 投资洞察平台..."
log_info "项目根目录: $PROJECT_ROOT"

# 记录开始时间
START_TIME=$(date +%s)

# 1. 构建前端项目
log_info "正在构建前端项目..."
if ! "$SCRIPT_DIR/build-frontend.sh"; then
    log_error "前端构建失败"
    exit 1
fi
log_success "前端构建完成"

# 2. 构建后端项目
log_info "正在构建后端项目..."
if ! "$SCRIPT_DIR/build-backend.sh"; then
    log_error "后端构建失败"
    exit 1
fi
log_success "后端构建完成"

# 3. 验证构建结果
log_info "正在验证构建结果..."

FAILED_BUILDS=()

# 检查前端构建产物
if [ ! -d "apps/web-app/.next" ] && [ ! -d "apps/web-app/dist" ]; then
    FAILED_BUILDS+=("web-app")
fi

# 检查后端构建产物
if [ ! -d "apps/api-server/dist" ]; then
    FAILED_BUILDS+=("api-server")
fi

# 检查共享库构建产物
# shared-types 已删除，跳过检查

# 4. 输出构建结果
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "======================================"
echo "           构建结果汇总"
echo "======================================"

if [ ${#FAILED_BUILDS[@]} -eq 0 ]; then
    log_success "所有项目构建成功！"
    echo ""
    log_info "构建产物位置："
    log_info "  - 前端应用: apps/web-app/.next/"
    log_info "  - 后端应用: apps/api-server/dist/"
    log_info "  - 共享库: libs/*/dist/"
    echo ""
    log_success "总耗时: ${DURATION}秒"
    echo ""
    log_info "独立构建脚本："
    log_info "  - 仅构建前端: ./scripts/build-frontend.sh"
    log_info "  - 仅构建后端: ./scripts/build-backend.sh"
    echo ""
    log_info "启动命令："
    log_info "  - 启动全部服务: pnpm nx dev:all"
    log_info "  - 单独启动前端: pnpm nx dev web-app"
    log_info "  - 单独启动后端: cd apps/api-server && source .venv/bin/activate && python main.py"
else
    log_error "以下项目构建失败:"
    for failed in "${FAILED_BUILDS[@]}"; do
        log_error "  - $failed"
    done
    echo ""
    log_warn "总耗时: ${DURATION}秒"
    exit 1
fi

echo "======================================"
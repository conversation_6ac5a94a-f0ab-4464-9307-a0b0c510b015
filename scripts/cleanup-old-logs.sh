#!/bin/bash

# 清理旧的日志文件和目录，迁移到新的统一日志目录

echo "🧹 清理旧日志文件和目录..."

# 移除 api-server 本地 logs 目录（如果存在）
if [ -d "apps/api-server/logs" ]; then
    echo "📁 发现旧的 api-server logs 目录，正在备份并清理..."
    
    # 如果有日志文件，先备份到新位置
    if [ -f "apps/api-server/logs/investor-insight.log" ]; then
        echo "💾 备份旧日志文件到 logs/api-server.old.log"
        mkdir -p logs
        cp "apps/api-server/logs/investor-insight.log" "logs/api-server.old.log"
    fi
    
    # 删除旧目录
    rm -rf "apps/api-server/logs"
    echo "✅ 已清理 apps/api-server/logs 目录"
fi

# 移除 webapp 可能存在的旧 logs 目录
if [ -d "logs/webapp" ]; then
    echo "📁 发现旧的 webapp logs 子目录，正在合并到根 logs 目录..."
    
    # 备份可能存在的日志文件
    if [ -f "logs/webapp/webapp.browser.log" ]; then
mv "logs/webapp/webapp.browser.log" "logs/webapp.browser.old.log"
echo "💾 已备份 webapp.browser.log"
    fi
    
    if [ -f "logs/webapp/webapp.server.log" ]; then
        mv "logs/webapp/webapp.server.log" "logs/webapp.server.old.log"
        echo "💾 已备份 webapp.server.log"
    fi
    
    # 删除子目录
    rm -rf "logs/webapp"
    echo "✅ 已清理 logs/webapp 子目录"
fi

# 确保 logs 目录存在
mkdir -p logs

echo "🎉 日志目录清理完成！"
echo "📂 统一日志目录结构："
echo "   logs/"
echo "   ├── webapp.browser.log    # WebApp 浏览器日志"
echo "   ├── webapp.server.log    # WebApp 服务端日志"
echo "   ├── api-server.log       # API Server 日志"
echo "   └── *.old.log           # 备份的旧日志文件"
echo ""
echo "🚀 现在可以启动服务，日志将统一输出到 logs/ 目录"
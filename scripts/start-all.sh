#!/bin/bash

# AI投资洞察平台 - 简化开发启动脚本
# 直接使用 pnpm 和 Nx 命令

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🚀 AI投资洞察平台 - 快速启动${NC}"
echo "=================================="

# 检查是否在项目根目录
if [ ! -f "package.json" ] || [ ! -f "nx.json" ]; then
    echo -e "${YELLOW}⚠️  请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}📦 安装依赖...${NC}"
    pnpm install
fi

# 构建共享库（如果需要）
# shared-types 和 api-client 已删除，跳过检查

# 检查后端虚拟环境
if [ ! -d "apps/api-server/.venv" ]; then
    echo -e "${BLUE}🐍 设置 Python 环境...${NC}"
    cd apps/api-server
    if command -v uv &> /dev/null; then
        uv venv .venv
        source .venv/bin/activate
        uv pip install -e .[dev]
    else
        python3 -m venv .venv
        source .venv/bin/activate
        pip install -e .[dev]
    fi
    cd ../..
fi

echo -e "${GREEN}✅ 准备完成，启动开发服务...${NC}"
echo ""

# 检查启动模式
if [ "${1:-}" = "--foreground" ]; then
    echo -e "${BLUE}📍 服务地址:${NC}"
    echo "   前端: http://localhost:4200 (或 3000)"
    echo "   后端: http://localhost:8000"
    echo "   API文档: http://localhost:8000/docs"
    echo ""
    echo -e "${YELLOW}💡 提示: 按 Ctrl+C 停止所有服务${NC}"
    echo ""
    
    # 前台模式启动
    exec pnpm run dev
else
    echo -e "${BLUE}🔄 以后台模式启动服务...${NC}"
    
    # 后台模式启动
    nohup pnpm run dev > logs/start-all.log 2>&1 &
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 15
    
    # 检查服务状态
    backend_running=false
    frontend_running=false
    
    if curl -s http://localhost:8000/health > /dev/null 2>&1 || curl -s http://localhost:8000/ > /dev/null 2>&1; then
        backend_running=true
    fi
    
    if curl -s http://localhost:4200/ > /dev/null 2>&1 || curl -s http://localhost:3000/ > /dev/null 2>&1; then
        frontend_running=true
    fi
    
    echo ""
    echo -e "${GREEN}🚀 服务启动完成${NC}"
    echo "=================================="
    
    if [ "$backend_running" = true ]; then
        echo "✅ 后端服务: http://localhost:8000"
        echo "📋 API文档: http://localhost:8000/docs"
    else
        echo "⏳ 后端服务: 仍在启动中..."
    fi
    
    if [ "$frontend_running" = true ]; then
        if curl -s http://localhost:4200/ > /dev/null 2>&1; then
            echo "✅ 前端服务: http://localhost:4200"
        else
            echo "✅ 前端服务: http://localhost:3000"
        fi
    else
        echo "⏳ 前端服务: 仍在启动中..."
    fi
    
    echo ""
    echo -e "${BLUE}📋 管理命令:${NC}"
    echo "   查看状态: ./scripts/check-services.sh"
    echo "   查看日志: ./scripts/logs-all.sh"
    echo "   停止服务: ./scripts/stop-all.sh"
    echo ""
    
    if [ "$backend_running" = false ] || [ "$frontend_running" = false ]; then
        echo -e "${YELLOW}💡 部分服务可能仍在启动中，请稍后检查状态${NC}"
    fi
fi

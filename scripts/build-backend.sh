#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录的项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 切换到项目根目录
cd "$PROJECT_ROOT"

log_info "开始构建后端项目..."
log_info "项目根目录: $PROJECT_ROOT"

# 记录开始时间
START_TIME=$(date +%s)

# 1. 环境检查
log_info "正在检查后端构建环境..."

# 检查 Python
if ! command -v python3 &> /dev/null; then
    log_error "Python3 未安装或不在 PATH 中"
    exit 1
fi
PYTHON_VERSION=$(python3 --version)
log_success "Python 版本: $PYTHON_VERSION"

# 检查 uv
if ! command -v uv &> /dev/null; then
    log_error "uv 未安装或不在 PATH 中，请安装: pip install uv"
    exit 1
fi
UV_VERSION=$(uv --version)
log_success "uv 版本: $UV_VERSION"

# 2. 清理后端构建产物
log_info "正在清理后端构建产物..."

# 清理后端相关的构建产物
find apps/api-server -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
find apps/api-server -name "*.egg-info" -type d -exec rm -rf {} + 2>/dev/null || true
find apps/api-server -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

log_success "后端构建产物清理完成"

# 3. 安装后端依赖
log_info "正在安装后端依赖..."

# 检查是否在Docker环境中
if [ "$DOCKER_BUILD" = "1" ]; then
    log_info "检测到Docker环境，跳过虚拟环境创建"

    # Docker环境：先安装本地依赖库
    log_info "正在安装本地依赖库..."

    # 定义需要安装的本地Python库的顺序（按依赖关系排序）
    PYTHON_LIBS=(
        "shared-bs-core"
        "shared-bs-llm"
        "demo-feature-bs"
        "research-v2-bs"
        "research-v2b-bs"
        "research-v2h-bs"
    )

    # 按顺序安装本地Python库
    for lib_name in "${PYTHON_LIBS[@]}"; do
        lib_dir="libs/$lib_name"
        if [ -d "$lib_dir" ] && [ -f "${lib_dir}/pyproject.toml" ]; then
            log_info "安装本地库: $lib_name"
            cd "$lib_dir"
            uv pip install -e . --index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
            cd "$PROJECT_ROOT"
            log_success "本地库 $lib_name 安装完成"
        else
            log_warn "跳过不存在的库: $lib_name"
        fi
    done

    # 然后安装api-server
    cd apps/api-server
    uv pip install -e . --index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
else
    # 本地环境：使用虚拟环境
    cd apps/api-server

    # 检查虚拟环境
    if [ ! -d ".venv" ]; then
        log_info "创建 Python 虚拟环境..."
        uv venv --python python3
    fi

    # 使用 uv sync 安装依赖（包括 workspace 依赖）
    log_info "使用 uv sync 安装依赖..."
    uv sync --all-extras
fi

log_success "后端依赖安装完成"

# 4. 构建后端应用（Python 包）
log_info "构建后端应用..."

# 确保在正确的目录
if [ "$DOCKER_BUILD" = "1" ]; then
    cd "$PROJECT_ROOT/apps/api-server"
else
    # 本地环境已经在 apps/api-server 目录中
    :
fi

# 使用 hatchling 构建 Python 包
if [ "$DOCKER_BUILD" = "1" ]; then
    uv pip install build
    python -m build
else
    # 本地环境使用 uv 安装 build 包
    uv pip install build
    .venv/bin/python -m build
fi
log_success "后端应用构建完成"

# 返回项目根目录
cd "$PROJECT_ROOT"

# 5. 验证后端构建结果
log_info "正在验证后端构建结果..."

FAILED_BUILDS=()

# 检查后端构建产物
if [ ! -d "apps/api-server/dist" ]; then
    FAILED_BUILDS+=("api-server")
fi

# 6. 输出后端构建结果
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "======================================"
echo "         后端构建结果汇总"
echo "======================================"

if [ ${#FAILED_BUILDS[@]} -eq 0 ]; then
    log_success "后端项目构建成功！"
    echo ""
    log_info "构建产物位置："
    log_info "  - 后端应用: apps/api-server/dist/"
    log_info "  - Python 虚拟环境: apps/api-server/.venv/"
    echo ""
    log_success "总耗时: ${DURATION}秒"
    echo ""
    log_info "启动命令："
    log_info "  - 启动后端开发服务器: cd apps/api-server && source .venv/bin/activate && python main.py"
    log_info "  - 安装构建的包: cd apps/api-server && source .venv/bin/activate && pip install dist/*.whl"
else
    log_error "以下后端项目构建失败:"
    for failed in "${FAILED_BUILDS[@]}"; do
        log_error "  - $failed"
    done
    echo ""
    log_warn "总耗时: ${DURATION}秒"
    exit 1
fi

echo "======================================"